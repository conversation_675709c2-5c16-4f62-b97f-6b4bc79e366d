Dependencies for Project 'arm-asm', Target 'Target 1': (DO NOT MODIFY !)
F (.\start.s)(0x676F8783)(--gdwarf-2 -mthumb-interwork -IC:/Keil_v5/ARM/CMSIS/Include -IC:/Keil_v5/ARM/INC/Samsung -I"C:/Program Files (x86)/CodeSourcery/Sourcery_CodeBench_Lite_for_ARM_GNU_Linux/arm-none-linux-gnueabi/include" -I"C:/Program Files (x86)/CodeSourcery/Sourcery_CodeBench_Lite_for_ARM_GNU_Linux/lib/gcc/arm-none-linux-gnueabi/4.6.1/include" -I"C:/Program Files (x86)/CodeSourcery/Sourcery_CodeBench_Lite_for_ARM_GNU_Linux/arm-none-linux-gnueabi/include/c++/4.6.1" -I"C:/Program Files (x86)/CodeSourcery/Sourcery_CodeBench_Lite_for_ARM_GNU_Linux/arm-none-linux-gnueabi/include/c++/4.6.1/arm-none-linux-gnueabi" --defsym __UVISION_VERSION=527 --defsym __GCC=1 --defsym __GCC_VERSION=461

--MD ./objects/start.d -o ./objects/start.o)
