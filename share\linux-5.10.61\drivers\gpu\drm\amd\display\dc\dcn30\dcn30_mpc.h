/* Copyright 2020 Advanced Micro Devices, Inc.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a
 * copy of this software and associated documentation files (the "Software"),
 * to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
 * THE COPYRIGHT HOLDER(S) OR AUTHOR(S) BE LIABLE FOR ANY CLAIM, DAMAGES OR
 * OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 * OTHER DEALINGS IN THE SOFTWARE.
 *
 * Authors: <AUTHORS>
 *
 */

#ifndef __DC_MPCC_DCN30_H__
#define __DC_MPCC_DCN30_H__

#include "dcn20/dcn20_mpc.h"

#define MAX_RMU 3

#define TO_DCN30_MPC(mpc_base) \
	container_of(mpc_base, struct dcn30_mpc, base)

#ifdef SRII_MPC_RMU
#undef SRII_MPC_RMU

#define SRII_MPC_RMU(reg_name, block, id)\
	.RMU##_##reg_name[id] = BASE(mm ## block ## id ## _ ## reg_name ## _BASE_IDX) + \
					mm ## block ## id ## _ ## reg_name

#endif


#define MPC_REG_LIST_DCN3_0(inst)\
	MPC_COMMON_REG_LIST_DCN1_0(inst),\
	SRII(MPCC_TOP_GAIN, MPCC, inst),\
	SRII(MPCC_BOT_GAIN_INSIDE, MPCC, inst),\
	SRII(MPCC_BOT_GAIN_OUTSIDE, MPCC, inst),\
	SRII(MPCC_MEM_PWR_CTRL, MPCC, inst),\
	SRII(MPCC_OGAM_LUT_INDEX, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_LUT_DATA, MPCC_OGAM, inst), \
	SRII(MPCC_GAMUT_REMAP_COEF_FORMAT, MPCC_OGAM, inst),\
	SRII(MPCC_GAMUT_REMAP_MODE, MPCC_OGAM, inst),\
	SRII(MPC_GAMUT_REMAP_C11_C12_A, MPCC_OGAM, inst),\
	SRII(MPC_GAMUT_REMAP_C33_C34_A, MPCC_OGAM, inst),\
	SRII(MPC_GAMUT_REMAP_C11_C12_B, MPCC_OGAM, inst),\
	SRII(MPC_GAMUT_REMAP_C33_C34_B, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMA_START_CNTL_B, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMA_START_CNTL_G, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMA_START_CNTL_R, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMA_START_SLOPE_CNTL_B, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMA_START_SLOPE_CNTL_G, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMA_START_SLOPE_CNTL_R, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMA_END_CNTL1_B, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMA_END_CNTL2_B, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMA_END_CNTL1_G, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMA_END_CNTL2_G, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMA_END_CNTL1_R, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMA_END_CNTL2_R, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMA_REGION_0_1, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMA_REGION_32_33, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMA_OFFSET_B, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMA_OFFSET_G, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMA_OFFSET_R, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMA_START_BASE_CNTL_B, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMA_START_BASE_CNTL_G, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMA_START_BASE_CNTL_R, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMB_START_CNTL_B, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMB_START_CNTL_G, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMB_START_CNTL_R, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMB_START_SLOPE_CNTL_B, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMB_START_SLOPE_CNTL_G, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMB_START_SLOPE_CNTL_R, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMB_END_CNTL1_B, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMB_END_CNTL2_B, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMB_END_CNTL1_G, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMB_END_CNTL2_G, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMB_END_CNTL1_R, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMB_END_CNTL2_R, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMB_REGION_0_1, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMB_REGION_32_33, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMB_OFFSET_B, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMB_OFFSET_G, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMB_OFFSET_R, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMB_START_BASE_CNTL_B, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMB_START_BASE_CNTL_G, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_RAMB_START_BASE_CNTL_R, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_CONTROL, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_LUT_CONTROL, MPCC_OGAM, inst)

/*
	SRII(MPCC_OGAM_LUT_RAM_CONTROL, MPCC_OGAM, inst),\
	SRII(MPCC_OGAM_MODE, MPCC_OGAM, inst)
*/

#define MPC_OUT_MUX_REG_LIST_DCN3_0(inst) \
	MPC_OUT_MUX_COMMON_REG_LIST_DCN1_0(inst),\
	SRII(CSC_MODE, MPC_OUT, inst),\
	SRII(CSC_C11_C12_A, MPC_OUT, inst),\
	SRII(CSC_C33_C34_A, MPC_OUT, inst),\
	SRII(CSC_C11_C12_B, MPC_OUT, inst),\
	SRII(CSC_C33_C34_B, MPC_OUT, inst),\
	SRII(DENORM_CONTROL, MPC_OUT, inst),\
	SRII(DENORM_CLAMP_G_Y, MPC_OUT, inst),\
	SRII(DENORM_CLAMP_B_CB, MPC_OUT, inst), \
	SR(MPC_OUT_CSC_COEF_FORMAT)

#define MPC_RMU_GLOBAL_REG_LIST_DCN3AG \
	SR(MPC_RMU_CONTROL),\
	SR(MPC_RMU_MEM_PWR_CTRL)

#define MPC_RMU_REG_LIST_DCN3AG(inst) \
	SRII(SHAPER_CONTROL, MPC_RMU, inst),\
	SRII(SHAPER_OFFSET_R, MPC_RMU, inst),\
	SRII(SHAPER_OFFSET_G, MPC_RMU, inst),\
	SRII(SHAPER_OFFSET_B, MPC_RMU, inst),\
	SRII(SHAPER_SCALE_R, MPC_RMU, inst),\
	SRII(SHAPER_SCALE_G_B, MPC_RMU, inst),\
	SRII(SHAPER_LUT_INDEX, MPC_RMU, inst),\
	SRII(SHAPER_LUT_DATA, MPC_RMU, inst),\
	SRII(SHAPER_LUT_WRITE_EN_MASK, MPC_RMU, inst),\
	SRII(SHAPER_RAMA_START_CNTL_B, MPC_RMU, inst),\
	SRII(SHAPER_RAMA_START_CNTL_G, MPC_RMU, inst),\
	SRII(SHAPER_RAMA_START_CNTL_R, MPC_RMU, inst),\
	SRII(SHAPER_RAMA_END_CNTL_B, MPC_RMU, inst),\
	SRII(SHAPER_RAMA_END_CNTL_G, MPC_RMU, inst),\
	SRII(SHAPER_RAMA_END_CNTL_R, MPC_RMU, inst),\
	SRII(SHAPER_RAMA_REGION_0_1, MPC_RMU, inst),\
	SRII(SHAPER_RAMA_REGION_2_3, MPC_RMU, inst),\
	SRII(SHAPER_RAMA_REGION_4_5, MPC_RMU, inst),\
	SRII(SHAPER_RAMA_REGION_6_7, MPC_RMU, inst),\
	SRII(SHAPER_RAMA_REGION_8_9, MPC_RMU, inst),\
	SRII(SHAPER_RAMA_REGION_10_11, MPC_RMU, inst),\
	SRII(SHAPER_RAMA_REGION_12_13, MPC_RMU, inst),\
	SRII(SHAPER_RAMA_REGION_14_15, MPC_RMU, inst),\
	SRII(SHAPER_RAMA_REGION_16_17, MPC_RMU, inst),\
	SRII(SHAPER_RAMA_REGION_18_19, MPC_RMU, inst),\
	SRII(SHAPER_RAMA_REGION_20_21, MPC_RMU, inst),\
	SRII(SHAPER_RAMA_REGION_22_23, MPC_RMU, inst),\
	SRII(SHAPER_RAMA_REGION_24_25, MPC_RMU, inst),\
	SRII(SHAPER_RAMA_REGION_26_27, MPC_RMU, inst),\
	SRII(SHAPER_RAMA_REGION_28_29, MPC_RMU, inst),\
	SRII(SHAPER_RAMA_REGION_30_31, MPC_RMU, inst),\
	SRII(SHAPER_RAMA_REGION_32_33, MPC_RMU, inst),\
	SRII(SHAPER_RAMB_START_CNTL_B, MPC_RMU, inst),\
	SRII(SHAPER_RAMB_START_CNTL_G, MPC_RMU, inst),\
	SRII(SHAPER_RAMB_START_CNTL_R, MPC_RMU, inst),\
	SRII(SHAPER_RAMB_END_CNTL_B, MPC_RMU, inst),\
	SRII(SHAPER_RAMB_END_CNTL_G, MPC_RMU, inst),\
	SRII(SHAPER_RAMB_END_CNTL_R, MPC_RMU, inst),\
	SRII(SHAPER_RAMB_REGION_0_1, MPC_RMU, inst),\
	SRII(SHAPER_RAMB_REGION_2_3, MPC_RMU, inst),\
	SRII(SHAPER_RAMB_REGION_4_5, MPC_RMU, inst),\
	SRII(SHAPER_RAMB_REGION_6_7, MPC_RMU, inst),\
	SRII(SHAPER_RAMB_REGION_8_9, MPC_RMU, inst),\
	SRII(SHAPER_RAMB_REGION_10_11, MPC_RMU, inst),\
	SRII(SHAPER_RAMB_REGION_12_13, MPC_RMU, inst),\
	SRII(SHAPER_RAMB_REGION_14_15, MPC_RMU, inst),\
	SRII(SHAPER_RAMB_REGION_16_17, MPC_RMU, inst),\
	SRII(SHAPER_RAMB_REGION_18_19, MPC_RMU, inst),\
	SRII(SHAPER_RAMB_REGION_20_21, MPC_RMU, inst),\
	SRII(SHAPER_RAMB_REGION_22_23, MPC_RMU, inst),\
	SRII(SHAPER_RAMB_REGION_24_25, MPC_RMU, inst),\
	SRII(SHAPER_RAMB_REGION_26_27, MPC_RMU, inst),\
	SRII(SHAPER_RAMB_REGION_28_29, MPC_RMU, inst),\
	SRII(SHAPER_RAMB_REGION_30_31, MPC_RMU, inst),\
	SRII(SHAPER_RAMB_REGION_32_33, MPC_RMU, inst),\
	SRII_MPC_RMU(3DLUT_MODE, MPC_RMU, inst),\
	SRII_MPC_RMU(3DLUT_INDEX, MPC_RMU, inst),\
	SRII_MPC_RMU(3DLUT_DATA, MPC_RMU, inst),\
	SRII_MPC_RMU(3DLUT_DATA_30BIT, MPC_RMU, inst),\
	SRII_MPC_RMU(3DLUT_READ_WRITE_CONTROL, MPC_RMU, inst),\
	SRII_MPC_RMU(3DLUT_OUT_NORM_FACTOR, MPC_RMU, inst),\
	SRII_MPC_RMU(3DLUT_OUT_OFFSET_R, MPC_RMU, inst),\
	SRII_MPC_RMU(3DLUT_OUT_OFFSET_G, MPC_RMU, inst),\
	SRII_MPC_RMU(3DLUT_OUT_OFFSET_B, MPC_RMU, inst)


#define MPC_DWB_MUX_REG_LIST_DCN3_0(inst) \
	SRII_DWB(DWB_MUX, MUX, MPC_DWB, inst)

#define MPC_REG_VARIABLE_LIST_DCN3_0 \
	MPC_REG_VARIABLE_LIST_DCN2_0 \
	uint32_t DWB_MUX[MAX_DWB]; \
	uint32_t MPCC_GAMUT_REMAP_COEF_FORMAT[MAX_MPCC]; \
	uint32_t MPCC_GAMUT_REMAP_MODE[MAX_MPCC]; \
	uint32_t MPC_GAMUT_REMAP_C11_C12_A[MAX_MPCC]; \
	uint32_t MPC_GAMUT_REMAP_C33_C34_A[MAX_MPCC]; \
	uint32_t MPC_GAMUT_REMAP_C11_C12_B[MAX_MPCC]; \
	uint32_t MPC_GAMUT_REMAP_C33_C34_B[MAX_MPCC]; \
	uint32_t MPC_RMU_CONTROL; \
	uint32_t MPC_RMU_MEM_PWR_CTRL; \
	uint32_t SHAPER_CONTROL[MAX_RMU]; \
	uint32_t SHAPER_OFFSET_R[MAX_RMU]; \
	uint32_t SHAPER_OFFSET_G[MAX_RMU]; \
	uint32_t SHAPER_OFFSET_B[MAX_RMU]; \
	uint32_t SHAPER_SCALE_R[MAX_RMU]; \
	uint32_t SHAPER_SCALE_G_B[MAX_RMU]; \
	uint32_t SHAPER_LUT_INDEX[MAX_RMU]; \
	uint32_t SHAPER_LUT_DATA[MAX_RMU]; \
	uint32_t SHAPER_LUT_WRITE_EN_MASK[MAX_RMU]; \
	uint32_t SHAPER_RAMA_START_CNTL_B[MAX_RMU]; \
	uint32_t SHAPER_RAMA_START_CNTL_G[MAX_RMU]; \
	uint32_t SHAPER_RAMA_START_CNTL_R[MAX_RMU]; \
	uint32_t SHAPER_RAMA_END_CNTL_B[MAX_RMU]; \
	uint32_t SHAPER_RAMA_END_CNTL_G[MAX_RMU]; \
	uint32_t SHAPER_RAMA_END_CNTL_R[MAX_RMU]; \
	uint32_t SHAPER_RAMA_REGION_0_1[MAX_RMU]; \
	uint32_t SHAPER_RAMA_REGION_2_3[MAX_RMU]; \
	uint32_t SHAPER_RAMA_REGION_4_5[MAX_RMU]; \
	uint32_t SHAPER_RAMA_REGION_6_7[MAX_RMU]; \
	uint32_t SHAPER_RAMA_REGION_8_9[MAX_RMU]; \
	uint32_t SHAPER_RAMA_REGION_10_11[MAX_RMU]; \
	uint32_t SHAPER_RAMA_REGION_12_13[MAX_RMU]; \
	uint32_t SHAPER_RAMA_REGION_14_15[MAX_RMU]; \
	uint32_t SHAPER_RAMA_REGION_16_17[MAX_RMU]; \
	uint32_t SHAPER_RAMA_REGION_18_19[MAX_RMU]; \
	uint32_t SHAPER_RAMA_REGION_20_21[MAX_RMU]; \
	uint32_t SHAPER_RAMA_REGION_22_23[MAX_RMU]; \
	uint32_t SHAPER_RAMA_REGION_24_25[MAX_RMU]; \
	uint32_t SHAPER_RAMA_REGION_26_27[MAX_RMU]; \
	uint32_t SHAPER_RAMA_REGION_28_29[MAX_RMU]; \
	uint32_t SHAPER_RAMA_REGION_30_31[MAX_RMU]; \
	uint32_t SHAPER_RAMA_REGION_32_33[MAX_RMU]; \
	uint32_t MPCC_OGAM_RAMA_START_SLOPE_CNTL_B[MAX_MPCC]; \
	uint32_t MPCC_OGAM_RAMA_START_SLOPE_CNTL_G[MAX_MPCC]; \
	uint32_t MPCC_OGAM_RAMA_START_SLOPE_CNTL_R[MAX_MPCC]; \
	uint32_t MPCC_OGAM_RAMA_OFFSET_B[MAX_MPCC]; \
	uint32_t MPCC_OGAM_RAMA_OFFSET_G[MAX_MPCC]; \
	uint32_t MPCC_OGAM_RAMA_OFFSET_R[MAX_MPCC]; \
	uint32_t MPCC_OGAM_RAMA_START_BASE_CNTL_B[MAX_MPCC]; \
	uint32_t MPCC_OGAM_RAMA_START_BASE_CNTL_G[MAX_MPCC]; \
	uint32_t MPCC_OGAM_RAMA_START_BASE_CNTL_R[MAX_MPCC];\
	uint32_t SHAPER_RAMB_START_CNTL_B[MAX_RMU]; \
	uint32_t SHAPER_RAMB_START_CNTL_G[MAX_RMU]; \
	uint32_t SHAPER_RAMB_START_CNTL_R[MAX_RMU]; \
	uint32_t SHAPER_RAMB_END_CNTL_B[MAX_RMU]; \
	uint32_t SHAPER_RAMB_END_CNTL_G[MAX_RMU]; \
	uint32_t SHAPER_RAMB_END_CNTL_R[MAX_RMU]; \
	uint32_t SHAPER_RAMB_REGION_0_1[MAX_RMU]; \
	uint32_t SHAPER_RAMB_REGION_2_3[MAX_RMU]; \
	uint32_t SHAPER_RAMB_REGION_4_5[MAX_RMU]; \
	uint32_t SHAPER_RAMB_REGION_6_7[MAX_RMU]; \
	uint32_t SHAPER_RAMB_REGION_8_9[MAX_RMU]; \
	uint32_t SHAPER_RAMB_REGION_10_11[MAX_RMU]; \
	uint32_t SHAPER_RAMB_REGION_12_13[MAX_RMU]; \
	uint32_t SHAPER_RAMB_REGION_14_15[MAX_RMU]; \
	uint32_t SHAPER_RAMB_REGION_16_17[MAX_RMU]; \
	uint32_t SHAPER_RAMB_REGION_18_19[MAX_RMU]; \
	uint32_t SHAPER_RAMB_REGION_20_21[MAX_RMU]; \
	uint32_t SHAPER_RAMB_REGION_22_23[MAX_RMU]; \
	uint32_t SHAPER_RAMB_REGION_24_25[MAX_RMU]; \
	uint32_t SHAPER_RAMB_REGION_26_27[MAX_RMU]; \
	uint32_t SHAPER_RAMB_REGION_28_29[MAX_RMU]; \
	uint32_t SHAPER_RAMB_REGION_30_31[MAX_RMU]; \
	uint32_t SHAPER_RAMB_REGION_32_33[MAX_RMU]; \
	uint32_t RMU_3DLUT_MODE[MAX_RMU]; \
	uint32_t RMU_3DLUT_INDEX[MAX_RMU]; \
	uint32_t RMU_3DLUT_DATA[MAX_RMU]; \
	uint32_t RMU_3DLUT_DATA_30BIT[MAX_RMU]; \
	uint32_t RMU_3DLUT_READ_WRITE_CONTROL[MAX_RMU]; \
	uint32_t RMU_3DLUT_OUT_NORM_FACTOR[MAX_RMU]; \
	uint32_t RMU_3DLUT_OUT_OFFSET_R[MAX_RMU]; \
	uint32_t RMU_3DLUT_OUT_OFFSET_G[MAX_RMU]; \
	uint32_t RMU_3DLUT_OUT_OFFSET_B[MAX_RMU]; \
	uint32_t MPCC_OGAM_RAMB_START_SLOPE_CNTL_B[MAX_MPCC]; \
	uint32_t MPCC_OGAM_RAMB_START_SLOPE_CNTL_G[MAX_MPCC]; \
	uint32_t MPCC_OGAM_RAMB_START_SLOPE_CNTL_R[MAX_MPCC]; \
	uint32_t MPCC_OGAM_CONTROL[MAX_MPCC]; \
	uint32_t MPCC_OGAM_LUT_CONTROL[MAX_MPCC]; \
	uint32_t MPCC_OGAM_RAMB_OFFSET_B[MAX_MPCC]; \
	uint32_t MPCC_OGAM_RAMB_OFFSET_G[MAX_MPCC]; \
	uint32_t MPCC_OGAM_RAMB_OFFSET_R[MAX_MPCC]; \
	uint32_t MPCC_OGAM_RAMB_START_BASE_CNTL_B[MAX_MPCC]; \
	uint32_t MPCC_OGAM_RAMB_START_BASE_CNTL_G[MAX_MPCC]; \
	uint32_t MPCC_OGAM_RAMB_START_BASE_CNTL_R[MAX_MPCC]; \
	uint32_t MPC_OUT_CSC_COEF_FORMAT

#define MPC_COMMON_MASK_SH_LIST_DCN3_0(mask_sh) \
	MPC_COMMON_MASK_SH_LIST_DCN1_0(mask_sh),\
	SF(MPCC0_MPCC_CONTROL, MPCC_BG_BPC, mask_sh),\
	SF(MPCC0_MPCC_CONTROL, MPCC_BOT_GAIN_MODE, mask_sh),\
	SF(MPCC0_MPCC_TOP_GAIN, MPCC_TOP_GAIN, mask_sh),\
	SF(MPCC0_MPCC_BOT_GAIN_INSIDE, MPCC_BOT_GAIN_INSIDE, mask_sh),\
	SF(MPCC0_MPCC_BOT_GAIN_OUTSIDE, MPCC_BOT_GAIN_OUTSIDE, mask_sh),\
	SF(MPC_OUT0_CSC_MODE, MPC_OCSC_MODE, mask_sh),\
	SF(MPC_OUT0_CSC_C11_C12_A, MPC_OCSC_C11_A, mask_sh),\
	SF(MPC_OUT0_CSC_C11_C12_A, MPC_OCSC_C12_A, mask_sh),\
	SF(MPCC0_MPCC_STATUS, MPCC_DISABLED, mask_sh),\
	SF(MPCC0_MPCC_MEM_PWR_CTRL, MPCC_OGAM_MEM_PWR_FORCE, mask_sh),\
	SF(MPCC0_MPCC_MEM_PWR_CTRL, MPCC_OGAM_MEM_PWR_DIS, mask_sh),\
	SF(MPC_OUT0_DENORM_CONTROL, MPC_OUT_DENORM_MODE, mask_sh),\
	SF(MPC_OUT0_DENORM_CONTROL, MPC_OUT_DENORM_CLAMP_MAX_R_CR, mask_sh),\
	SF(MPC_OUT0_DENORM_CONTROL, MPC_OUT_DENORM_CLAMP_MIN_R_CR, mask_sh),\
	SF(MPC_OUT0_DENORM_CLAMP_G_Y, MPC_OUT_DENORM_CLAMP_MAX_G_Y, mask_sh),\
	SF(MPC_OUT0_DENORM_CLAMP_G_Y, MPC_OUT_DENORM_CLAMP_MIN_G_Y, mask_sh),\
	SF(MPC_OUT0_DENORM_CLAMP_B_CB, MPC_OUT_DENORM_CLAMP_MAX_B_CB, mask_sh),\
	SF(MPC_OUT0_DENORM_CLAMP_B_CB, MPC_OUT_DENORM_CLAMP_MIN_B_CB, mask_sh),\
	SF(MPCC_OGAM0_MPCC_GAMUT_REMAP_MODE, MPCC_GAMUT_REMAP_MODE, mask_sh),\
	SF(MPCC_OGAM0_MPCC_GAMUT_REMAP_MODE, MPCC_GAMUT_REMAP_MODE_CURRENT, mask_sh),\
	SF(MPCC_OGAM0_MPCC_GAMUT_REMAP_COEF_FORMAT, MPCC_GAMUT_REMAP_COEF_FORMAT, mask_sh),\
	SF(MPCC_OGAM0_MPC_GAMUT_REMAP_C11_C12_A, MPCC_GAMUT_REMAP_C11_A, mask_sh),\
	SF(MPCC_OGAM0_MPC_GAMUT_REMAP_C11_C12_A, MPCC_GAMUT_REMAP_C12_A, mask_sh),\
	SF(MPC_DWB0_MUX, MPC_DWB0_MUX, mask_sh),\
	SF(MPC_DWB0_MUX, MPC_DWB0_MUX_STATUS, mask_sh),\
	SF(MPC_OUT0_MUX, MPC_OUT_RATE_CONTROL, mask_sh),\
	SF(MPC_OUT0_MUX, MPC_OUT_RATE_CONTROL_DISABLE, mask_sh),\
	SF(MPC_OUT0_MUX, MPC_OUT_FLOW_CONTROL_MODE, mask_sh),\
	SF(MPC_OUT0_MUX, MPC_OUT_FLOW_CONTROL_COUNT, mask_sh), \
	SF(MPC_RMU_CONTROL, MPC_RMU0_MUX, mask_sh), \
	SF(MPC_RMU_CONTROL, MPC_RMU1_MUX, mask_sh), \
	SF(MPC_RMU_CONTROL, MPC_RMU0_MUX_STATUS, mask_sh), \
	SF(MPC_RMU_CONTROL, MPC_RMU1_MUX_STATUS, mask_sh), \
	SF(MPCC_OGAM0_MPCC_OGAM_RAMA_REGION_0_1, MPCC_OGAM_RAMA_EXP_REGION0_LUT_OFFSET, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_RAMA_REGION_0_1, MPCC_OGAM_RAMA_EXP_REGION0_NUM_SEGMENTS, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_RAMA_REGION_0_1, MPCC_OGAM_RAMA_EXP_REGION1_LUT_OFFSET, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_RAMA_REGION_0_1, MPCC_OGAM_RAMA_EXP_REGION1_NUM_SEGMENTS, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_RAMA_END_CNTL2_B, MPCC_OGAM_RAMA_EXP_REGION_END_SLOPE_B, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_RAMA_END_CNTL2_B, MPCC_OGAM_RAMA_EXP_REGION_END_B, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_RAMA_END_CNTL1_B, MPCC_OGAM_RAMA_EXP_REGION_END_BASE_B, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_RAMA_START_SLOPE_CNTL_B, MPCC_OGAM_RAMA_EXP_REGION_START_SLOPE_B, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_RAMA_START_BASE_CNTL_B, MPCC_OGAM_RAMA_EXP_REGION_START_BASE_B, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_RAMA_START_CNTL_B, MPCC_OGAM_RAMA_EXP_REGION_START_B, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_RAMA_START_CNTL_B, MPCC_OGAM_RAMA_EXP_REGION_START_SEGMENT_B, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_RAMA_OFFSET_B, MPCC_OGAM_RAMA_OFFSET_B, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_RAMA_OFFSET_G, MPCC_OGAM_RAMA_OFFSET_G, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_RAMA_OFFSET_R, MPCC_OGAM_RAMA_OFFSET_R, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_LUT_INDEX, MPCC_OGAM_LUT_INDEX, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_CONTROL, MPCC_OGAM_MODE, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_CONTROL, MPCC_OGAM_SELECT, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_CONTROL, MPCC_OGAM_PWL_DISABLE, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_CONTROL, MPCC_OGAM_MODE_CURRENT, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_CONTROL, MPCC_OGAM_SELECT_CURRENT, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_LUT_CONTROL, MPCC_OGAM_LUT_WRITE_COLOR_MASK, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_LUT_CONTROL, MPCC_OGAM_LUT_READ_COLOR_SEL, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_LUT_CONTROL, MPCC_OGAM_LUT_READ_DBG, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_LUT_CONTROL, MPCC_OGAM_LUT_HOST_SEL, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_LUT_CONTROL, MPCC_OGAM_LUT_CONFIG_MODE, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_LUT_CONTROL, MPCC_OGAM_LUT_STATUS, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_LUT_DATA, MPCC_OGAM_LUT_DATA, mask_sh),\
	SF(MPC_RMU0_3DLUT_MODE, MPC_RMU_3DLUT_MODE, mask_sh),\
	SF(MPC_RMU0_3DLUT_MODE, MPC_RMU_3DLUT_SIZE, mask_sh),\
	SF(MPC_RMU0_3DLUT_MODE, MPC_RMU_3DLUT_MODE_CURRENT, mask_sh),\
	SF(MPC_RMU0_3DLUT_READ_WRITE_CONTROL, MPC_RMU_3DLUT_WRITE_EN_MASK, mask_sh),\
	SF(MPC_RMU0_3DLUT_READ_WRITE_CONTROL, MPC_RMU_3DLUT_RAM_SEL, mask_sh),\
	SF(MPC_RMU0_3DLUT_READ_WRITE_CONTROL, MPC_RMU_3DLUT_30BIT_EN, mask_sh),\
	SF(MPC_RMU0_3DLUT_READ_WRITE_CONTROL, MPC_RMU_3DLUT_CONFIG_STATUS, mask_sh),\
	SF(MPC_RMU0_3DLUT_READ_WRITE_CONTROL, MPC_RMU_3DLUT_READ_SEL, mask_sh),\
	SF(MPC_RMU0_3DLUT_INDEX, MPC_RMU_3DLUT_INDEX, mask_sh),\
	SF(MPC_RMU0_3DLUT_DATA, MPC_RMU_3DLUT_DATA0, mask_sh),\
	SF(MPC_RMU0_3DLUT_DATA, MPC_RMU_3DLUT_DATA1, mask_sh),\
	SF(MPC_RMU0_3DLUT_DATA_30BIT, MPC_RMU_3DLUT_DATA_30BIT, mask_sh),\
	SF(MPC_RMU0_SHAPER_CONTROL, MPC_RMU_SHAPER_LUT_MODE, mask_sh),\
	SF(MPC_RMU0_SHAPER_CONTROL, MPC_RMU_SHAPER_LUT_MODE_CURRENT, mask_sh),\
	SF(MPC_RMU0_SHAPER_OFFSET_R, MPC_RMU_SHAPER_OFFSET_R, mask_sh),\
	SF(MPC_RMU0_SHAPER_OFFSET_G, MPC_RMU_SHAPER_OFFSET_G, mask_sh),\
	SF(MPC_RMU0_SHAPER_OFFSET_B, MPC_RMU_SHAPER_OFFSET_B, mask_sh),\
	SF(MPC_RMU0_SHAPER_SCALE_R, MPC_RMU_SHAPER_SCALE_R, mask_sh),\
	SF(MPC_RMU0_SHAPER_SCALE_G_B, MPC_RMU_SHAPER_SCALE_G, mask_sh),\
	SF(MPC_RMU0_SHAPER_SCALE_G_B, MPC_RMU_SHAPER_SCALE_B, mask_sh),\
	SF(MPC_RMU0_SHAPER_LUT_INDEX, MPC_RMU_SHAPER_LUT_INDEX, mask_sh),\
	SF(MPC_RMU0_SHAPER_LUT_DATA, MPC_RMU_SHAPER_LUT_DATA, mask_sh),\
	SF(MPC_RMU0_SHAPER_LUT_WRITE_EN_MASK, MPC_RMU_SHAPER_LUT_WRITE_EN_MASK, mask_sh),\
	SF(MPC_RMU0_SHAPER_LUT_WRITE_EN_MASK, MPC_RMU_SHAPER_LUT_WRITE_SEL, mask_sh),\
	SF(MPC_RMU0_SHAPER_LUT_WRITE_EN_MASK, MPC_RMU_SHAPER_CONFIG_STATUS, mask_sh),\
	SF(MPC_RMU0_SHAPER_RAMA_START_CNTL_B, MPC_RMU_SHAPER_RAMA_EXP_REGION_START_B, mask_sh),\
	SF(MPC_RMU0_SHAPER_RAMA_START_CNTL_B, MPC_RMU_SHAPER_RAMA_EXP_REGION_START_SEGMENT_B, mask_sh),\
	SF(MPC_RMU0_SHAPER_RAMA_END_CNTL_B, MPC_RMU_SHAPER_RAMA_EXP_REGION_END_B, mask_sh),\
	SF(MPC_RMU0_SHAPER_RAMA_END_CNTL_B, MPC_RMU_SHAPER_RAMA_EXP_REGION_END_BASE_B, mask_sh),\
	SF(MPC_RMU0_SHAPER_RAMA_REGION_0_1, MPC_RMU_SHAPER_RAMA_EXP_REGION0_LUT_OFFSET, mask_sh),\
	SF(MPC_RMU0_SHAPER_RAMA_REGION_0_1, MPC_RMU_SHAPER_RAMA_EXP_REGION0_NUM_SEGMENTS, mask_sh),\
	SF(MPC_RMU0_SHAPER_RAMA_REGION_0_1, MPC_RMU_SHAPER_RAMA_EXP_REGION1_LUT_OFFSET, mask_sh),\
	SF(MPC_RMU0_SHAPER_RAMA_REGION_0_1, MPC_RMU_SHAPER_RAMA_EXP_REGION1_NUM_SEGMENTS, mask_sh),\
	SF(MPC_RMU_MEM_PWR_CTRL, MPC_RMU0_MEM_PWR_FORCE, mask_sh),\
	SF(MPC_RMU_MEM_PWR_CTRL, MPC_RMU0_MEM_PWR_DIS, mask_sh),\
	SF(MPC_RMU_MEM_PWR_CTRL, MPC_RMU0_SHAPER_MEM_PWR_STATE, mask_sh),\
	SF(MPC_RMU_MEM_PWR_CTRL, MPC_RMU0_3DLUT_MEM_PWR_STATE, mask_sh),\
	SF(MPC_RMU_MEM_PWR_CTRL, MPC_RMU1_MEM_PWR_FORCE, mask_sh),\
	SF(MPC_RMU_MEM_PWR_CTRL, MPC_RMU1_MEM_PWR_DIS, mask_sh),\
	SF(MPC_RMU_MEM_PWR_CTRL, MPC_RMU1_SHAPER_MEM_PWR_STATE, mask_sh),\
	SF(MPC_RMU_MEM_PWR_CTRL, MPC_RMU1_3DLUT_MEM_PWR_STATE, mask_sh),\
	SF(CUR_VUPDATE_LOCK_SET0, CUR_VUPDATE_LOCK_SET, mask_sh)


#define MPC_COMMON_MASK_SH_LIST_DCN30(mask_sh) \
	MPC_COMMON_MASK_SH_LIST_DCN1_0(mask_sh),\
	SF(MPCC0_MPCC_CONTROL, MPCC_BG_BPC, mask_sh),\
	SF(MPCC0_MPCC_CONTROL, MPCC_BOT_GAIN_MODE, mask_sh),\
	SF(MPCC0_MPCC_TOP_GAIN, MPCC_TOP_GAIN, mask_sh),\
	SF(MPCC0_MPCC_BOT_GAIN_INSIDE, MPCC_BOT_GAIN_INSIDE, mask_sh),\
	SF(MPCC0_MPCC_BOT_GAIN_OUTSIDE, MPCC_BOT_GAIN_OUTSIDE, mask_sh),\
	SF(MPC_OUT0_CSC_MODE, MPC_OCSC_MODE, mask_sh),\
	SF(MPC_OUT0_CSC_C11_C12_A, MPC_OCSC_C11_A, mask_sh),\
	SF(MPC_OUT0_CSC_C11_C12_A, MPC_OCSC_C12_A, mask_sh),\
	SF(MPCC0_MPCC_STATUS, MPCC_DISABLED, mask_sh),\
	SF(MPCC0_MPCC_MEM_PWR_CTRL, MPCC_OGAM_MEM_PWR_FORCE, mask_sh),\
	SF(MPCC0_MPCC_MEM_PWR_CTRL, MPCC_OGAM_MEM_PWR_DIS, mask_sh),\
	SF(MPC_OUT0_DENORM_CONTROL, MPC_OUT_DENORM_MODE, mask_sh),\
	SF(MPC_OUT0_DENORM_CONTROL, MPC_OUT_DENORM_CLAMP_MAX_R_CR, mask_sh),\
	SF(MPC_OUT0_DENORM_CONTROL, MPC_OUT_DENORM_CLAMP_MIN_R_CR, mask_sh),\
	SF(MPC_OUT0_DENORM_CLAMP_G_Y, MPC_OUT_DENORM_CLAMP_MAX_G_Y, mask_sh),\
	SF(MPC_OUT0_DENORM_CLAMP_G_Y, MPC_OUT_DENORM_CLAMP_MIN_G_Y, mask_sh),\
	SF(MPC_OUT0_DENORM_CLAMP_B_CB, MPC_OUT_DENORM_CLAMP_MAX_B_CB, mask_sh),\
	SF(MPC_OUT0_DENORM_CLAMP_B_CB, MPC_OUT_DENORM_CLAMP_MIN_B_CB, mask_sh),\
	SF(MPCC_OGAM0_MPCC_GAMUT_REMAP_MODE, MPCC_GAMUT_REMAP_MODE, mask_sh),\
	SF(MPCC_OGAM0_MPCC_GAMUT_REMAP_MODE, MPCC_GAMUT_REMAP_MODE_CURRENT, mask_sh),\
	SF(MPCC_OGAM0_MPCC_GAMUT_REMAP_COEF_FORMAT, MPCC_GAMUT_REMAP_COEF_FORMAT, mask_sh),\
	SF(MPCC_OGAM0_MPC_GAMUT_REMAP_C11_C12_A, MPCC_GAMUT_REMAP_C11_A, mask_sh),\
	SF(MPCC_OGAM0_MPC_GAMUT_REMAP_C11_C12_A, MPCC_GAMUT_REMAP_C12_A, mask_sh),\
	SF(MPC_DWB0_MUX, MPC_DWB0_MUX, mask_sh),\
	SF(MPC_DWB0_MUX, MPC_DWB0_MUX_STATUS, mask_sh),\
	SF(MPC_OUT0_MUX, MPC_OUT_RATE_CONTROL, mask_sh),\
	SF(MPC_OUT0_MUX, MPC_OUT_RATE_CONTROL_DISABLE, mask_sh),\
	SF(MPC_OUT0_MUX, MPC_OUT_FLOW_CONTROL_MODE, mask_sh),\
	SF(MPC_OUT0_MUX, MPC_OUT_FLOW_CONTROL_COUNT, mask_sh), \
	SF(MPC_RMU_CONTROL, MPC_RMU0_MUX, mask_sh), \
	SF(MPC_RMU_CONTROL, MPC_RMU1_MUX, mask_sh), \
	SF(MPC_RMU_CONTROL, MPC_RMU0_MUX_STATUS, mask_sh), \
	SF(MPC_RMU_CONTROL, MPC_RMU1_MUX_STATUS, mask_sh), \
	SF(MPCC_OGAM0_MPCC_OGAM_RAMA_REGION_0_1, MPCC_OGAM_RAMA_EXP_REGION0_LUT_OFFSET, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_RAMA_REGION_0_1, MPCC_OGAM_RAMA_EXP_REGION0_NUM_SEGMENTS, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_RAMA_REGION_0_1, MPCC_OGAM_RAMA_EXP_REGION1_LUT_OFFSET, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_RAMA_REGION_0_1, MPCC_OGAM_RAMA_EXP_REGION1_NUM_SEGMENTS, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_RAMA_END_CNTL2_B, MPCC_OGAM_RAMA_EXP_REGION_END_SLOPE_B, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_RAMA_END_CNTL2_B, MPCC_OGAM_RAMA_EXP_REGION_END_B, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_RAMA_END_CNTL1_B, MPCC_OGAM_RAMA_EXP_REGION_END_BASE_B, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_RAMA_START_SLOPE_CNTL_B, MPCC_OGAM_RAMA_EXP_REGION_START_SLOPE_B, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_RAMA_START_BASE_CNTL_B, MPCC_OGAM_RAMA_EXP_REGION_START_BASE_B, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_RAMA_START_CNTL_B, MPCC_OGAM_RAMA_EXP_REGION_START_B, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_RAMA_START_CNTL_B, MPCC_OGAM_RAMA_EXP_REGION_START_SEGMENT_B, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_RAMA_OFFSET_B, MPCC_OGAM_RAMA_OFFSET_B, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_RAMA_OFFSET_G, MPCC_OGAM_RAMA_OFFSET_G, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_RAMA_OFFSET_R, MPCC_OGAM_RAMA_OFFSET_R, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_LUT_INDEX, MPCC_OGAM_LUT_INDEX, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_CONTROL, MPCC_OGAM_MODE, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_CONTROL, MPCC_OGAM_SELECT, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_CONTROL, MPCC_OGAM_PWL_DISABLE, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_CONTROL, MPCC_OGAM_MODE_CURRENT, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_CONTROL, MPCC_OGAM_SELECT_CURRENT, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_LUT_CONTROL, MPCC_OGAM_LUT_WRITE_COLOR_MASK, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_LUT_CONTROL, MPCC_OGAM_LUT_READ_COLOR_SEL, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_LUT_CONTROL, MPCC_OGAM_LUT_READ_DBG, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_LUT_CONTROL, MPCC_OGAM_LUT_HOST_SEL, mask_sh),\
	SF(MPCC_OGAM0_MPCC_OGAM_LUT_CONTROL, MPCC_OGAM_LUT_CONFIG_MODE, mask_sh),\
	/*SF(MPCC_OGAM0_MPCC_OGAM_LUT_CONTROL, MPCC_OGAM_LUT_STATUS, mask_sh),*/\
	SF(MPCC_OGAM0_MPCC_OGAM_LUT_DATA, MPCC_OGAM_LUT_DATA, mask_sh),\
	SF(MPC_RMU0_3DLUT_MODE, MPC_RMU_3DLUT_MODE, mask_sh),\
	SF(MPC_RMU0_3DLUT_MODE, MPC_RMU_3DLUT_SIZE, mask_sh),\
	/*SF(MPC_RMU0_3DLUT_MODE, MPC_RMU_3DLUT_MODE_CURRENT, mask_sh),*/\
	SF(MPC_RMU0_3DLUT_READ_WRITE_CONTROL, MPC_RMU_3DLUT_WRITE_EN_MASK, mask_sh),\
	SF(MPC_RMU0_3DLUT_READ_WRITE_CONTROL, MPC_RMU_3DLUT_RAM_SEL, mask_sh),\
	SF(MPC_RMU0_3DLUT_READ_WRITE_CONTROL, MPC_RMU_3DLUT_30BIT_EN, mask_sh),\
	/*SF(MPC_RMU0_3DLUT_READ_WRITE_CONTROL, MPC_RMU_3DLUT_CONFIG_STATUS, mask_sh),*/\
	SF(MPC_RMU0_3DLUT_READ_WRITE_CONTROL, MPC_RMU_3DLUT_READ_SEL, mask_sh),\
	SF(MPC_RMU0_3DLUT_INDEX, MPC_RMU_3DLUT_INDEX, mask_sh),\
	SF(MPC_RMU0_3DLUT_DATA, MPC_RMU_3DLUT_DATA0, mask_sh),\
	SF(MPC_RMU0_3DLUT_DATA, MPC_RMU_3DLUT_DATA1, mask_sh),\
	SF(MPC_RMU0_3DLUT_DATA_30BIT, MPC_RMU_3DLUT_DATA_30BIT, mask_sh),\
	SF(MPC_RMU0_SHAPER_CONTROL, MPC_RMU_SHAPER_LUT_MODE, mask_sh),\
	/*SF(MPC_RMU0_SHAPER_CONTROL, MPC_RMU_SHAPER_LUT_MODE_CURRENT, mask_sh),*/\
	SF(MPC_RMU0_SHAPER_OFFSET_R, MPC_RMU_SHAPER_OFFSET_R, mask_sh),\
	SF(MPC_RMU0_SHAPER_OFFSET_G, MPC_RMU_SHAPER_OFFSET_G, mask_sh),\
	SF(MPC_RMU0_SHAPER_OFFSET_B, MPC_RMU_SHAPER_OFFSET_B, mask_sh),\
	SF(MPC_RMU0_SHAPER_SCALE_R, MPC_RMU_SHAPER_SCALE_R, mask_sh),\
	SF(MPC_RMU0_SHAPER_SCALE_G_B, MPC_RMU_SHAPER_SCALE_G, mask_sh),\
	SF(MPC_RMU0_SHAPER_SCALE_G_B, MPC_RMU_SHAPER_SCALE_B, mask_sh),\
	SF(MPC_RMU0_SHAPER_LUT_INDEX, MPC_RMU_SHAPER_LUT_INDEX, mask_sh),\
	SF(MPC_RMU0_SHAPER_LUT_DATA, MPC_RMU_SHAPER_LUT_DATA, mask_sh),\
	SF(MPC_RMU0_SHAPER_LUT_WRITE_EN_MASK, MPC_RMU_SHAPER_LUT_WRITE_EN_MASK, mask_sh),\
	SF(MPC_RMU0_SHAPER_LUT_WRITE_EN_MASK, MPC_RMU_SHAPER_LUT_WRITE_SEL, mask_sh),\
	/*SF(MPC_RMU0_SHAPER_LUT_WRITE_EN_MASK, MPC_RMU_SHAPER_CONFIG_STATUS, mask_sh),*/\
	SF(MPC_RMU0_SHAPER_RAMA_START_CNTL_B, MPC_RMU_SHAPER_RAMA_EXP_REGION_START_B, mask_sh),\
	SF(MPC_RMU0_SHAPER_RAMA_START_CNTL_B, MPC_RMU_SHAPER_RAMA_EXP_REGION_START_SEGMENT_B, mask_sh),\
	SF(MPC_RMU0_SHAPER_RAMA_END_CNTL_B, MPC_RMU_SHAPER_RAMA_EXP_REGION_END_B, mask_sh),\
	SF(MPC_RMU0_SHAPER_RAMA_END_CNTL_B, MPC_RMU_SHAPER_RAMA_EXP_REGION_END_BASE_B, mask_sh),\
	SF(MPC_RMU0_SHAPER_RAMA_REGION_0_1, MPC_RMU_SHAPER_RAMA_EXP_REGION0_LUT_OFFSET, mask_sh),\
	SF(MPC_RMU0_SHAPER_RAMA_REGION_0_1, MPC_RMU_SHAPER_RAMA_EXP_REGION0_NUM_SEGMENTS, mask_sh),\
	SF(MPC_RMU0_SHAPER_RAMA_REGION_0_1, MPC_RMU_SHAPER_RAMA_EXP_REGION1_LUT_OFFSET, mask_sh),\
	SF(MPC_RMU0_SHAPER_RAMA_REGION_0_1, MPC_RMU_SHAPER_RAMA_EXP_REGION1_NUM_SEGMENTS, mask_sh),\
	SF(MPC_RMU_MEM_PWR_CTRL, MPC_RMU0_MEM_PWR_FORCE, mask_sh),\
	SF(MPC_RMU_MEM_PWR_CTRL, MPC_RMU0_MEM_PWR_DIS, mask_sh),\
	SF(MPC_RMU_MEM_PWR_CTRL, MPC_RMU0_SHAPER_MEM_PWR_STATE, mask_sh),\
	SF(MPC_RMU_MEM_PWR_CTRL, MPC_RMU0_3DLUT_MEM_PWR_STATE, mask_sh),\
	SF(MPC_RMU_MEM_PWR_CTRL, MPC_RMU1_MEM_PWR_FORCE, mask_sh),\
	SF(MPC_RMU_MEM_PWR_CTRL, MPC_RMU1_MEM_PWR_DIS, mask_sh),\
	SF(MPC_RMU_MEM_PWR_CTRL, MPC_RMU1_SHAPER_MEM_PWR_STATE, mask_sh),\
	SF(MPC_RMU_MEM_PWR_CTRL, MPC_RMU1_3DLUT_MEM_PWR_STATE, mask_sh),\
	SF(MPC_RMU0_SHAPER_CONTROL, MPC_RMU_SHAPER_MODE_CURRENT, mask_sh),\
	SF(CUR_VUPDATE_LOCK_SET0, CUR_VUPDATE_LOCK_SET, mask_sh)


#define MPC_REG_FIELD_LIST_DCN3_0(type) \
	MPC_REG_FIELD_LIST_DCN2_0(type) \
	type MPC_DWB0_MUX;\
	type MPC_DWB0_MUX_STATUS;\
	type MPC_OUT_RATE_CONTROL;\
	type MPC_OUT_RATE_CONTROL_DISABLE;\
	type MPC_OUT_FLOW_CONTROL_MODE;\
	type MPC_OUT_FLOW_CONTROL_COUNT; \
	type MPCC_GAMUT_REMAP_MODE; \
	type MPCC_GAMUT_REMAP_MODE_CURRENT;\
	type MPCC_GAMUT_REMAP_COEF_FORMAT; \
	type MPCC_GAMUT_REMAP_C11_A; \
	type MPCC_GAMUT_REMAP_C12_A; \
	type MPC_RMU0_MUX; \
	type MPC_RMU1_MUX; \
	type MPC_RMU0_MUX_STATUS; \
	type MPC_RMU1_MUX_STATUS; \
	type MPC_RMU0_MEM_PWR_FORCE;\
	type MPC_RMU0_MEM_PWR_DIS;\
	type MPC_RMU0_SHAPER_MEM_PWR_STATE;\
	type MPC_RMU0_3DLUT_MEM_PWR_STATE;\
	type MPC_RMU1_MEM_PWR_FORCE;\
	type MPC_RMU1_MEM_PWR_DIS;\
	type MPC_RMU1_SHAPER_MEM_PWR_STATE;\
	type MPC_RMU1_3DLUT_MEM_PWR_STATE;\
	type MPCC_OGAM_RAMA_EXP_REGION_START_SLOPE_B; \
	type MPCC_OGAM_RAMA_EXP_REGION_START_BASE_B;\
	type MPCC_OGAM_RAMA_OFFSET_B;\
	type MPCC_OGAM_RAMA_OFFSET_G;\
	type MPCC_OGAM_RAMA_OFFSET_R;\
	type MPCC_OGAM_SELECT; \
	type MPCC_OGAM_PWL_DISABLE; \
	type MPCC_OGAM_MODE_CURRENT; \
	type MPCC_OGAM_SELECT_CURRENT; \
	type MPCC_OGAM_LUT_WRITE_COLOR_MASK; \
	type MPCC_OGAM_LUT_READ_COLOR_SEL; \
	type MPCC_OGAM_LUT_READ_DBG; \
	type MPCC_OGAM_LUT_HOST_SEL; \
	type MPCC_OGAM_LUT_CONFIG_MODE; \
	type MPCC_OGAM_LUT_STATUS; \
	type MPCC_OGAM_RAMA_START_BASE_CNTL_B;\
	type MPC_RMU_3DLUT_MODE; \
	type MPC_RMU_3DLUT_SIZE; \
	type MPC_RMU_3DLUT_MODE_CURRENT; \
	type MPC_RMU_3DLUT_WRITE_EN_MASK;\
	type MPC_RMU_3DLUT_RAM_SEL;\
	type MPC_RMU_3DLUT_30BIT_EN;\
	type MPC_RMU_3DLUT_CONFIG_STATUS;\
	type MPC_RMU_3DLUT_READ_SEL;\
	type MPC_RMU_3DLUT_INDEX;\
	type MPC_RMU_3DLUT_DATA0;\
	type MPC_RMU_3DLUT_DATA1;\
	type MPC_RMU_3DLUT_DATA_30BIT;\
	type MPC_RMU_SHAPER_LUT_MODE;\
	type MPC_RMU_SHAPER_LUT_MODE_CURRENT;\
	type MPC_RMU_SHAPER_OFFSET_R;\
	type MPC_RMU_SHAPER_OFFSET_G;\
	type MPC_RMU_SHAPER_OFFSET_B;\
	type MPC_RMU_SHAPER_SCALE_R;\
	type MPC_RMU_SHAPER_SCALE_G;\
	type MPC_RMU_SHAPER_SCALE_B;\
	type MPC_RMU_SHAPER_LUT_INDEX;\
	type MPC_RMU_SHAPER_LUT_DATA;\
	type MPC_RMU_SHAPER_LUT_WRITE_EN_MASK;\
	type MPC_RMU_SHAPER_LUT_WRITE_SEL;\
	type MPC_RMU_SHAPER_CONFIG_STATUS;\
	type MPC_RMU_SHAPER_RAMA_EXP_REGION_START_B;\
	type MPC_RMU_SHAPER_RAMA_EXP_REGION_START_SEGMENT_B;\
	type MPC_RMU_SHAPER_RAMA_EXP_REGION_END_B;\
	type MPC_RMU_SHAPER_RAMA_EXP_REGION_END_BASE_B;\
	type MPC_RMU_SHAPER_RAMA_EXP_REGION0_LUT_OFFSET;\
	type MPC_RMU_SHAPER_RAMA_EXP_REGION0_NUM_SEGMENTS;\
	type MPC_RMU_SHAPER_RAMA_EXP_REGION1_LUT_OFFSET;\
	type MPC_RMU_SHAPER_RAMA_EXP_REGION1_NUM_SEGMENTS;\
	type MPC_RMU_SHAPER_MODE_CURRENT


struct dcn30_mpc_registers {
	MPC_REG_VARIABLE_LIST_DCN3_0;
};

struct dcn30_mpc_shift {
	MPC_REG_FIELD_LIST_DCN3_0(uint8_t);
};

struct dcn30_mpc_mask {
	MPC_REG_FIELD_LIST_DCN3_0(uint32_t);
};

struct dcn30_mpc {
	struct mpc base;

	int mpcc_in_use_mask;
	int num_mpcc;
	const struct dcn30_mpc_registers *mpc_regs;
	const struct dcn30_mpc_shift *mpc_shift;
	const struct dcn30_mpc_mask *mpc_mask;
	int num_rmu;
};

void dcn30_mpc_construct(struct dcn30_mpc *mpc30,
	struct dc_context *ctx,
	const struct dcn30_mpc_registers *mpc_regs,
	const struct dcn30_mpc_shift *mpc_shift,
	const struct dcn30_mpc_mask *mpc_mask,
	int num_mpcc,
	int num_rmu);

bool mpc3_program_shaper(
		struct mpc *mpc,
		const struct pwl_params *params,
		uint32_t rmu_idx);

bool mpc3_program_3dlut(
		struct mpc *mpc,
		const struct tetrahedral_params *params,
		int rmu_idx);

uint32_t mpcc3_acquire_rmu(struct mpc *mpc,
		int mpcc_id, int rmu_idx);

void mpc3_set_denorm(
	struct mpc *mpc,
	int opp_id,
	enum dc_color_depth output_depth);

void mpc3_set_denorm_clamp(
	struct mpc *mpc,
	int opp_id,
	struct mpc_denorm_clamp denorm_clamp);

void mpc3_set_output_csc(
	struct mpc *mpc,
	int opp_id,
	const uint16_t *regval,
	enum mpc_output_csc_mode ocsc_mode);

void mpc3_set_ocsc_default(
	struct mpc *mpc,
	int opp_id,
	enum dc_color_space color_space,
	enum mpc_output_csc_mode ocsc_mode);

void mpc3_set_output_gamma(
	struct mpc *mpc,
	int mpcc_id,
	const struct pwl_params *params);

uint32_t mpc3_get_rmu_mux_status(
	struct mpc *mpc,
	int rmu_idx);

void mpc3_set_gamut_remap(
	struct mpc *mpc,
	int mpcc_id,
	const struct mpc_grph_gamut_adjustment *adjust);

void mpc3_set_rmu_mux(
	struct mpc *mpc,
	int rmu_idx,
	int value);

#endif
