// <experimental/regex> -*- C++ -*-

// Copyright (C) 2015-2017 Free Software Foundation, Inc.
//
// This file is part of the GNU ISO C++ Library.  This library is free
// software; you can redistribute it and/or modify it under the
// terms of the GNU General Public License as published by the
// Free Software Foundation; either version 3, or (at your option)
// any later version.

// This library is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.

// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.

// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

/** @file experimental/regex
 *  This is a TS C++ Library header.
 */

#ifndef _GLIBCXX_EXPERIMENTAL_REGEX
#define _GLIBCXX_EXPERIMENTAL_REGEX 1

#pragma GCC system_header

#if __cplusplus >= 201402L

#include <regex>
#include <experimental/string>

namespace std _GLIBCXX_VISIBILITY(default)
{
namespace experimental
{
inline namespace fundamentals_v2
{
#if _GLIBCXX_USE_CXX11_ABI
namespace pmr
{
_GLIBCXX_BEGIN_NAMESPACE_VERSION

  template<typename _BidirectionalIterator>
    using match_results
      = std::match_results<_BidirectionalIterator, polymorphic_allocator<
			     sub_match<_BidirectionalIterator>>>;

  typedef match_results<const char*> cmatch;
  typedef match_results<const wchar_t*> wcmatch;
  typedef match_results<string::const_iterator> smatch;
  typedef match_results<wstring::const_iterator> wsmatch;

_GLIBCXX_END_NAMESPACE_VERSION
} // namespace pmr
#endif
} // namespace fundamentals_v2
} // namespace experimental
} // namespace std

#endif // C++14

#endif // _GLIBCXX_EXPERIMENTAL_REGEX
