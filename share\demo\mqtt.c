#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "MQTTClient.h"
#include <pthread.h>
#include "mqtt.h"
#include "parse_config.h"
#include "cJSON.h"
#define QOS 1
#define CLIENTID_PUB "LinuxToWin"
#define DEMO "demo"
MQTTClient client;
volatile MQTTClient_deliveryToken deliveredtoken;
// 数组用来存放订阅到的数据
char data[1024] = {0};
// 用来存放解析后的数据
int demo_data;
/* 接收虚拟仿真软件发布的消息，通过cJSON 解析，并获取传感器数据*/
int transfor_virtual_data()
{

	cJSON *root = NULL;
	cJSON *item = NULL; // cjson对象

	root = cJSON_Parse(data);
	if (!root)
	{
		printf("parse err\n");
		return -1;
	}

	// 获取灯的状态
	item = cJSON_GetObjectItem(root, DEMO);
	if (NULL != item)
	{
		demo_data = item->valueint;
	}
	return 0;
}

void delivered(void *context, MQTTClient_deliveryToken dt)
{
	// printf("Message with token value %d delivery confirmed\n", dt);
	deliveredtoken = dt;
}

int msgarrvd(void *context, char *topicName, int topicLen, MQTTClient_message *message)
{
	// 数据到达后执行这个函数并且把数据存放到数组里
	memset(data, 0, sizeof(data));
	memcpy(data, message->payload, message->payloadlen);
	// 打印出订阅到的数据
	// printf("data_sub:%s\n",data);
	transfor_virtual_data();
	MQTTClient_freeMessage(&message);
	MQTTClient_free(topicName);
	return 1;
}

// 连接丢失时执行
void connlost(void *context, char *cause)
{
	printf("\nConnection lost\n");
	printf(" cause: %s\n", cause);
}

// 自定义发布信息的函数
int mqtt_publish(const char *topic, char *msg)
{
	MQTTClient_message pubmsg = MQTTClient_message_initializer;
	MQTTClient_deliveryToken token;
	pubmsg.payload = msg;
	pubmsg.payloadlen = (int)strlen(msg);
	pubmsg.qos = QOS;
	pubmsg.retained = 0;
	return MQTTClient_publishMessage(client, topic, &pubmsg, &token);
}

// 客户端退出的函数
void exit_mqtt()
{
	int rc;
	if ((rc = MQTTClient_disconnect(client, 10000)) != MQTTCLIENT_SUCCESS)
		printf("Failed to disconnect, return code %d\n", rc);

	MQTTClient_destroy(&client);
}

// 自定义订阅函数
int mqtt_subscribe(const char *topic)
{
	return MQTTClient_subscribe(client, topic, QOS);
}

// mqtt的初始化，完成客户端对象的定义以及填充并且连接到服务器
int mqtt_init()
{
	char uri[128] = {0};
	GetProfileString(DEF_CONF_FILE, "mqtt", "uri", uri);
	MQTTClient_connectOptions conn_opts = MQTTClient_connectOptions_initializer;
	int rc;
	// MQTTClient_create(&client, ADDRESS, CLIENTID_PUB, MQTTCLIENT_PERSISTENCE_NONE, NULL);
	MQTTClient_create(&client, uri, CLIENTID_PUB, MQTTCLIENT_PERSISTENCE_NONE, NULL);
	conn_opts.keepAliveInterval = 20;
	conn_opts.cleansession = 1;

	//连接成功后的回调函数
	MQTTClient_setCallbacks(client, NULL, connlost, msgarrvd, delivered);

	if ((rc = MQTTClient_connect(client, &conn_opts)) != MQTTCLIENT_SUCCESS)
	{
		printf("Failed to connect, return code %d\n", rc);
		return -1;
	}
	printf("mqtt connect success\n");

	return 0;
}
