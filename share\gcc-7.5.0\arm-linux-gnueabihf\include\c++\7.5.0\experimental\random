// <experimental/random> -*- C++ -*-

// Copyright (C) 2015-2017 Free Software Foundation, Inc.
//
// This file is part of the GNU ISO C++ Library.  This library is free
// software; you can redistribute it and/or modify it under the
// terms of the GNU General Public License as published by the
// Free Software Foundation; either version 3, or (at your option)
// any later version.

// This library is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.

// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.

// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

/** @file experimental/random
 *  This is a TS C++ Library header.
 */

#ifndef _GLIBCXX_EXPERIMENTAL_RANDOM
#define _GLIBCXX_EXPERIMENTAL_RANDOM 1

#include <random>
#include <experimental/bits/lfts_config.h>

namespace std {
namespace experimental {
inline namespace fundamentals_v2 {
_GLIBCXX_BEGIN_NAMESPACE_VERSION

#define __cpp_lib_experimental_randint 201511

  inline std::default_random_engine&
  _S_randint_engine()
  {
    static thread_local default_random_engine __eng{random_device{}()};
    return __eng;
  }

  // 13.2.2.1, Function template randint
  template<typename _IntType>
    inline _IntType
    randint(_IntType __a, _IntType __b)
    {
      static_assert(is_integral<_IntType>::value && sizeof(_IntType) > 1,
		    "argument must be an integer type");
      using _Dist = std::uniform_int_distribution<_IntType>;
      // This relies on the fact our uniform_int_distribution is stateless,
      // otherwise we'd need a static thread_local _Dist and pass it
      // _Dist::param_type{__a, __b}.
      return _Dist(__a, __b)(_S_randint_engine());
    }

  inline void
  reseed()
  {
    _S_randint_engine().seed(random_device{}());
  }

  inline void
  reseed(default_random_engine::result_type __value)
  {
    _S_randint_engine().seed(__value);
  }

_GLIBCXX_END_NAMESPACE_VERSION
} // namespace fundamentals_v2
} // namespace experimental
} // namespace std

#endif
