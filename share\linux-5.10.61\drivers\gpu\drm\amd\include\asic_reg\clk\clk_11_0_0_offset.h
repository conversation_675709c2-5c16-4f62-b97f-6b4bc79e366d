/*
 * Copyright (C) 2019  Advanced Micro Devices, Inc.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a
 * copy of this software and associated documentation files (the "Software"),
 * to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included
 * in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
 * THE COPYRIGHT HOLDER(S) BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN
 * AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
#ifndef _clk_11_0_0_OFFSET_HEADER
#define _clk_11_0_0_OFFSET_HEADER


// addressBlock: clk_clk3_0_SmuClkDec
// base address: 0x5c800
#define mmCLK3_0_CLK3_CLK_PLL_REQ                                                                      0x000e
#define mmCLK3_0_CLK3_CLK_PLL_REQ_BASE_IDX                                                             3
#define mmCLK3_0_CLK3_CLK2_DFS_CNTL                                                                    0x0054
#define mmCLK3_0_CLK3_CLK2_DFS_CNTL_BASE_IDX                                                           3


#endif
