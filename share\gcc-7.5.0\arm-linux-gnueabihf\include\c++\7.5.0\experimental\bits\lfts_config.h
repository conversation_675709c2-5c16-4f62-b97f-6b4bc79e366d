// Namespace declarations for Library Fundamentals TS -*- C++ -*-

// Copyright (C) 2016-2017 Free Software Foundation, Inc.
//
// This file is part of the GNU ISO C++ Library.  This library is free
// software; you can redistribute it and/or modify it under the
// terms of the GNU General Public License as published by the
// Free Software Foundation; either version 3, or (at your option)
// any later version.

// This library is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.

// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.

// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

/** @file experimental/bits/lfts_config.h
 *  This is an internal header file, included by other library headers.
 *  Do not attempt to use it directly.
 */

#if __cplusplus >= 201402L
#include <bits/c++config.h>

#if _GLIBCXX_INLINE_VERSION
namespace std _GLIBCXX_VISIBILITY(default)
{
namespace chrono
{
namespace experimental
{
inline namespace fundamentals_v1 { inline namespace __7 { } }
inline namespace fundamentals_v2 { inline namespace __7 { } }
} // namespace experimental
} // namespace chrono

namespace experimental
{
inline namespace fundamentals_v1 {
  inline namespace __7 { }
  namespace __detail { inline namespace __7 { } }
}
inline namespace fundamentals_v2 {
  inline namespace __7 { }
  namespace pmr { inline namespace __7 { } }
  namespace __detail { inline namespace __7 { } }
} // namespace fundamentals_v2
inline namespace literals { inline namespace string_view_literals {
  inline namespace __7 { }
} } // namespace literals::string_view_literals
} // namespace experimental
} // namespace std
#endif
#endif
