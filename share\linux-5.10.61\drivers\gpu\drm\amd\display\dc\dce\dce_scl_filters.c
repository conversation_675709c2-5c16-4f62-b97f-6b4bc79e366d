/*
 * Copyright 2012-16 Advanced Micro Devices, Inc.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a
 * copy of this software and associated documentation files (the "Software"),
 * to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
 * THE COPYRIGHT HOLDER(S) OR AUTHOR(S) BE LIABLE FOR ANY CLAIM, DAMAGES OR
 * OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 * OTHER DEALINGS IN THE SOFTWARE.
 *
 * Authors: <AUTHORS>
 *
 */
#include "transform.h"

//=========================================
// <num_taps>    = 2
// <num_phases>  = 16
// <scale_ratio> = 0.833333 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = s1.10
// <CoefOut>     = s1.12
//=========================================
static const uint16_t filter_2tap_16p[18] = {
		0x1000, 0x0000,
		0x0FF0, 0x0010,
		0x0FB0, 0x0050,
		0x0F34, 0x00CC,
		0x0E68, 0x0198,
		0x0D44, 0x02BC,
		0x0BC4, 0x043C,
		0x09FC, 0x0604,
		0x0800, 0x0800
};

//=========================================
// <num_taps>    = 3
// <num_phases>  = 16
// <scale_ratio> = 0.83333 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_3tap_16p_upscale[27] = {
		0x0804, 0x07FC, 0x0000,
		0x06AC, 0x0978, 0x3FDC,
		0x055C, 0x0AF0, 0x3FB4,
		0x0420, 0x0C50, 0x3F90,
		0x0300, 0x0D88, 0x3F78,
		0x0200, 0x0E90, 0x3F70,
		0x0128, 0x0F5C, 0x3F7C,
		0x007C, 0x0FD8, 0x3FAC,
		0x0000, 0x1000, 0x0000
};

//=========================================
// <num_taps>    = 3
// <num_phases>  = 16
// <scale_ratio> = 1.16666 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_3tap_16p_116[27] = {
		0x0804, 0x07FC, 0x0000,
		0x0700, 0x0914, 0x3FEC,
		0x0604, 0x0A1C, 0x3FE0,
		0x050C, 0x0B14, 0x3FE0,
		0x041C, 0x0BF4, 0x3FF0,
		0x0340, 0x0CB0, 0x0010,
		0x0274, 0x0D3C, 0x0050,
		0x01C0, 0x0D94, 0x00AC,
		0x0128, 0x0DB4, 0x0124
};

//=========================================
// <num_taps>    = 3
// <num_phases>  = 16
// <scale_ratio> = 1.49999 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_3tap_16p_149[27] = {
		0x0804, 0x07FC, 0x0000,
		0x0730, 0x08CC, 0x0004,
		0x0660, 0x098C, 0x0014,
		0x0590, 0x0A3C, 0x0034,
		0x04C4, 0x0AD4, 0x0068,
		0x0400, 0x0B54, 0x00AC,
		0x0348, 0x0BB0, 0x0108,
		0x029C, 0x0BEC, 0x0178,
		0x0200, 0x0C00, 0x0200
};

//=========================================
// <num_taps>    = 3
// <num_phases>  = 16
// <scale_ratio> = 1.83332 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_3tap_16p_183[27] = {
		0x0804, 0x07FC, 0x0000,
		0x0754, 0x0880, 0x002C,
		0x06A8, 0x08F0, 0x0068,
		0x05FC, 0x0954, 0x00B0,
		0x0550, 0x09AC, 0x0104,
		0x04A8, 0x09F0, 0x0168,
		0x0408, 0x0A20, 0x01D8,
		0x036C, 0x0A40, 0x0254,
		0x02DC, 0x0A48, 0x02DC
};

//=========================================
// <num_taps>    = 4
// <num_phases>  = 16
// <scale_ratio> = 0.83333 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_4tap_16p_upscale[36] = {
		0x0000, 0x1000, 0x0000, 0x0000,
		0x3F74, 0x0FDC, 0x00B4, 0x3FFC,
		0x3F0C, 0x0F70, 0x0194, 0x3FF0,
		0x3ECC, 0x0EC4, 0x0298, 0x3FD8,
		0x3EAC, 0x0DE4, 0x03B8, 0x3FB8,
		0x3EA4, 0x0CD8, 0x04F4, 0x3F90,
		0x3EB8, 0x0BA0, 0x0644, 0x3F64,
		0x3ED8, 0x0A54, 0x07A0, 0x3F34,
		0x3F00, 0x08FC, 0x0900, 0x3F04
};

//=========================================
// <num_taps>    = 4
// <num_phases>  = 16
// <scale_ratio> = 1.16666 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_4tap_16p_116[36] = {
		0x01A8, 0x0CB4, 0x01A4, 0x0000,
		0x0110, 0x0CB0, 0x0254, 0x3FEC,
		0x0090, 0x0C80, 0x031C, 0x3FD4,
		0x0024, 0x0C2C, 0x03F4, 0x3FBC,
		0x3FD8, 0x0BAC, 0x04DC, 0x3FA0,
		0x3F9C, 0x0B14, 0x05CC, 0x3F84,
		0x3F70, 0x0A60, 0x06C4, 0x3F6C,
		0x3F5C, 0x098C, 0x07BC, 0x3F5C,
		0x3F54, 0x08AC, 0x08AC, 0x3F54
};

//=========================================
// <num_taps>    = 4
// <num_phases>  = 16
// <scale_ratio> = 1.49999 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_4tap_16p_149[36] = {
		0x02B8, 0x0A90, 0x02B8, 0x0000,
		0x0230, 0x0A90, 0x0350, 0x3FF0,
		0x01B8, 0x0A78, 0x03F0, 0x3FE0,
		0x0148, 0x0A48, 0x049C, 0x3FD4,
		0x00E8, 0x0A00, 0x054C, 0x3FCC,
		0x0098, 0x09A0, 0x0600, 0x3FC8,
		0x0054, 0x0928, 0x06B4, 0x3FD0,
		0x001C, 0x08A4, 0x0760, 0x3FE0,
		0x3FFC, 0x0804, 0x0804, 0x3FFC
};

//=========================================
// <num_taps>    = 4
// <num_phases>  = 16
// <scale_ratio> = 1.83332 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_4tap_16p_183[36] = {
		0x03B0, 0x08A0, 0x03B0, 0x0000,
		0x0348, 0x0898, 0x041C, 0x0004,
		0x02DC, 0x0884, 0x0490, 0x0010,
		0x0278, 0x0864, 0x0500, 0x0024,
		0x021C, 0x0838, 0x0570, 0x003C,
		0x01C8, 0x07FC, 0x05E0, 0x005C,
		0x0178, 0x07B8, 0x064C, 0x0084,
		0x0130, 0x076C, 0x06B0, 0x00B4,
		0x00F0, 0x0714, 0x0710, 0x00EC
};

//=========================================
// <num_taps>    = 2
// <num_phases>  = 64
// <scale_ratio> = 0.833333 (input/output)
// <sharpness>   = 0
// <CoefType> = ModifiedLanczos
// <CoefQuant> = s1.10
// <CoefOut> = s1.12
//=========================================
static const uint16_t filter_2tap_64p[66] = {
		0x1000, 0x0000,
		0x1000, 0x0000,
		0x0FFC, 0x0004,
		0x0FF8, 0x0008,
		0x0FF0, 0x0010,
		0x0FE4, 0x001C,
		0x0FD8, 0x0028,
		0x0FC4, 0x003C,
		0x0FB0, 0x0050,
		0x0F98, 0x0068,
		0x0F7C, 0x0084,
		0x0F58, 0x00A8,
		0x0F34, 0x00CC,
		0x0F08, 0x00F8,
		0x0ED8, 0x0128,
		0x0EA4, 0x015C,
		0x0E68, 0x0198,
		0x0E28, 0x01D8,
		0x0DE4, 0x021C,
		0x0D98, 0x0268,
		0x0D44, 0x02BC,
		0x0CEC, 0x0314,
		0x0C90, 0x0370,
		0x0C2C, 0x03D4,
		0x0BC4, 0x043C,
		0x0B58, 0x04A8,
		0x0AE8, 0x0518,
		0x0A74, 0x058C,
		0x09FC, 0x0604,
		0x0980, 0x0680,
		0x0900, 0x0700,
		0x0880, 0x0780,
		0x0800, 0x0800
};

//=========================================
// <num_taps>    = 3
// <num_phases>  = 64
// <scale_ratio> = 0.83333 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_3tap_64p_upscale[99] = {
		0x0804, 0x07FC, 0x0000,
		0x07A8, 0x0860, 0x3FF8,
		0x0754, 0x08BC, 0x3FF0,
		0x0700, 0x0918, 0x3FE8,
		0x06AC, 0x0978, 0x3FDC,
		0x0654, 0x09D8, 0x3FD4,
		0x0604, 0x0A34, 0x3FC8,
		0x05B0, 0x0A90, 0x3FC0,
		0x055C, 0x0AF0, 0x3FB4,
		0x050C, 0x0B48, 0x3FAC,
		0x04BC, 0x0BA0, 0x3FA4,
		0x0470, 0x0BF4, 0x3F9C,
		0x0420, 0x0C50, 0x3F90,
		0x03D8, 0x0C9C, 0x3F8C,
		0x038C, 0x0CF0, 0x3F84,
		0x0344, 0x0D40, 0x3F7C,
		0x0300, 0x0D88, 0x3F78,
		0x02BC, 0x0DD0, 0x3F74,
		0x027C, 0x0E14, 0x3F70,
		0x023C, 0x0E54, 0x3F70,
		0x0200, 0x0E90, 0x3F70,
		0x01C8, 0x0EC8, 0x3F70,
		0x0190, 0x0EFC, 0x3F74,
		0x015C, 0x0F2C, 0x3F78,
		0x0128, 0x0F5C, 0x3F7C,
		0x00FC, 0x0F7C, 0x3F88,
		0x00CC, 0x0FA4, 0x3F90,
		0x00A4, 0x0FC0, 0x3F9C,
		0x007C, 0x0FD8, 0x3FAC,
		0x0058, 0x0FE8, 0x3FC0,
		0x0038, 0x0FF4, 0x3FD4,
		0x0018, 0x1000, 0x3FE8,
		0x0000, 0x1000, 0x0000
};

//=========================================
// <num_taps>    = 3
// <num_phases>  = 64
// <scale_ratio> = 1.16666 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_3tap_64p_116[99] = {
		0x0804, 0x07FC, 0x0000,
		0x07C0, 0x0844, 0x3FFC,
		0x0780, 0x0888, 0x3FF8,
		0x0740, 0x08D0, 0x3FF0,
		0x0700, 0x0914, 0x3FEC,
		0x06C0, 0x0958, 0x3FE8,
		0x0684, 0x0998, 0x3FE4,
		0x0644, 0x09DC, 0x3FE0,
		0x0604, 0x0A1C, 0x3FE0,
		0x05C4, 0x0A5C, 0x3FE0,
		0x0588, 0x0A9C, 0x3FDC,
		0x0548, 0x0ADC, 0x3FDC,
		0x050C, 0x0B14, 0x3FE0,
		0x04CC, 0x0B54, 0x3FE0,
		0x0490, 0x0B8C, 0x3FE4,
		0x0458, 0x0BC0, 0x3FE8,
		0x041C, 0x0BF4, 0x3FF0,
		0x03E0, 0x0C28, 0x3FF8,
		0x03A8, 0x0C58, 0x0000,
		0x0374, 0x0C88, 0x0004,
		0x0340, 0x0CB0, 0x0010,
		0x0308, 0x0CD8, 0x0020,
		0x02D8, 0x0CFC, 0x002C,
		0x02A0, 0x0D20, 0x0040,
		0x0274, 0x0D3C, 0x0050,
		0x0244, 0x0D58, 0x0064,
		0x0214, 0x0D70, 0x007C,
		0x01E8, 0x0D84, 0x0094,
		0x01C0, 0x0D94, 0x00AC,
		0x0198, 0x0DA0, 0x00C8,
		0x0170, 0x0DAC, 0x00E4,
		0x014C, 0x0DB0, 0x0104,
		0x0128, 0x0DB4, 0x0124
};

//=========================================
// <num_taps>    = 3
// <num_phases>  = 64
// <scale_ratio> = 1.49999 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_3tap_64p_149[99] = {
		0x0804, 0x07FC, 0x0000,
		0x07CC, 0x0834, 0x0000,
		0x0798, 0x0868, 0x0000,
		0x0764, 0x089C, 0x0000,
		0x0730, 0x08CC, 0x0004,
		0x0700, 0x08FC, 0x0004,
		0x06CC, 0x092C, 0x0008,
		0x0698, 0x095C, 0x000C,
		0x0660, 0x098C, 0x0014,
		0x062C, 0x09B8, 0x001C,
		0x05FC, 0x09E4, 0x0020,
		0x05C4, 0x0A10, 0x002C,
		0x0590, 0x0A3C, 0x0034,
		0x055C, 0x0A64, 0x0040,
		0x0528, 0x0A8C, 0x004C,
		0x04F8, 0x0AB0, 0x0058,
		0x04C4, 0x0AD4, 0x0068,
		0x0490, 0x0AF8, 0x0078,
		0x0460, 0x0B18, 0x0088,
		0x0430, 0x0B38, 0x0098,
		0x0400, 0x0B54, 0x00AC,
		0x03D0, 0x0B6C, 0x00C4,
		0x03A0, 0x0B88, 0x00D8,
		0x0374, 0x0B9C, 0x00F0,
		0x0348, 0x0BB0, 0x0108,
		0x0318, 0x0BC4, 0x0124,
		0x02EC, 0x0BD4, 0x0140,
		0x02C4, 0x0BE0, 0x015C,
		0x029C, 0x0BEC, 0x0178,
		0x0274, 0x0BF4, 0x0198,
		0x024C, 0x0BFC, 0x01B8,
		0x0228, 0x0BFC, 0x01DC,
		0x0200, 0x0C00, 0x0200
};

//=========================================
// <num_taps>    = 3
// <num_phases>  = 64
// <scale_ratio> = 1.83332 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_3tap_64p_183[99] = {
		0x0804, 0x07FC, 0x0000,
		0x07D4, 0x0824, 0x0008,
		0x07AC, 0x0840, 0x0014,
		0x0780, 0x0860, 0x0020,
		0x0754, 0x0880, 0x002C,
		0x0728, 0x089C, 0x003C,
		0x0700, 0x08B8, 0x0048,
		0x06D4, 0x08D4, 0x0058,
		0x06A8, 0x08F0, 0x0068,
		0x067C, 0x090C, 0x0078,
		0x0650, 0x0924, 0x008C,
		0x0628, 0x093C, 0x009C,
		0x05FC, 0x0954, 0x00B0,
		0x05D0, 0x096C, 0x00C4,
		0x05A8, 0x0980, 0x00D8,
		0x0578, 0x0998, 0x00F0,
		0x0550, 0x09AC, 0x0104,
		0x0528, 0x09BC, 0x011C,
		0x04FC, 0x09D0, 0x0134,
		0x04D4, 0x09E0, 0x014C,
		0x04A8, 0x09F0, 0x0168,
		0x0480, 0x09FC, 0x0184,
		0x045C, 0x0A08, 0x019C,
		0x0434, 0x0A14, 0x01B8,
		0x0408, 0x0A20, 0x01D8,
		0x03E0, 0x0A2C, 0x01F4,
		0x03B8, 0x0A34, 0x0214,
		0x0394, 0x0A38, 0x0234,
		0x036C, 0x0A40, 0x0254,
		0x0348, 0x0A44, 0x0274,
		0x0324, 0x0A48, 0x0294,
		0x0300, 0x0A48, 0x02B8,
		0x02DC, 0x0A48, 0x02DC
};

//=========================================
// <num_taps>    = 4
// <num_phases>  = 64
// <scale_ratio> = 0.83333 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_4tap_64p_upscale[132] = {
		0x0000, 0x1000, 0x0000, 0x0000,
		0x3FDC, 0x0FFC, 0x0028, 0x0000,
		0x3FB4, 0x0FF8, 0x0054, 0x0000,
		0x3F94, 0x0FE8, 0x0084, 0x0000,
		0x3F74, 0x0FDC, 0x00B4, 0x3FFC,
		0x3F58, 0x0FC4, 0x00E8, 0x3FFC,
		0x3F3C, 0x0FAC, 0x0120, 0x3FF8,
		0x3F24, 0x0F90, 0x0158, 0x3FF4,
		0x3F0C, 0x0F70, 0x0194, 0x3FF0,
		0x3EF8, 0x0F4C, 0x01D0, 0x3FEC,
		0x3EE8, 0x0F20, 0x0210, 0x3FE8,
		0x3ED8, 0x0EF4, 0x0254, 0x3FE0,
		0x3ECC, 0x0EC4, 0x0298, 0x3FD8,
		0x3EC0, 0x0E90, 0x02DC, 0x3FD4,
		0x3EB8, 0x0E58, 0x0324, 0x3FCC,
		0x3EB0, 0x0E20, 0x036C, 0x3FC4,
		0x3EAC, 0x0DE4, 0x03B8, 0x3FB8,
		0x3EA8, 0x0DA4, 0x0404, 0x3FB0,
		0x3EA4, 0x0D60, 0x0454, 0x3FA8,
		0x3EA4, 0x0D1C, 0x04A4, 0x3F9C,
		0x3EA4, 0x0CD8, 0x04F4, 0x3F90,
		0x3EA8, 0x0C88, 0x0548, 0x3F88,
		0x3EAC, 0x0C3C, 0x059C, 0x3F7C,
		0x3EB0, 0x0BF0, 0x05F0, 0x3F70,
		0x3EB8, 0x0BA0, 0x0644, 0x3F64,
		0x3EBC, 0x0B54, 0x0698, 0x3F58,
		0x3EC4, 0x0B00, 0x06F0, 0x3F4C,
		0x3ECC, 0x0AAC, 0x0748, 0x3F40,
		0x3ED8, 0x0A54, 0x07A0, 0x3F34,
		0x3EE0, 0x0A04, 0x07F8, 0x3F24,
		0x3EEC, 0x09AC, 0x0850, 0x3F18,
		0x3EF8, 0x0954, 0x08A8, 0x3F0C,
		0x3F00, 0x08FC, 0x0900, 0x3F04
};

//=========================================
// <num_taps>    = 4
// <num_phases>  = 64
// <scale_ratio> = 1.16666 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_4tap_64p_116[132] = {
		0x01A8, 0x0CB4, 0x01A4, 0x0000,
		0x017C, 0x0CB8, 0x01D0, 0x3FFC,
		0x0158, 0x0CB8, 0x01F8, 0x3FF8,
		0x0130, 0x0CB4, 0x0228, 0x3FF4,
		0x0110, 0x0CB0, 0x0254, 0x3FEC,
		0x00EC, 0x0CA8, 0x0284, 0x3FE8,
		0x00CC, 0x0C9C, 0x02B4, 0x3FE4,
		0x00AC, 0x0C90, 0x02E8, 0x3FDC,
		0x0090, 0x0C80, 0x031C, 0x3FD4,
		0x0070, 0x0C70, 0x0350, 0x3FD0,
		0x0058, 0x0C5C, 0x0384, 0x3FC8,
		0x003C, 0x0C48, 0x03BC, 0x3FC0,
		0x0024, 0x0C2C, 0x03F4, 0x3FBC,
		0x0010, 0x0C10, 0x042C, 0x3FB4,
		0x3FFC, 0x0BF4, 0x0464, 0x3FAC,
		0x3FE8, 0x0BD4, 0x04A0, 0x3FA4,
		0x3FD8, 0x0BAC, 0x04DC, 0x3FA0,
		0x3FC4, 0x0B8C, 0x0518, 0x3F98,
		0x3FB4, 0x0B68, 0x0554, 0x3F90,
		0x3FA8, 0x0B40, 0x0590, 0x3F88,
		0x3F9C, 0x0B14, 0x05CC, 0x3F84,
		0x3F90, 0x0AEC, 0x0608, 0x3F7C,
		0x3F84, 0x0ABC, 0x0648, 0x3F78,
		0x3F7C, 0x0A90, 0x0684, 0x3F70,
		0x3F70, 0x0A60, 0x06C4, 0x3F6C,
		0x3F6C, 0x0A2C, 0x0700, 0x3F68,
		0x3F64, 0x09F8, 0x0740, 0x3F64,
		0x3F60, 0x09C4, 0x077C, 0x3F60,
		0x3F5C, 0x098C, 0x07BC, 0x3F5C,
		0x3F58, 0x0958, 0x07F8, 0x3F58,
		0x3F58, 0x091C, 0x0834, 0x3F58,
		0x3F54, 0x08E4, 0x0870, 0x3F58,
		0x3F54, 0x08AC, 0x08AC, 0x3F54
};

//=========================================
// <num_taps>    = 4
// <num_phases>  = 64
// <scale_ratio> = 1.49999 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_4tap_64p_149[132] = {
		0x02B8, 0x0A90, 0x02B8, 0x0000,
		0x0294, 0x0A94, 0x02DC, 0x3FFC,
		0x0274, 0x0A94, 0x0300, 0x3FF8,
		0x0250, 0x0A94, 0x0328, 0x3FF4,
		0x0230, 0x0A90, 0x0350, 0x3FF0,
		0x0214, 0x0A8C, 0x0374, 0x3FEC,
		0x01F0, 0x0A88, 0x03A0, 0x3FE8,
		0x01D4, 0x0A80, 0x03C8, 0x3FE4,
		0x01B8, 0x0A78, 0x03F0, 0x3FE0,
		0x0198, 0x0A70, 0x041C, 0x3FDC,
		0x0180, 0x0A64, 0x0444, 0x3FD8,
		0x0164, 0x0A54, 0x0470, 0x3FD8,
		0x0148, 0x0A48, 0x049C, 0x3FD4,
		0x0130, 0x0A38, 0x04C8, 0x3FD0,
		0x0118, 0x0A24, 0x04F4, 0x3FD0,
		0x0100, 0x0A14, 0x0520, 0x3FCC,
		0x00E8, 0x0A00, 0x054C, 0x3FCC,
		0x00D4, 0x09E8, 0x057C, 0x3FC8,
		0x00C0, 0x09D0, 0x05A8, 0x3FC8,
		0x00AC, 0x09B8, 0x05D4, 0x3FC8,
		0x0098, 0x09A0, 0x0600, 0x3FC8,
		0x0084, 0x0984, 0x0630, 0x3FC8,
		0x0074, 0x0964, 0x065C, 0x3FCC,
		0x0064, 0x0948, 0x0688, 0x3FCC,
		0x0054, 0x0928, 0x06B4, 0x3FD0,
		0x0044, 0x0908, 0x06E0, 0x3FD4,
		0x0038, 0x08E8, 0x070C, 0x3FD4,
		0x002C, 0x08C4, 0x0738, 0x3FD8,
		0x001C, 0x08A4, 0x0760, 0x3FE0,
		0x0014, 0x087C, 0x078C, 0x3FE4,
		0x0008, 0x0858, 0x07B4, 0x3FEC,
		0x0000, 0x0830, 0x07DC, 0x3FF4,
		0x3FFC, 0x0804, 0x0804, 0x3FFC
};

//=========================================
// <num_taps>    = 4
// <num_phases>  = 64
// <scale_ratio> = 1.83332 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_4tap_64p_183[132] = {
		0x03B0, 0x08A0, 0x03B0, 0x0000,
		0x0394, 0x08A0, 0x03CC, 0x0000,
		0x037C, 0x089C, 0x03E8, 0x0000,
		0x0360, 0x089C, 0x0400, 0x0004,
		0x0348, 0x0898, 0x041C, 0x0004,
		0x032C, 0x0894, 0x0438, 0x0008,
		0x0310, 0x0890, 0x0454, 0x000C,
		0x02F8, 0x0888, 0x0474, 0x000C,
		0x02DC, 0x0884, 0x0490, 0x0010,
		0x02C4, 0x087C, 0x04AC, 0x0014,
		0x02AC, 0x0874, 0x04C8, 0x0018,
		0x0290, 0x086C, 0x04E4, 0x0020,
		0x0278, 0x0864, 0x0500, 0x0024,
		0x0264, 0x0858, 0x051C, 0x0028,
		0x024C, 0x084C, 0x0538, 0x0030,
		0x0234, 0x0844, 0x0554, 0x0034,
		0x021C, 0x0838, 0x0570, 0x003C,
		0x0208, 0x0828, 0x058C, 0x0044,
		0x01F0, 0x081C, 0x05A8, 0x004C,
		0x01DC, 0x080C, 0x05C4, 0x0054,
		0x01C8, 0x07FC, 0x05E0, 0x005C,
		0x01B4, 0x07EC, 0x05FC, 0x0064,
		0x019C, 0x07DC, 0x0618, 0x0070,
		0x018C, 0x07CC, 0x0630, 0x0078,
		0x0178, 0x07B8, 0x064C, 0x0084,
		0x0164, 0x07A8, 0x0664, 0x0090,
		0x0150, 0x0794, 0x0680, 0x009C,
		0x0140, 0x0780, 0x0698, 0x00A8,
		0x0130, 0x076C, 0x06B0, 0x00B4,
		0x0120, 0x0758, 0x06C8, 0x00C0,
		0x0110, 0x0740, 0x06E0, 0x00D0,
		0x0100, 0x072C, 0x06F8, 0x00DC,
		0x00F0, 0x0714, 0x0710, 0x00EC
};

//=========================================
// <num_taps>    = 5
// <num_phases>  = 64
// <scale_ratio> = 0.83333 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_5tap_64p_upscale[165] = {
		0x3E40, 0x09C0, 0x09C0, 0x3E40, 0x0000,
		0x3E50, 0x0964, 0x0A18, 0x3E34, 0x0000,
		0x3E5C, 0x0908, 0x0A6C, 0x3E2C, 0x0004,
		0x3E6C, 0x08AC, 0x0AC0, 0x3E20, 0x0008,
		0x3E78, 0x0850, 0x0B14, 0x3E18, 0x000C,
		0x3E88, 0x07F4, 0x0B60, 0x3E14, 0x0010,
		0x3E98, 0x0798, 0x0BB0, 0x3E0C, 0x0014,
		0x3EA8, 0x073C, 0x0C00, 0x3E08, 0x0014,
		0x3EB8, 0x06E4, 0x0C48, 0x3E04, 0x0018,
		0x3ECC, 0x0684, 0x0C90, 0x3E04, 0x001C,
		0x3EDC, 0x062C, 0x0CD4, 0x3E04, 0x0020,
		0x3EEC, 0x05D4, 0x0D1C, 0x3E04, 0x0020,
		0x3EFC, 0x057C, 0x0D5C, 0x3E08, 0x0024,
		0x3F0C, 0x0524, 0x0D98, 0x3E10, 0x0028,
		0x3F20, 0x04CC, 0x0DD8, 0x3E14, 0x0028,
		0x3F30, 0x0478, 0x0E14, 0x3E1C, 0x0028,
		0x3F40, 0x0424, 0x0E48, 0x3E28, 0x002C,
		0x3F50, 0x03D4, 0x0E7C, 0x3E34, 0x002C,
		0x3F60, 0x0384, 0x0EAC, 0x3E44, 0x002C,
		0x3F6C, 0x0338, 0x0EDC, 0x3E54, 0x002C,
		0x3F7C, 0x02E8, 0x0F08, 0x3E68, 0x002C,
		0x3F8C, 0x02A0, 0x0F2C, 0x3E7C, 0x002C,
		0x3F98, 0x0258, 0x0F50, 0x3E94, 0x002C,
		0x3FA4, 0x0210, 0x0F74, 0x3EB0, 0x0028,
		0x3FB0, 0x01CC, 0x0F90, 0x3ECC, 0x0028,
		0x3FC0, 0x018C, 0x0FA8, 0x3EE8, 0x0024,
		0x3FC8, 0x014C, 0x0FC0, 0x3F0C, 0x0020,
		0x3FD4, 0x0110, 0x0FD4, 0x3F2C, 0x001C,
		0x3FE0, 0x00D4, 0x0FE0, 0x3F54, 0x0018,
		0x3FE8, 0x009C, 0x0FF0, 0x3F7C, 0x0010,
		0x3FF0, 0x0064, 0x0FFC, 0x3FA4, 0x000C,
		0x3FFC, 0x0030, 0x0FFC, 0x3FD4, 0x0004,
		0x0000, 0x0000, 0x1000, 0x0000, 0x0000
};

//=========================================
// <num_taps>    = 5
// <num_phases>  = 64
// <scale_ratio> = 1.16666 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_5tap_64p_116[165] = {
		0x3EDC, 0x0924, 0x0924, 0x3EDC, 0x0000,
		0x3ED8, 0x08EC, 0x095C, 0x3EE0, 0x0000,
		0x3ED4, 0x08B0, 0x0994, 0x3EE8, 0x0000,
		0x3ED0, 0x0878, 0x09C8, 0x3EF0, 0x0000,
		0x3ED0, 0x083C, 0x09FC, 0x3EF8, 0x0000,
		0x3ED0, 0x0800, 0x0A2C, 0x3F04, 0x0000,
		0x3ED0, 0x07C4, 0x0A5C, 0x3F10, 0x0000,
		0x3ED0, 0x0788, 0x0A8C, 0x3F1C, 0x0000,
		0x3ED0, 0x074C, 0x0AC0, 0x3F28, 0x3FFC,
		0x3ED4, 0x0710, 0x0AE8, 0x3F38, 0x3FFC,
		0x3ED8, 0x06D0, 0x0B18, 0x3F48, 0x3FF8,
		0x3EDC, 0x0694, 0x0B3C, 0x3F5C, 0x3FF8,
		0x3EE0, 0x0658, 0x0B68, 0x3F6C, 0x3FF4,
		0x3EE4, 0x061C, 0x0B90, 0x3F80, 0x3FF0,
		0x3EEC, 0x05DC, 0x0BB4, 0x3F98, 0x3FEC,
		0x3EF0, 0x05A0, 0x0BD8, 0x3FB0, 0x3FE8,
		0x3EF8, 0x0564, 0x0BF8, 0x3FC8, 0x3FE4,
		0x3EFC, 0x0528, 0x0C1C, 0x3FE0, 0x3FE0,
		0x3F04, 0x04EC, 0x0C38, 0x3FFC, 0x3FDC,
		0x3F0C, 0x04B4, 0x0C54, 0x0014, 0x3FD8,
		0x3F14, 0x047C, 0x0C70, 0x0030, 0x3FD0,
		0x3F1C, 0x0440, 0x0C88, 0x0050, 0x3FCC,
		0x3F24, 0x0408, 0x0CA0, 0x0070, 0x3FC4,
		0x3F2C, 0x03D0, 0x0CB0, 0x0094, 0x3FC0,
		0x3F34, 0x0398, 0x0CC4, 0x00B8, 0x3FB8,
		0x3F3C, 0x0364, 0x0CD4, 0x00DC, 0x3FB0,
		0x3F48, 0x032C, 0x0CE0, 0x0100, 0x3FAC,
		0x3F50, 0x02F8, 0x0CEC, 0x0128, 0x3FA4,
		0x3F58, 0x02C4, 0x0CF8, 0x0150, 0x3F9C,
		0x3F60, 0x0290, 0x0D00, 0x017C, 0x3F94,
		0x3F68, 0x0260, 0x0D04, 0x01A8, 0x3F8C,
		0x3F74, 0x0230, 0x0D04, 0x01D4, 0x3F84,
		0x3F7C, 0x0200, 0x0D08, 0x0200, 0x3F7C
};

//=========================================
// <num_taps>    = 5
// <num_phases>  = 64
// <scale_ratio> = 1.49999 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_5tap_64p_149[165] = {
		0x3FF4, 0x080C, 0x080C, 0x3FF4, 0x0000,
		0x3FE8, 0x07E8, 0x0830, 0x0000, 0x0000,
		0x3FDC, 0x07C8, 0x0850, 0x0010, 0x3FFC,
		0x3FD0, 0x07A4, 0x0878, 0x001C, 0x3FF8,
		0x3FC4, 0x0780, 0x0898, 0x0030, 0x3FF4,
		0x3FB8, 0x075C, 0x08B8, 0x0040, 0x3FF4,
		0x3FB0, 0x0738, 0x08D8, 0x0050, 0x3FF0,
		0x3FA8, 0x0710, 0x08F8, 0x0064, 0x3FEC,
		0x3FA0, 0x06EC, 0x0914, 0x0078, 0x3FE8,
		0x3F98, 0x06C4, 0x0934, 0x008C, 0x3FE4,
		0x3F90, 0x06A0, 0x094C, 0x00A4, 0x3FE0,
		0x3F8C, 0x0678, 0x0968, 0x00B8, 0x3FDC,
		0x3F84, 0x0650, 0x0984, 0x00D0, 0x3FD8,
		0x3F80, 0x0628, 0x099C, 0x00E8, 0x3FD4,
		0x3F7C, 0x0600, 0x09B8, 0x0100, 0x3FCC,
		0x3F78, 0x05D8, 0x09D0, 0x0118, 0x3FC8,
		0x3F74, 0x05B0, 0x09E4, 0x0134, 0x3FC4,
		0x3F70, 0x0588, 0x09F8, 0x0150, 0x3FC0,
		0x3F70, 0x0560, 0x0A08, 0x016C, 0x3FBC,
		0x3F6C, 0x0538, 0x0A20, 0x0188, 0x3FB4,
		0x3F6C, 0x0510, 0x0A30, 0x01A4, 0x3FB0,
		0x3F6C, 0x04E8, 0x0A3C, 0x01C4, 0x3FAC,
		0x3F6C, 0x04C0, 0x0A48, 0x01E4, 0x3FA8,
		0x3F6C, 0x0498, 0x0A58, 0x0200, 0x3FA4,
		0x3F6C, 0x0470, 0x0A60, 0x0224, 0x3FA0,
		0x3F6C, 0x0448, 0x0A70, 0x0244, 0x3F98,
		0x3F70, 0x0420, 0x0A78, 0x0264, 0x3F94,
		0x3F70, 0x03F8, 0x0A80, 0x0288, 0x3F90,
		0x3F74, 0x03D4, 0x0A84, 0x02A8, 0x3F8C,
		0x3F74, 0x03AC, 0x0A8C, 0x02CC, 0x3F88,
		0x3F78, 0x0384, 0x0A90, 0x02F0, 0x3F84,
		0x3F7C, 0x0360, 0x0A90, 0x0314, 0x3F80,
		0x3F7C, 0x033C, 0x0A90, 0x033C, 0x3F7C
};

//=========================================
// <num_taps>    = 5
// <num_phases>  = 64
// <scale_ratio> = 1.83332 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_5tap_64p_183[165] = {
		0x0168, 0x069C, 0x0698, 0x0164, 0x0000,
		0x0154, 0x068C, 0x06AC, 0x0174, 0x0000,
		0x0144, 0x0674, 0x06C0, 0x0188, 0x0000,
		0x0138, 0x0664, 0x06D0, 0x0198, 0x3FFC,
		0x0128, 0x0654, 0x06E0, 0x01A8, 0x3FFC,
		0x0118, 0x0640, 0x06F0, 0x01BC, 0x3FFC,
		0x010C, 0x0630, 0x0700, 0x01CC, 0x3FF8,
		0x00FC, 0x061C, 0x0710, 0x01E0, 0x3FF8,
		0x00F0, 0x060C, 0x071C, 0x01F0, 0x3FF8,
		0x00E4, 0x05F4, 0x072C, 0x0204, 0x3FF8,
		0x00D8, 0x05E4, 0x0738, 0x0218, 0x3FF4,
		0x00CC, 0x05D0, 0x0744, 0x022C, 0x3FF4,
		0x00C0, 0x05B8, 0x0754, 0x0240, 0x3FF4,
		0x00B4, 0x05A4, 0x0760, 0x0254, 0x3FF4,
		0x00A8, 0x0590, 0x076C, 0x0268, 0x3FF4,
		0x009C, 0x057C, 0x0778, 0x027C, 0x3FF4,
		0x0094, 0x0564, 0x0780, 0x0294, 0x3FF4,
		0x0088, 0x0550, 0x0788, 0x02A8, 0x3FF8,
		0x0080, 0x0538, 0x0794, 0x02BC, 0x3FF8,
		0x0074, 0x0524, 0x079C, 0x02D4, 0x3FF8,
		0x006C, 0x0510, 0x07A4, 0x02E8, 0x3FF8,
		0x0064, 0x04F4, 0x07AC, 0x0300, 0x3FFC,
		0x005C, 0x04E4, 0x07B0, 0x0314, 0x3FFC,
		0x0054, 0x04C8, 0x07B8, 0x032C, 0x0000,
		0x004C, 0x04B4, 0x07C0, 0x0340, 0x0000,
		0x0044, 0x04A0, 0x07C4, 0x0358, 0x0000,
		0x003C, 0x0488, 0x07C8, 0x0370, 0x0004,
		0x0038, 0x0470, 0x07CC, 0x0384, 0x0008,
		0x0030, 0x045C, 0x07D0, 0x039C, 0x0008,
		0x002C, 0x0444, 0x07D0, 0x03B4, 0x000C,
		0x0024, 0x042C, 0x07D4, 0x03CC, 0x0010,
		0x0020, 0x0414, 0x07D4, 0x03E0, 0x0018,
		0x001C, 0x03FC, 0x07D4, 0x03F8, 0x001C
};

//=========================================
// <num_taps>    = 6
// <num_phases>  = 64
// <scale_ratio> = 0.83333 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_6tap_64p_upscale[198] = {
		0x0000, 0x0000, 0x1000, 0x0000, 0x0000, 0x0000,
		0x000C, 0x3FD0, 0x0FFC, 0x0034, 0x3FF4, 0x0000,
		0x0018, 0x3F9C, 0x0FF8, 0x006C, 0x3FE8, 0x0000,
		0x0024, 0x3F6C, 0x0FF0, 0x00A8, 0x3FD8, 0x0000,
		0x002C, 0x3F44, 0x0FE4, 0x00E4, 0x3FC8, 0x0000,
		0x0038, 0x3F18, 0x0FD4, 0x0124, 0x3FB8, 0x0000,
		0x0040, 0x3EF0, 0x0FC0, 0x0164, 0x3FA8, 0x0004,
		0x0048, 0x3EC8, 0x0FAC, 0x01A8, 0x3F98, 0x0004,
		0x0050, 0x3EA8, 0x0F94, 0x01EC, 0x3F84, 0x0004,
		0x0058, 0x3E84, 0x0F74, 0x0234, 0x3F74, 0x0008,
		0x0060, 0x3E68, 0x0F54, 0x027C, 0x3F60, 0x0008,
		0x0064, 0x3E4C, 0x0F30, 0x02C8, 0x3F4C, 0x000C,
		0x006C, 0x3E30, 0x0F04, 0x0314, 0x3F3C, 0x0010,
		0x0070, 0x3E18, 0x0EDC, 0x0360, 0x3F28, 0x0014,
		0x0074, 0x3E04, 0x0EB0, 0x03B0, 0x3F14, 0x0014,
		0x0078, 0x3DF0, 0x0E80, 0x0400, 0x3F00, 0x0018,
		0x0078, 0x3DE0, 0x0E4C, 0x0454, 0x3EEC, 0x001C,
		0x007C, 0x3DD0, 0x0E14, 0x04A8, 0x3ED8, 0x0020,
		0x007C, 0x3DC4, 0x0DDC, 0x04FC, 0x3EC4, 0x0024,
		0x007C, 0x3DBC, 0x0DA0, 0x0550, 0x3EB0, 0x0028,
		0x0080, 0x3DB4, 0x0D5C, 0x05A8, 0x3E9C, 0x002C,
		0x0080, 0x3DAC, 0x0D1C, 0x0600, 0x3E88, 0x0030,
		0x007C, 0x3DA8, 0x0CDC, 0x0658, 0x3E74, 0x0034,
		0x007C, 0x3DA4, 0x0C94, 0x06B0, 0x3E64, 0x0038,
		0x007C, 0x3DA4, 0x0C48, 0x0708, 0x3E50, 0x0040,
		0x0078, 0x3DA4, 0x0C00, 0x0760, 0x3E40, 0x0044,
		0x0078, 0x3DA8, 0x0BB4, 0x07B8, 0x3E2C, 0x0048,
		0x0074, 0x3DAC, 0x0B68, 0x0810, 0x3E1C, 0x004C,
		0x0070, 0x3DB4, 0x0B18, 0x0868, 0x3E0C, 0x0050,
		0x006C, 0x3DBC, 0x0AC4, 0x08C4, 0x3DFC, 0x0054,
		0x0068, 0x3DC4, 0x0A74, 0x0918, 0x3DF0, 0x0058,
		0x0068, 0x3DCC, 0x0A20, 0x0970, 0x3DE0, 0x005C,
		0x0064, 0x3DD4, 0x09C8, 0x09C8, 0x3DD4, 0x0064
};

//=========================================
// <num_taps>    = 6
// <num_phases>  = 64
// <scale_ratio> = 1.16666 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_6tap_64p_116[198] = {
		0x3F0C, 0x0240, 0x0D68, 0x0240, 0x3F0C, 0x0000,
		0x3F18, 0x0210, 0x0D64, 0x0274, 0x3F00, 0x0000,
		0x3F24, 0x01E0, 0x0D58, 0x02A8, 0x3EF8, 0x0004,
		0x3F2C, 0x01B0, 0x0D58, 0x02DC, 0x3EEC, 0x0004,
		0x3F38, 0x0180, 0x0D50, 0x0310, 0x3EE0, 0x0008,
		0x3F44, 0x0154, 0x0D40, 0x0348, 0x3ED8, 0x0008,
		0x3F50, 0x0128, 0x0D34, 0x037C, 0x3ECC, 0x000C,
		0x3F5C, 0x00FC, 0x0D20, 0x03B4, 0x3EC4, 0x0010,
		0x3F64, 0x00D4, 0x0D14, 0x03EC, 0x3EB8, 0x0010,
		0x3F70, 0x00AC, 0x0CFC, 0x0424, 0x3EB0, 0x0014,
		0x3F78, 0x0084, 0x0CE8, 0x0460, 0x3EA8, 0x0014,
		0x3F84, 0x0060, 0x0CCC, 0x0498, 0x3EA0, 0x0018,
		0x3F90, 0x003C, 0x0CB4, 0x04D0, 0x3E98, 0x0018,
		0x3F98, 0x0018, 0x0C9C, 0x050C, 0x3E90, 0x0018,
		0x3FA0, 0x3FFC, 0x0C78, 0x0548, 0x3E88, 0x001C,
		0x3FAC, 0x3FDC, 0x0C54, 0x0584, 0x3E84, 0x001C,
		0x3FB4, 0x3FBC, 0x0C3C, 0x05BC, 0x3E7C, 0x001C,
		0x3FBC, 0x3FA0, 0x0C14, 0x05F8, 0x3E78, 0x0020,
		0x3FC4, 0x3F84, 0x0BF0, 0x0634, 0x3E74, 0x0020,
		0x3FCC, 0x3F68, 0x0BCC, 0x0670, 0x3E70, 0x0020,
		0x3FD4, 0x3F50, 0x0BA4, 0x06AC, 0x3E6C, 0x0020,
		0x3FDC, 0x3F38, 0x0B78, 0x06E8, 0x3E6C, 0x0020,
		0x3FE0, 0x3F24, 0x0B50, 0x0724, 0x3E68, 0x0020,
		0x3FE8, 0x3F0C, 0x0B24, 0x0760, 0x3E68, 0x0020,
		0x3FF0, 0x3EFC, 0x0AF4, 0x0798, 0x3E68, 0x0020,
		0x3FF4, 0x3EE8, 0x0AC8, 0x07D4, 0x3E68, 0x0020,
		0x3FFC, 0x3ED8, 0x0A94, 0x0810, 0x3E6C, 0x001C,
		0x0000, 0x3EC8, 0x0A64, 0x0848, 0x3E70, 0x001C,
		0x0000, 0x3EB8, 0x0A38, 0x0880, 0x3E74, 0x001C,
		0x0004, 0x3EAC, 0x0A04, 0x08BC, 0x3E78, 0x0018,
		0x0008, 0x3EA4, 0x09D0, 0x08F4, 0x3E7C, 0x0014,
		0x000C, 0x3E98, 0x0998, 0x092C, 0x3E84, 0x0014,
		0x0010, 0x3E90, 0x0964, 0x0960, 0x3E8C, 0x0010
};

//=========================================
// <num_taps>    = 6
// <num_phases>  = 64
// <scale_ratio> = 1.49999 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_6tap_64p_149[198] = {
		0x3F14, 0x0394, 0x0AB0, 0x0394, 0x3F14, 0x0000,
		0x3F18, 0x036C, 0x0AB0, 0x03B8, 0x3F14, 0x0000,
		0x3F18, 0x0348, 0x0AAC, 0x03E0, 0x3F14, 0x0000,
		0x3F1C, 0x0320, 0x0AAC, 0x0408, 0x3F10, 0x0000,
		0x3F20, 0x02FC, 0x0AA8, 0x042C, 0x3F10, 0x0000,
		0x3F24, 0x02D8, 0x0AA0, 0x0454, 0x3F10, 0x0000,
		0x3F28, 0x02B4, 0x0A98, 0x047C, 0x3F10, 0x0000,
		0x3F28, 0x0290, 0x0A90, 0x04A4, 0x3F14, 0x0000,
		0x3F30, 0x026C, 0x0A84, 0x04CC, 0x3F14, 0x0000,
		0x3F34, 0x024C, 0x0A7C, 0x04F4, 0x3F14, 0x3FFC,
		0x3F38, 0x0228, 0x0A70, 0x051C, 0x3F18, 0x3FFC,
		0x3F3C, 0x0208, 0x0A64, 0x0544, 0x3F1C, 0x3FF8,
		0x3F40, 0x01E8, 0x0A54, 0x056C, 0x3F20, 0x3FF8,
		0x3F44, 0x01C8, 0x0A48, 0x0594, 0x3F24, 0x3FF4,
		0x3F4C, 0x01A8, 0x0A34, 0x05BC, 0x3F28, 0x3FF4,
		0x3F50, 0x0188, 0x0A28, 0x05E4, 0x3F2C, 0x3FF0,
		0x3F54, 0x016C, 0x0A10, 0x060C, 0x3F34, 0x3FF0,
		0x3F5C, 0x014C, 0x09FC, 0x0634, 0x3F3C, 0x3FEC,
		0x3F60, 0x0130, 0x09EC, 0x065C, 0x3F40, 0x3FE8,
		0x3F68, 0x0114, 0x09D0, 0x0684, 0x3F48, 0x3FE8,
		0x3F6C, 0x00F8, 0x09B8, 0x06AC, 0x3F54, 0x3FE4,
		0x3F74, 0x00E0, 0x09A0, 0x06D0, 0x3F5C, 0x3FE0,
		0x3F78, 0x00C4, 0x098C, 0x06F8, 0x3F64, 0x3FDC,
		0x3F7C, 0x00AC, 0x0970, 0x0720, 0x3F70, 0x3FD8,
		0x3F84, 0x0094, 0x0954, 0x0744, 0x3F7C, 0x3FD4,
		0x3F88, 0x007C, 0x093C, 0x0768, 0x3F88, 0x3FD0,
		0x3F90, 0x0064, 0x091C, 0x0790, 0x3F94, 0x3FCC,
		0x3F94, 0x0050, 0x08FC, 0x07B4, 0x3FA4, 0x3FC8,
		0x3F98, 0x003C, 0x08E0, 0x07D8, 0x3FB0, 0x3FC4,
		0x3FA0, 0x0024, 0x08C0, 0x07FC, 0x3FC0, 0x3FC0,
		0x3FA4, 0x0014, 0x08A4, 0x081C, 0x3FD0, 0x3FB8,
		0x3FAC, 0x0000, 0x0880, 0x0840, 0x3FE0, 0x3FB4,
		0x3FB0, 0x3FF0, 0x0860, 0x0860, 0x3FF0, 0x3FB0
};

//=========================================
// <num_taps>    = 6
// <num_phases>  = 64
// <scale_ratio> = 1.83332 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_6tap_64p_183[198] = {
		0x002C, 0x0420, 0x076C, 0x041C, 0x002C, 0x0000,
		0x0028, 0x040C, 0x0768, 0x0430, 0x0034, 0x0000,
		0x0020, 0x03F8, 0x0768, 0x0448, 0x003C, 0x3FFC,
		0x0018, 0x03E4, 0x0768, 0x045C, 0x0044, 0x3FFC,
		0x0014, 0x03D0, 0x0768, 0x0470, 0x004C, 0x3FF8,
		0x000C, 0x03BC, 0x0764, 0x0484, 0x0058, 0x3FF8,
		0x0008, 0x03A4, 0x0764, 0x049C, 0x0060, 0x3FF4,
		0x0004, 0x0390, 0x0760, 0x04B0, 0x0068, 0x3FF4,
		0x0000, 0x037C, 0x0760, 0x04C4, 0x0070, 0x3FF0,
		0x3FFC, 0x0364, 0x075C, 0x04D8, 0x007C, 0x3FF0,
		0x3FF8, 0x0350, 0x0758, 0x04F0, 0x0084, 0x3FEC,
		0x3FF4, 0x033C, 0x0750, 0x0504, 0x0090, 0x3FEC,
		0x3FF0, 0x0328, 0x074C, 0x0518, 0x009C, 0x3FE8,
		0x3FEC, 0x0314, 0x0744, 0x052C, 0x00A8, 0x3FE8,
		0x3FE8, 0x0304, 0x0740, 0x0540, 0x00B0, 0x3FE4,
		0x3FE4, 0x02EC, 0x073C, 0x0554, 0x00BC, 0x3FE4,
		0x3FE0, 0x02DC, 0x0734, 0x0568, 0x00C8, 0x3FE0,
		0x3FE0, 0x02C4, 0x072C, 0x057C, 0x00D4, 0x3FE0,
		0x3FDC, 0x02B4, 0x0724, 0x058C, 0x00E4, 0x3FDC,
		0x3FDC, 0x02A0, 0x0718, 0x05A0, 0x00F0, 0x3FDC,
		0x3FD8, 0x028C, 0x0714, 0x05B4, 0x00FC, 0x3FD8,
		0x3FD8, 0x0278, 0x0704, 0x05C8, 0x010C, 0x3FD8,
		0x3FD4, 0x0264, 0x0700, 0x05D8, 0x0118, 0x3FD8,
		0x3FD4, 0x0254, 0x06F0, 0x05EC, 0x0128, 0x3FD4,
		0x3FD0, 0x0244, 0x06E8, 0x05FC, 0x0134, 0x3FD4,
		0x3FD0, 0x0230, 0x06DC, 0x060C, 0x0144, 0x3FD4,
		0x3FD0, 0x021C, 0x06D0, 0x0620, 0x0154, 0x3FD0,
		0x3FD0, 0x0208, 0x06C4, 0x0630, 0x0164, 0x3FD0,
		0x3FD0, 0x01F8, 0x06B8, 0x0640, 0x0170, 0x3FD0,
		0x3FCC, 0x01E8, 0x06AC, 0x0650, 0x0180, 0x3FD0,
		0x3FCC, 0x01D8, 0x069C, 0x0660, 0x0190, 0x3FD0,
		0x3FCC, 0x01C4, 0x068C, 0x0670, 0x01A4, 0x3FD0,
		0x3FCC, 0x01B8, 0x0680, 0x067C, 0x01B4, 0x3FCC
};

//=========================================
// <num_taps>    = 7
// <num_phases>  = 64
// <scale_ratio> = 0.83333 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_7tap_64p_upscale[231] = {
		0x00B0, 0x3D98, 0x09BC, 0x09B8, 0x3D94, 0x00B0, 0x0000,
		0x00AC, 0x3DA0, 0x0968, 0x0A10, 0x3D88, 0x00B4, 0x0000,
		0x00A8, 0x3DAC, 0x0914, 0x0A60, 0x3D80, 0x00B8, 0x0000,
		0x00A4, 0x3DB8, 0x08C0, 0x0AB4, 0x3D78, 0x00BC, 0x3FFC,
		0x00A0, 0x3DC8, 0x0868, 0x0B00, 0x3D74, 0x00C0, 0x3FFC,
		0x0098, 0x3DD8, 0x0818, 0x0B54, 0x3D6C, 0x00C0, 0x3FF8,
		0x0094, 0x3DE8, 0x07C0, 0x0B9C, 0x3D6C, 0x00C4, 0x3FF8,
		0x008C, 0x3DFC, 0x0768, 0x0BEC, 0x3D68, 0x00C4, 0x3FF8,
		0x0088, 0x3E0C, 0x0714, 0x0C38, 0x3D68, 0x00C4, 0x3FF4,
		0x0080, 0x3E20, 0x06BC, 0x0C80, 0x3D6C, 0x00C4, 0x3FF4,
		0x0078, 0x3E34, 0x0668, 0x0CC4, 0x3D70, 0x00C4, 0x3FF4,
		0x0074, 0x3E48, 0x0610, 0x0D08, 0x3D78, 0x00C4, 0x3FF0,
		0x006C, 0x3E5C, 0x05BC, 0x0D48, 0x3D80, 0x00C4, 0x3FF0,
		0x0068, 0x3E74, 0x0568, 0x0D84, 0x3D88, 0x00C0, 0x3FF0,
		0x0060, 0x3E88, 0x0514, 0x0DC8, 0x3D94, 0x00BC, 0x3FEC,
		0x0058, 0x3E9C, 0x04C0, 0x0E04, 0x3DA4, 0x00B8, 0x3FEC,
		0x0054, 0x3EB4, 0x046C, 0x0E38, 0x3DB4, 0x00B4, 0x3FEC,
		0x004C, 0x3ECC, 0x0418, 0x0E6C, 0x3DC8, 0x00B0, 0x3FEC,
		0x0044, 0x3EE0, 0x03C8, 0x0EA4, 0x3DDC, 0x00A8, 0x3FEC,
		0x0040, 0x3EF8, 0x0378, 0x0ED0, 0x3DF4, 0x00A0, 0x3FEC,
		0x0038, 0x3F0C, 0x032C, 0x0EFC, 0x3E10, 0x0098, 0x3FEC,
		0x0034, 0x3F24, 0x02DC, 0x0F24, 0x3E2C, 0x0090, 0x3FEC,
		0x002C, 0x3F38, 0x0294, 0x0F4C, 0x3E48, 0x0088, 0x3FEC,
		0x0028, 0x3F50, 0x0248, 0x0F68, 0x3E6C, 0x007C, 0x3FF0,
		0x0020, 0x3F64, 0x0200, 0x0F88, 0x3E90, 0x0074, 0x3FF0,
		0x001C, 0x3F7C, 0x01B8, 0x0FA4, 0x3EB4, 0x0068, 0x3FF0,
		0x0018, 0x3F90, 0x0174, 0x0FBC, 0x3EDC, 0x0058, 0x3FF4,
		0x0014, 0x3FA4, 0x0130, 0x0FD0, 0x3F08, 0x004C, 0x3FF4,
		0x000C, 0x3FB8, 0x00F0, 0x0FE4, 0x3F34, 0x003C, 0x3FF8,
		0x0008, 0x3FCC, 0x00B0, 0x0FF0, 0x3F64, 0x0030, 0x3FF8,
		0x0004, 0x3FDC, 0x0070, 0x0FFC, 0x3F98, 0x0020, 0x3FFC,
		0x0000, 0x3FF0, 0x0038, 0x0FFC, 0x3FCC, 0x0010, 0x0000,
		0x0000, 0x0000, 0x0000, 0x1000, 0x0000, 0x0000, 0x0000
};

//=========================================
// <num_taps>    = 7
// <num_phases>  = 64
// <scale_ratio> = 1.16666 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_7tap_64p_116[231] = {
		0x0020, 0x3E58, 0x0988, 0x0988, 0x3E58, 0x0020, 0x0000,
		0x0024, 0x3E4C, 0x0954, 0x09C0, 0x3E64, 0x0018, 0x0000,
		0x002C, 0x3E44, 0x091C, 0x09F4, 0x3E70, 0x0010, 0x0000,
		0x0030, 0x3E3C, 0x08E8, 0x0A24, 0x3E80, 0x0008, 0x0000,
		0x0034, 0x3E34, 0x08AC, 0x0A5C, 0x3E90, 0x0000, 0x0000,
		0x003C, 0x3E30, 0x0870, 0x0A84, 0x3EA0, 0x3FFC, 0x0004,
		0x0040, 0x3E28, 0x0838, 0x0AB4, 0x3EB4, 0x3FF4, 0x0004,
		0x0044, 0x3E24, 0x07FC, 0x0AE4, 0x3EC8, 0x3FEC, 0x0004,
		0x0048, 0x3E24, 0x07C4, 0x0B08, 0x3EDC, 0x3FE4, 0x0008,
		0x0048, 0x3E20, 0x0788, 0x0B3C, 0x3EF4, 0x3FD8, 0x0008,
		0x004C, 0x3E20, 0x074C, 0x0B60, 0x3F0C, 0x3FD0, 0x000C,
		0x0050, 0x3E20, 0x0710, 0x0B8C, 0x3F24, 0x3FC4, 0x000C,
		0x0050, 0x3E20, 0x06D4, 0x0BB0, 0x3F40, 0x3FBC, 0x0010,
		0x0054, 0x3E24, 0x0698, 0x0BD4, 0x3F5C, 0x3FB0, 0x0010,
		0x0054, 0x3E24, 0x065C, 0x0BFC, 0x3F78, 0x3FA4, 0x0014,
		0x0054, 0x3E28, 0x0624, 0x0C1C, 0x3F98, 0x3F98, 0x0014,
		0x0058, 0x3E2C, 0x05E4, 0x0C3C, 0x3FB8, 0x3F8C, 0x0018,
		0x0058, 0x3E34, 0x05A8, 0x0C58, 0x3FD8, 0x3F80, 0x001C,
		0x0058, 0x3E38, 0x0570, 0x0C78, 0x3FF8, 0x3F74, 0x001C,
		0x0058, 0x3E40, 0x0534, 0x0C94, 0x0018, 0x3F68, 0x0020,
		0x0058, 0x3E48, 0x04F4, 0x0CAC, 0x0040, 0x3F5C, 0x0024,
		0x0058, 0x3E50, 0x04BC, 0x0CC4, 0x0064, 0x3F50, 0x0024,
		0x0054, 0x3E58, 0x0484, 0x0CD8, 0x008C, 0x3F44, 0x0028,
		0x0054, 0x3E60, 0x0448, 0x0CEC, 0x00B4, 0x3F38, 0x002C,
		0x0054, 0x3E68, 0x0410, 0x0CFC, 0x00E0, 0x3F28, 0x0030,
		0x0054, 0x3E74, 0x03D4, 0x0D0C, 0x010C, 0x3F1C, 0x0030,
		0x0050, 0x3E7C, 0x03A0, 0x0D18, 0x0138, 0x3F10, 0x0034,
		0x0050, 0x3E88, 0x0364, 0x0D24, 0x0164, 0x3F04, 0x0038,
		0x004C, 0x3E94, 0x0330, 0x0D30, 0x0194, 0x3EF4, 0x0038,
		0x004C, 0x3EA0, 0x02F8, 0x0D34, 0x01C4, 0x3EE8, 0x003C,
		0x0048, 0x3EAC, 0x02C0, 0x0D3C, 0x01F4, 0x3EDC, 0x0040,
		0x0048, 0x3EB8, 0x0290, 0x0D3C, 0x0224, 0x3ED0, 0x0040,
		0x0044, 0x3EC4, 0x0258, 0x0D40, 0x0258, 0x3EC4, 0x0044
};

//=========================================
// <num_taps>    = 7
// <num_phases>  = 64
// <scale_ratio> = 1.49999 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_7tap_64p_149[231] = {
		0x3F68, 0x3FEC, 0x08A8, 0x08AC, 0x3FF0, 0x3F68, 0x0000,
		0x3F70, 0x3FDC, 0x0888, 0x08CC, 0x0000, 0x3F60, 0x0000,
		0x3F74, 0x3FC8, 0x0868, 0x08F0, 0x0014, 0x3F58, 0x0000,
		0x3F7C, 0x3FB4, 0x0844, 0x0908, 0x002C, 0x3F54, 0x0004,
		0x3F84, 0x3FA4, 0x0820, 0x0924, 0x0044, 0x3F4C, 0x0004,
		0x3F88, 0x3F90, 0x0800, 0x0944, 0x005C, 0x3F44, 0x0004,
		0x3F90, 0x3F80, 0x07D8, 0x095C, 0x0074, 0x3F40, 0x0008,
		0x3F98, 0x3F70, 0x07B0, 0x097C, 0x008C, 0x3F38, 0x0008,
		0x3F9C, 0x3F60, 0x0790, 0x0994, 0x00A8, 0x3F30, 0x0008,
		0x3FA4, 0x3F54, 0x0764, 0x09B0, 0x00C4, 0x3F28, 0x0008,
		0x3FA8, 0x3F48, 0x0740, 0x09C4, 0x00DC, 0x3F24, 0x000C,
		0x3FB0, 0x3F38, 0x0718, 0x09DC, 0x00FC, 0x3F1C, 0x000C,
		0x3FB4, 0x3F2C, 0x06F0, 0x09F4, 0x0118, 0x3F18, 0x000C,
		0x3FBC, 0x3F24, 0x06C8, 0x0A08, 0x0134, 0x3F10, 0x000C,
		0x3FC0, 0x3F18, 0x06A0, 0x0A1C, 0x0154, 0x3F08, 0x0010,
		0x3FC8, 0x3F10, 0x0678, 0x0A2C, 0x0170, 0x3F04, 0x0010,
		0x3FCC, 0x3F04, 0x0650, 0x0A40, 0x0190, 0x3F00, 0x0010,
		0x3FD0, 0x3EFC, 0x0628, 0x0A54, 0x01B0, 0x3EF8, 0x0010,
		0x3FD4, 0x3EF4, 0x0600, 0x0A64, 0x01D0, 0x3EF4, 0x0010,
		0x3FDC, 0x3EEC, 0x05D8, 0x0A6C, 0x01F4, 0x3EF0, 0x0010,
		0x3FE0, 0x3EE8, 0x05B0, 0x0A7C, 0x0214, 0x3EE8, 0x0010,
		0x3FE4, 0x3EE0, 0x0588, 0x0A88, 0x0238, 0x3EE4, 0x0010,
		0x3FE8, 0x3EDC, 0x055C, 0x0A98, 0x0258, 0x3EE0, 0x0010,
		0x3FEC, 0x3ED8, 0x0534, 0x0AA0, 0x027C, 0x3EDC, 0x0010,
		0x3FF0, 0x3ED4, 0x050C, 0x0AAC, 0x02A0, 0x3ED8, 0x000C,
		0x3FF4, 0x3ED0, 0x04E4, 0x0AB4, 0x02C4, 0x3ED4, 0x000C,
		0x3FF4, 0x3ECC, 0x04C0, 0x0ABC, 0x02E8, 0x3ED0, 0x000C,
		0x3FF8, 0x3ECC, 0x0494, 0x0AC0, 0x030C, 0x3ED0, 0x000C,
		0x3FFC, 0x3EC8, 0x046C, 0x0AC8, 0x0334, 0x3ECC, 0x0008,
		0x0000, 0x3EC8, 0x0444, 0x0AC8, 0x0358, 0x3ECC, 0x0008,
		0x0000, 0x3EC8, 0x041C, 0x0ACC, 0x0380, 0x3EC8, 0x0008,
		0x0000, 0x3EC8, 0x03F4, 0x0AD0, 0x03A8, 0x3EC8, 0x0004,
		0x0004, 0x3EC8, 0x03CC, 0x0AD0, 0x03CC, 0x3EC8, 0x0004
};

//=========================================
// <num_taps>    = 7
// <num_phases>  = 64
// <scale_ratio> = 1.83332 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_7tap_64p_183[231] = {
		0x3FA4, 0x01E8, 0x0674, 0x0674, 0x01E8, 0x3FA4, 0x0000,
		0x3FA4, 0x01D4, 0x0668, 0x0684, 0x01F8, 0x3FA4, 0x0000,
		0x3FA4, 0x01C4, 0x0658, 0x0690, 0x0208, 0x3FA8, 0x0000,
		0x3FA0, 0x01B4, 0x064C, 0x06A0, 0x021C, 0x3FA8, 0x3FFC,
		0x3FA0, 0x01A4, 0x063C, 0x06AC, 0x022C, 0x3FAC, 0x3FFC,
		0x3FA0, 0x0194, 0x0630, 0x06B4, 0x0240, 0x3FAC, 0x3FFC,
		0x3FA0, 0x0184, 0x0620, 0x06C4, 0x0250, 0x3FB0, 0x3FF8,
		0x3FA0, 0x0174, 0x0614, 0x06CC, 0x0264, 0x3FB0, 0x3FF8,
		0x3FA0, 0x0164, 0x0604, 0x06D8, 0x0278, 0x3FB4, 0x3FF4,
		0x3FA0, 0x0154, 0x05F4, 0x06E4, 0x0288, 0x3FB8, 0x3FF4,
		0x3FA0, 0x0148, 0x05E4, 0x06EC, 0x029C, 0x3FBC, 0x3FF0,
		0x3FA0, 0x0138, 0x05D4, 0x06F4, 0x02B0, 0x3FC0, 0x3FF0,
		0x3FA0, 0x0128, 0x05C4, 0x0704, 0x02C4, 0x3FC0, 0x3FEC,
		0x3FA0, 0x011C, 0x05B4, 0x0708, 0x02D8, 0x3FC4, 0x3FEC,
		0x3FA4, 0x010C, 0x05A4, 0x0714, 0x02E8, 0x3FC8, 0x3FE8,
		0x3FA4, 0x0100, 0x0590, 0x0718, 0x02FC, 0x3FD0, 0x3FE8,
		0x3FA4, 0x00F0, 0x0580, 0x0724, 0x0310, 0x3FD4, 0x3FE4,
		0x3FA4, 0x00E4, 0x056C, 0x072C, 0x0324, 0x3FD8, 0x3FE4,
		0x3FA8, 0x00D8, 0x055C, 0x0730, 0x0338, 0x3FDC, 0x3FE0,
		0x3FA8, 0x00CC, 0x0548, 0x0738, 0x034C, 0x3FE4, 0x3FDC,
		0x3FA8, 0x00BC, 0x0538, 0x0740, 0x0360, 0x3FE8, 0x3FDC,
		0x3FAC, 0x00B0, 0x0528, 0x0744, 0x0374, 0x3FEC, 0x3FD8,
		0x3FAC, 0x00A4, 0x0514, 0x0748, 0x0388, 0x3FF4, 0x3FD8,
		0x3FB0, 0x0098, 0x0500, 0x074C, 0x039C, 0x3FFC, 0x3FD4,
		0x3FB0, 0x0090, 0x04EC, 0x0750, 0x03B0, 0x0000, 0x3FD4,
		0x3FB0, 0x0084, 0x04DC, 0x0758, 0x03C4, 0x0004, 0x3FD0,
		0x3FB4, 0x0078, 0x04CC, 0x0758, 0x03D8, 0x000C, 0x3FCC,
		0x3FB4, 0x006C, 0x04B8, 0x075C, 0x03EC, 0x0014, 0x3FCC,
		0x3FB8, 0x0064, 0x04A0, 0x0760, 0x0400, 0x001C, 0x3FC8,
		0x3FB8, 0x0058, 0x0490, 0x0760, 0x0414, 0x0024, 0x3FC8,
		0x3FBC, 0x0050, 0x047C, 0x0760, 0x0428, 0x002C, 0x3FC4,
		0x3FBC, 0x0048, 0x0464, 0x0764, 0x043C, 0x0034, 0x3FC4,
		0x3FC0, 0x003C, 0x0454, 0x0764, 0x0450, 0x003C, 0x3FC0
};

//=========================================
// <num_taps>    = 8
// <num_phases>  = 64
// <scale_ratio> = 0.83333 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_8tap_64p_upscale[264] = {
		0x0000, 0x0000, 0x0000, 0x1000, 0x0000, 0x0000, 0x0000, 0x0000,
		0x3FFC, 0x0014, 0x3FC8, 0x1000, 0x0038, 0x3FEC, 0x0004, 0x0000,
		0x3FF4, 0x0024, 0x3F94, 0x0FFC, 0x0074, 0x3FD8, 0x000C, 0x0000,
		0x3FF0, 0x0038, 0x3F60, 0x0FEC, 0x00B4, 0x3FC4, 0x0014, 0x0000,
		0x3FEC, 0x004C, 0x3F2C, 0x0FE4, 0x00F4, 0x3FAC, 0x0018, 0x0000,
		0x3FE4, 0x005C, 0x3F00, 0x0FD4, 0x0138, 0x3F94, 0x0020, 0x0000,
		0x3FE0, 0x006C, 0x3ED0, 0x0FC4, 0x017C, 0x3F7C, 0x0028, 0x0000,
		0x3FDC, 0x007C, 0x3EA8, 0x0FA4, 0x01C4, 0x3F68, 0x0030, 0x0000,
		0x3FD8, 0x0088, 0x3E80, 0x0F90, 0x020C, 0x3F50, 0x0038, 0x3FFC,
		0x3FD4, 0x0098, 0x3E58, 0x0F70, 0x0258, 0x3F38, 0x0040, 0x3FFC,
		0x3FD0, 0x00A4, 0x3E34, 0x0F54, 0x02A0, 0x3F1C, 0x004C, 0x3FFC,
		0x3FD0, 0x00B0, 0x3E14, 0x0F28, 0x02F0, 0x3F04, 0x0054, 0x3FFC,
		0x3FCC, 0x00BC, 0x3DF4, 0x0F08, 0x033C, 0x3EEC, 0x005C, 0x3FF8,
		0x3FC8, 0x00C8, 0x3DD8, 0x0EDC, 0x038C, 0x3ED4, 0x0064, 0x3FF8,
		0x3FC8, 0x00D0, 0x3DC0, 0x0EAC, 0x03E0, 0x3EBC, 0x006C, 0x3FF4,
		0x3FC4, 0x00D8, 0x3DA8, 0x0E7C, 0x0430, 0x3EA4, 0x0078, 0x3FF4,
		0x3FC4, 0x00E0, 0x3D94, 0x0E48, 0x0484, 0x3E8C, 0x0080, 0x3FF0,
		0x3FC4, 0x00E8, 0x3D80, 0x0E10, 0x04D8, 0x3E74, 0x0088, 0x3FF0,
		0x3FC4, 0x00F0, 0x3D70, 0x0DD8, 0x052C, 0x3E5C, 0x0090, 0x3FEC,
		0x3FC0, 0x00F4, 0x3D60, 0x0DA0, 0x0584, 0x3E44, 0x0098, 0x3FEC,
		0x3FC0, 0x00F8, 0x3D54, 0x0D68, 0x05D8, 0x3E2C, 0x00A0, 0x3FE8,
		0x3FC0, 0x00FC, 0x3D48, 0x0D20, 0x0630, 0x3E18, 0x00AC, 0x3FE8,
		0x3FC0, 0x0100, 0x3D40, 0x0CE0, 0x0688, 0x3E00, 0x00B4, 0x3FE4,
		0x3FC4, 0x0100, 0x3D3C, 0x0C98, 0x06DC, 0x3DEC, 0x00BC, 0x3FE4,
		0x3FC4, 0x0100, 0x3D38, 0x0C58, 0x0734, 0x3DD8, 0x00C0, 0x3FE0,
		0x3FC4, 0x0104, 0x3D38, 0x0C0C, 0x078C, 0x3DC4, 0x00C8, 0x3FDC,
		0x3FC4, 0x0100, 0x3D38, 0x0BC4, 0x07E4, 0x3DB0, 0x00D0, 0x3FDC,
		0x3FC4, 0x0100, 0x3D38, 0x0B78, 0x083C, 0x3DA0, 0x00D8, 0x3FD8,
		0x3FC8, 0x0100, 0x3D3C, 0x0B28, 0x0890, 0x3D90, 0x00DC, 0x3FD8,
		0x3FC8, 0x00FC, 0x3D40, 0x0ADC, 0x08E8, 0x3D80, 0x00E4, 0x3FD4,
		0x3FCC, 0x00FC, 0x3D48, 0x0A84, 0x093C, 0x3D74, 0x00E8, 0x3FD4,
		0x3FCC, 0x00F8, 0x3D50, 0x0A38, 0x0990, 0x3D64, 0x00F0, 0x3FD0,
		0x3FD0, 0x00F4, 0x3D58, 0x09E0, 0x09E4, 0x3D5C, 0x00F4, 0x3FD0
};

//=========================================
// <num_taps>    = 8
// <num_phases>  = 64
// <scale_ratio> = 1.16666 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_8tap_64p_116[264] = {
		0x0080, 0x3E90, 0x0268, 0x0D14, 0x0264, 0x3E90, 0x0080, 0x0000,
		0x007C, 0x3E9C, 0x0238, 0x0D14, 0x0298, 0x3E84, 0x0080, 0x0000,
		0x0078, 0x3EAC, 0x0200, 0x0D10, 0x02D0, 0x3E78, 0x0084, 0x0000,
		0x0078, 0x3EB8, 0x01D0, 0x0D0C, 0x0304, 0x3E6C, 0x0084, 0x0000,
		0x0074, 0x3EC8, 0x01A0, 0x0D00, 0x033C, 0x3E60, 0x0088, 0x0000,
		0x0070, 0x3ED4, 0x0170, 0x0D00, 0x0374, 0x3E54, 0x0088, 0x3FFC,
		0x006C, 0x3EE4, 0x0140, 0x0CF8, 0x03AC, 0x3E48, 0x0088, 0x3FFC,
		0x006C, 0x3EF0, 0x0114, 0x0CE8, 0x03E4, 0x3E3C, 0x008C, 0x3FFC,
		0x0068, 0x3F00, 0x00E8, 0x0CD8, 0x041C, 0x3E34, 0x008C, 0x3FFC,
		0x0064, 0x3F10, 0x00BC, 0x0CCC, 0x0454, 0x3E28, 0x008C, 0x3FFC,
		0x0060, 0x3F1C, 0x0090, 0x0CBC, 0x0490, 0x3E20, 0x008C, 0x3FFC,
		0x005C, 0x3F2C, 0x0068, 0x0CA4, 0x04CC, 0x3E18, 0x008C, 0x3FFC,
		0x0058, 0x3F38, 0x0040, 0x0C94, 0x0504, 0x3E10, 0x008C, 0x3FFC,
		0x0054, 0x3F48, 0x001C, 0x0C7C, 0x0540, 0x3E08, 0x0088, 0x3FFC,
		0x0050, 0x3F54, 0x3FF8, 0x0C60, 0x057C, 0x3E04, 0x0088, 0x3FFC,
		0x004C, 0x3F64, 0x3FD4, 0x0C44, 0x05B8, 0x3DFC, 0x0088, 0x3FFC,
		0x0048, 0x3F70, 0x3FB4, 0x0C28, 0x05F4, 0x3DF8, 0x0084, 0x3FFC,
		0x0044, 0x3F80, 0x3F90, 0x0C0C, 0x0630, 0x3DF4, 0x0080, 0x3FFC,
		0x0040, 0x3F8C, 0x3F70, 0x0BE8, 0x066C, 0x3DF4, 0x0080, 0x3FFC,
		0x003C, 0x3F9C, 0x3F50, 0x0BC8, 0x06A8, 0x3DF0, 0x007C, 0x3FFC,
		0x0038, 0x3FA8, 0x3F34, 0x0BA0, 0x06E4, 0x3DF0, 0x0078, 0x0000,
		0x0034, 0x3FB4, 0x3F18, 0x0B80, 0x071C, 0x3DF0, 0x0074, 0x0000,
		0x0030, 0x3FC0, 0x3EFC, 0x0B5C, 0x0758, 0x3DF0, 0x0070, 0x0000,
		0x002C, 0x3FCC, 0x3EE4, 0x0B34, 0x0794, 0x3DF4, 0x0068, 0x0000,
		0x002C, 0x3FDC, 0x3ECC, 0x0B08, 0x07CC, 0x3DF4, 0x0064, 0x0000,
		0x0028, 0x3FE4, 0x3EB4, 0x0AE0, 0x0808, 0x3DF8, 0x0060, 0x0000,
		0x0024, 0x3FF0, 0x3EA0, 0x0AB0, 0x0840, 0x3E00, 0x0058, 0x0004,
		0x0020, 0x3FFC, 0x3E90, 0x0A84, 0x0878, 0x3E04, 0x0050, 0x0004,
		0x001C, 0x0004, 0x3E7C, 0x0A54, 0x08B0, 0x3E0C, 0x004C, 0x0008,
		0x0018, 0x000C, 0x3E68, 0x0A28, 0x08E8, 0x3E18, 0x0044, 0x0008,
		0x0018, 0x0018, 0x3E54, 0x09F4, 0x0920, 0x3E20, 0x003C, 0x000C,
		0x0014, 0x0020, 0x3E48, 0x09C0, 0x0954, 0x3E2C, 0x0034, 0x0010,
		0x0010, 0x002C, 0x3E3C, 0x098C, 0x0988, 0x3E38, 0x002C, 0x0010
};

//=========================================
// <num_taps>    = 8
// <num_phases>  = 64
// <scale_ratio> = 1.49999 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_8tap_64p_149[264] = {
		0x0008, 0x3E8C, 0x03F8, 0x0AE8, 0x03F8, 0x3E8C, 0x0008, 0x0000,
		0x000C, 0x3E8C, 0x03D0, 0x0AE8, 0x0420, 0x3E90, 0x0000, 0x0000,
		0x000C, 0x3E8C, 0x03AC, 0x0AE8, 0x0444, 0x3E90, 0x0000, 0x0000,
		0x0010, 0x3E90, 0x0384, 0x0AE0, 0x046C, 0x3E94, 0x3FFC, 0x0000,
		0x0014, 0x3E90, 0x035C, 0x0ADC, 0x0494, 0x3E94, 0x3FF8, 0x0004,
		0x0018, 0x3E90, 0x0334, 0x0AD8, 0x04BC, 0x3E98, 0x3FF4, 0x0004,
		0x001C, 0x3E94, 0x0310, 0x0AD0, 0x04E4, 0x3E9C, 0x3FEC, 0x0004,
		0x0020, 0x3E98, 0x02E8, 0x0AC4, 0x050C, 0x3EA0, 0x3FE8, 0x0008,
		0x0020, 0x3E98, 0x02C4, 0x0AC0, 0x0534, 0x3EA4, 0x3FE4, 0x0008,
		0x0024, 0x3E9C, 0x02A0, 0x0AB4, 0x055C, 0x3EAC, 0x3FDC, 0x0008,
		0x0024, 0x3EA0, 0x027C, 0x0AA8, 0x0584, 0x3EB0, 0x3FD8, 0x000C,
		0x0028, 0x3EA4, 0x0258, 0x0A9C, 0x05AC, 0x3EB8, 0x3FD0, 0x000C,
		0x0028, 0x3EA8, 0x0234, 0x0A90, 0x05D4, 0x3EC0, 0x3FC8, 0x0010,
		0x002C, 0x3EAC, 0x0210, 0x0A80, 0x05FC, 0x3EC8, 0x3FC4, 0x0010,
		0x002C, 0x3EB4, 0x01F0, 0x0A70, 0x0624, 0x3ED0, 0x3FBC, 0x0010,
		0x002C, 0x3EB8, 0x01CC, 0x0A60, 0x064C, 0x3EDC, 0x3FB4, 0x0014,
		0x0030, 0x3EBC, 0x01A8, 0x0A50, 0x0674, 0x3EE4, 0x3FB0, 0x0014,
		0x0030, 0x3EC4, 0x0188, 0x0A38, 0x069C, 0x3EF0, 0x3FA8, 0x0018,
		0x0030, 0x3ECC, 0x0168, 0x0A28, 0x06C0, 0x3EFC, 0x3FA0, 0x0018,
		0x0030, 0x3ED0, 0x0148, 0x0A14, 0x06E8, 0x3F08, 0x3F98, 0x001C,
		0x0030, 0x3ED8, 0x012C, 0x0A00, 0x070C, 0x3F14, 0x3F90, 0x001C,
		0x0034, 0x3EE0, 0x0108, 0x09E4, 0x0734, 0x3F24, 0x3F8C, 0x001C,
		0x0034, 0x3EE4, 0x00EC, 0x09CC, 0x0758, 0x3F34, 0x3F84, 0x0020,
		0x0034, 0x3EEC, 0x00D0, 0x09B8, 0x077C, 0x3F40, 0x3F7C, 0x0020,
		0x0034, 0x3EF4, 0x00B4, 0x0998, 0x07A4, 0x3F50, 0x3F74, 0x0024,
		0x0030, 0x3EFC, 0x0098, 0x0980, 0x07C8, 0x3F64, 0x3F6C, 0x0024,
		0x0030, 0x3F04, 0x0080, 0x0968, 0x07E8, 0x3F74, 0x3F64, 0x0024,
		0x0030, 0x3F0C, 0x0060, 0x094C, 0x080C, 0x3F88, 0x3F5C, 0x0028,
		0x0030, 0x3F14, 0x0048, 0x0930, 0x0830, 0x3F98, 0x3F54, 0x0028,
		0x0030, 0x3F1C, 0x0030, 0x0914, 0x0850, 0x3FAC, 0x3F4C, 0x0028,
		0x0030, 0x3F24, 0x0018, 0x08F0, 0x0874, 0x3FC0, 0x3F44, 0x002C,
		0x002C, 0x3F2C, 0x0000, 0x08D4, 0x0894, 0x3FD8, 0x3F3C, 0x002C,
		0x002C, 0x3F34, 0x3FEC, 0x08B4, 0x08B4, 0x3FEC, 0x3F34, 0x002C
};

//=========================================
// <num_taps>    = 8
// <num_phases>  = 64
// <scale_ratio> = 1.83332 (input/output)
// <sharpness>   = 0
// <CoefType>    = ModifiedLanczos
// <CoefQuant>   = 1.10
// <CoefOut>     = 1.12
//=========================================
static const uint16_t filter_8tap_64p_183[264] = {
		0x3F88, 0x0048, 0x047C, 0x0768, 0x047C, 0x0048, 0x3F88, 0x0000,
		0x3F88, 0x003C, 0x0468, 0x076C, 0x0490, 0x0054, 0x3F84, 0x0000,
		0x3F8C, 0x0034, 0x0454, 0x0768, 0x04A4, 0x005C, 0x3F84, 0x0000,
		0x3F8C, 0x0028, 0x0444, 0x076C, 0x04B4, 0x0068, 0x3F80, 0x0000,
		0x3F90, 0x0020, 0x042C, 0x0768, 0x04C8, 0x0074, 0x3F80, 0x0000,
		0x3F90, 0x0018, 0x041C, 0x0764, 0x04DC, 0x0080, 0x3F7C, 0x0000,
		0x3F94, 0x0010, 0x0408, 0x075C, 0x04F0, 0x008C, 0x3F7C, 0x0000,
		0x3F94, 0x0004, 0x03F8, 0x0760, 0x0500, 0x0098, 0x3F7C, 0x3FFC,
		0x3F98, 0x0000, 0x03E0, 0x075C, 0x0514, 0x00A4, 0x3F78, 0x3FFC,
		0x3F9C, 0x3FF8, 0x03CC, 0x0754, 0x0528, 0x00B0, 0x3F78, 0x3FFC,
		0x3F9C, 0x3FF0, 0x03B8, 0x0754, 0x0538, 0x00BC, 0x3F78, 0x3FFC,
		0x3FA0, 0x3FE8, 0x03A4, 0x0750, 0x054C, 0x00CC, 0x3F74, 0x3FF8,
		0x3FA4, 0x3FE0, 0x0390, 0x074C, 0x055C, 0x00D8, 0x3F74, 0x3FF8,
		0x3FA4, 0x3FDC, 0x037C, 0x0744, 0x0570, 0x00E4, 0x3F74, 0x3FF8,
		0x3FA8, 0x3FD4, 0x0368, 0x0740, 0x0580, 0x00F4, 0x3F74, 0x3FF4,
		0x3FA8, 0x3FCC, 0x0354, 0x073C, 0x0590, 0x0104, 0x3F74, 0x3FF4,
		0x3FAC, 0x3FC8, 0x0340, 0x0730, 0x05A4, 0x0110, 0x3F74, 0x3FF4,
		0x3FB0, 0x3FC0, 0x0330, 0x0728, 0x05B4, 0x0120, 0x3F74, 0x3FF0,
		0x3FB0, 0x3FBC, 0x031C, 0x0724, 0x05C4, 0x0130, 0x3F70, 0x3FF0,
		0x3FB4, 0x3FB4, 0x0308, 0x0720, 0x05D4, 0x013C, 0x3F70, 0x3FF0,
		0x3FB8, 0x3FB0, 0x02F4, 0x0714, 0x05E4, 0x014C, 0x3F74, 0x3FEC,
		0x3FB8, 0x3FAC, 0x02E0, 0x0708, 0x05F8, 0x015C, 0x3F74, 0x3FEC,
		0x3FBC, 0x3FA8, 0x02CC, 0x0704, 0x0604, 0x016C, 0x3F74, 0x3FE8,
		0x3FC0, 0x3FA0, 0x02BC, 0x06F8, 0x0614, 0x017C, 0x3F74, 0x3FE8,
		0x3FC0, 0x3F9C, 0x02A8, 0x06F4, 0x0624, 0x018C, 0x3F74, 0x3FE4,
		0x3FC4, 0x3F98, 0x0294, 0x06E8, 0x0634, 0x019C, 0x3F74, 0x3FE4,
		0x3FC8, 0x3F94, 0x0284, 0x06D8, 0x0644, 0x01AC, 0x3F78, 0x3FE0,
		0x3FC8, 0x3F90, 0x0270, 0x06D4, 0x0650, 0x01BC, 0x3F78, 0x3FE0,
		0x3FCC, 0x3F8C, 0x025C, 0x06C8, 0x0660, 0x01D0, 0x3F78, 0x3FDC,
		0x3FCC, 0x3F8C, 0x024C, 0x06B8, 0x066C, 0x01E0, 0x3F7C, 0x3FDC,
		0x3FD0, 0x3F88, 0x0238, 0x06B0, 0x067C, 0x01F0, 0x3F7C, 0x3FD8,
		0x3FD4, 0x3F84, 0x0228, 0x069C, 0x0688, 0x0204, 0x3F80, 0x3FD8,
		0x3FD4, 0x3F84, 0x0214, 0x0694, 0x0694, 0x0214, 0x3F84, 0x3FD4
};

const uint16_t *get_filter_3tap_16p(struct fixed31_32 ratio)
{
	if (ratio.value < dc_fixpt_one.value)
		return filter_3tap_16p_upscale;
	else if (ratio.value < dc_fixpt_from_fraction(4, 3).value)
		return filter_3tap_16p_116;
	else if (ratio.value < dc_fixpt_from_fraction(5, 3).value)
		return filter_3tap_16p_149;
	else
		return filter_3tap_16p_183;
}

const uint16_t *get_filter_3tap_64p(struct fixed31_32 ratio)
{
	if (ratio.value < dc_fixpt_one.value)
		return filter_3tap_64p_upscale;
	else if (ratio.value < dc_fixpt_from_fraction(4, 3).value)
		return filter_3tap_64p_116;
	else if (ratio.value < dc_fixpt_from_fraction(5, 3).value)
		return filter_3tap_64p_149;
	else
		return filter_3tap_64p_183;
}

const uint16_t *get_filter_4tap_16p(struct fixed31_32 ratio)
{
	if (ratio.value < dc_fixpt_one.value)
		return filter_4tap_16p_upscale;
	else if (ratio.value < dc_fixpt_from_fraction(4, 3).value)
		return filter_4tap_16p_116;
	else if (ratio.value < dc_fixpt_from_fraction(5, 3).value)
		return filter_4tap_16p_149;
	else
		return filter_4tap_16p_183;
}

const uint16_t *get_filter_4tap_64p(struct fixed31_32 ratio)
{
	if (ratio.value < dc_fixpt_one.value)
		return filter_4tap_64p_upscale;
	else if (ratio.value < dc_fixpt_from_fraction(4, 3).value)
		return filter_4tap_64p_116;
	else if (ratio.value < dc_fixpt_from_fraction(5, 3).value)
		return filter_4tap_64p_149;
	else
		return filter_4tap_64p_183;
}

const uint16_t *get_filter_5tap_64p(struct fixed31_32 ratio)
{
	if (ratio.value < dc_fixpt_one.value)
		return filter_5tap_64p_upscale;
	else if (ratio.value < dc_fixpt_from_fraction(4, 3).value)
		return filter_5tap_64p_116;
	else if (ratio.value < dc_fixpt_from_fraction(5, 3).value)
		return filter_5tap_64p_149;
	else
		return filter_5tap_64p_183;
}

const uint16_t *get_filter_6tap_64p(struct fixed31_32 ratio)
{
	if (ratio.value < dc_fixpt_one.value)
		return filter_6tap_64p_upscale;
	else if (ratio.value < dc_fixpt_from_fraction(4, 3).value)
		return filter_6tap_64p_116;
	else if (ratio.value < dc_fixpt_from_fraction(5, 3).value)
		return filter_6tap_64p_149;
	else
		return filter_6tap_64p_183;
}

const uint16_t *get_filter_7tap_64p(struct fixed31_32 ratio)
{
	if (ratio.value < dc_fixpt_one.value)
		return filter_7tap_64p_upscale;
	else if (ratio.value < dc_fixpt_from_fraction(4, 3).value)
		return filter_7tap_64p_116;
	else if (ratio.value < dc_fixpt_from_fraction(5, 3).value)
		return filter_7tap_64p_149;
	else
		return filter_7tap_64p_183;
}

const uint16_t *get_filter_8tap_64p(struct fixed31_32 ratio)
{
	if (ratio.value < dc_fixpt_one.value)
		return filter_8tap_64p_upscale;
	else if (ratio.value < dc_fixpt_from_fraction(4, 3).value)
		return filter_8tap_64p_116;
	else if (ratio.value < dc_fixpt_from_fraction(5, 3).value)
		return filter_8tap_64p_149;
	else
		return filter_8tap_64p_183;
}

const uint16_t *get_filter_2tap_16p(void)
{
	return filter_2tap_16p;
}

const uint16_t *get_filter_2tap_64p(void)
{
	return filter_2tap_64p;
}
