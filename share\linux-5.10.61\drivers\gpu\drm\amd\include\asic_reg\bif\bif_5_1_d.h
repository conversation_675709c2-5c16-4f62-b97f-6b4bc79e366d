/*
 * BIF_5_1 Register documentation
 *
 * Copyright (C) 2014  Advanced Micro Devices, Inc.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a
 * copy of this software and associated documentation files (the "Software"),
 * to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included
 * in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
 * THE COPYRIGHT HOLDER(S) BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN
 * AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

#ifndef BIF_5_1_D_H
#define BIF_5_1_D_H

#define mmMM_INDEX                                                              0x0
#define mmMM_INDEX_HI                                                           0x6
#define mmMM_DATA                                                               0x1
#define mmBIF_MM_INDACCESS_CNTL                                                 0x1500
#define mmBUS_CNTL                                                              0x1508
#define mmCONFIG_CNTL                                                           0x1509
#define mmCONFIG_MEMSIZE                                                        0x150a
#define mmCONFIG_F0_BASE                                                        0x150b
#define mmCONFIG_APER_SIZE                                                      0x150c
#define mmCONFIG_REG_APER_SIZE                                                  0x150d
#define mmBIF_SCRATCH0                                                          0x150e
#define mmBIF_SCRATCH1                                                          0x150f
#define mmBX_RESET_EN                                                           0x1514
#define mmMM_CFGREGS_CNTL                                                       0x1513
#define mmHW_DEBUG                                                              0x1515
#define mmMASTER_CREDIT_CNTL                                                    0x1516
#define mmSLAVE_REQ_CREDIT_CNTL                                                 0x1517
#define mmBX_RESET_CNTL                                                         0x1518
#define mmINTERRUPT_CNTL                                                        0x151a
#define mmINTERRUPT_CNTL2                                                       0x151b
#define mmBIF_DEBUG_CNTL                                                        0x151c
#define mmBIF_DEBUG_MUX                                                         0x151d
#define mmBIF_DEBUG_OUT                                                         0x151e
#define mmHDP_REG_COHERENCY_FLUSH_CNTL                                          0x1528
#define mmHDP_MEM_COHERENCY_FLUSH_CNTL                                          0x1520
#define mmCLKREQB_PAD_CNTL                                                      0x1521
#define mmSMBDAT_PAD_CNTL                                                       0x1522
#define mmSMBCLK_PAD_CNTL                                                       0x1523
#define mmBIF_XDMA_LO                                                           0x14c0
#define mmBIF_XDMA_HI                                                           0x14c1
#define mmBIF_FEATURES_CONTROL_MISC                                             0x14c2
#define mmBIF_DOORBELL_CNTL                                                     0x14c3
#define mmBIF_SLVARB_MODE                                                       0x14c4
#define mmBIF_FB_EN                                                             0x1524
#define mmBIF_BUSNUM_CNTL1                                                      0x1525
#define mmBIF_BUSNUM_LIST0                                                      0x1526
#define mmBIF_BUSNUM_LIST1                                                      0x1527
#define mmBIF_BUSNUM_CNTL2                                                      0x152b
#define mmBIF_BUSY_DELAY_CNTR                                                   0x1529
#define mmBIF_PERFMON_CNTL                                                      0x152c
#define mmBIF_PERFCOUNTER0_RESULT                                               0x152d
#define mmBIF_PERFCOUNTER1_RESULT                                               0x152e
#define mmSLAVE_HANG_PROTECTION_CNTL                                            0x1536
#define mmGPU_HDP_FLUSH_REQ                                                     0x1537
#define mmGPU_HDP_FLUSH_DONE                                                    0x1538
#define mmSLAVE_HANG_ERROR                                                      0x153b
#define mmCAPTURE_HOST_BUSNUM                                                   0x153c
#define mmHOST_BUSNUM                                                           0x153d
#define mmPEER_REG_RANGE0                                                       0x153e
#define mmPEER_REG_RANGE1                                                       0x153f
#define mmPEER0_FB_OFFSET_HI                                                    0x14f3
#define mmPEER0_FB_OFFSET_LO                                                    0x14f2
#define mmPEER1_FB_OFFSET_HI                                                    0x14f1
#define mmPEER1_FB_OFFSET_LO                                                    0x14f0
#define mmPEER2_FB_OFFSET_HI                                                    0x14ef
#define mmPEER2_FB_OFFSET_LO                                                    0x14ee
#define mmPEER3_FB_OFFSET_HI                                                    0x14ed
#define mmPEER3_FB_OFFSET_LO                                                    0x14ec
#define mmDBG_BYPASS_SRBM_ACCESS                                                0x14eb
#define mmSMBUS_BACO_DUMMY                                                      0x14c6
#define mmBIF_DEVFUNCNUM_LIST0                                                  0x14e8
#define mmBIF_DEVFUNCNUM_LIST1                                                  0x14e7
#define mmBACO_CNTL                                                             0x14e5
#define mmBF_ANA_ISO_CNTL                                                       0x14c7
#define mmMEM_TYPE_CNTL                                                         0x14e4
#define mmBIF_BACO_DEBUG                                                        0x14df
#define mmBIF_BACO_DEBUG_LATCH                                                  0x14dc
#define mmBACO_CNTL_MISC                                                        0x14db
#define mmSMU_BIF_VDDGFX_PWR_STATUS                                             0x14f8
#define mmBIF_VDDGFX_GFX0_LOWER                                                 0x1428
#define mmBIF_VDDGFX_GFX0_UPPER                                                 0x1429
#define mmBIF_VDDGFX_GFX1_LOWER                                                 0x142a
#define mmBIF_VDDGFX_GFX1_UPPER                                                 0x142b
#define mmBIF_VDDGFX_GFX2_LOWER                                                 0x142c
#define mmBIF_VDDGFX_GFX2_UPPER                                                 0x142d
#define mmBIF_VDDGFX_GFX3_LOWER                                                 0x142e
#define mmBIF_VDDGFX_GFX3_UPPER                                                 0x142f
#define mmBIF_VDDGFX_GFX4_LOWER                                                 0x1430
#define mmBIF_VDDGFX_GFX4_UPPER                                                 0x1431
#define mmBIF_VDDGFX_GFX5_LOWER                                                 0x1432
#define mmBIF_VDDGFX_GFX5_UPPER                                                 0x1433
#define mmBIF_VDDGFX_RSV1_LOWER                                                 0x1434
#define mmBIF_VDDGFX_RSV1_UPPER                                                 0x1435
#define mmBIF_VDDGFX_RSV2_LOWER                                                 0x1436
#define mmBIF_VDDGFX_RSV2_UPPER                                                 0x1437
#define mmBIF_VDDGFX_RSV3_LOWER                                                 0x1438
#define mmBIF_VDDGFX_RSV3_UPPER                                                 0x1439
#define mmBIF_VDDGFX_RSV4_LOWER                                                 0x143a
#define mmBIF_VDDGFX_RSV4_UPPER                                                 0x143b
#define mmBIF_VDDGFX_FB_CMP                                                     0x143c
#define mmBIF_DOORBELL_GBLAPER1_LOWER                                           0x14fc
#define mmBIF_DOORBELL_GBLAPER1_UPPER                                           0x14fd
#define mmBIF_DOORBELL_GBLAPER2_LOWER                                           0x14fe
#define mmBIF_DOORBELL_GBLAPER2_UPPER                                           0x14ff
#define mmBIF_SMU_INDEX                                                         0x143d
#define mmBIF_SMU_DATA                                                          0x143e
#define mmIMPCTL_RESET                                                          0x14f5
#define mmGARLIC_FLUSH_CNTL                                                     0x1401
#define mmGARLIC_FLUSH_ADDR_START_0                                             0x1402
#define mmGARLIC_FLUSH_ADDR_START_1                                             0x1404
#define mmGARLIC_FLUSH_ADDR_START_2                                             0x1406
#define mmGARLIC_FLUSH_ADDR_START_3                                             0x1408
#define mmGARLIC_FLUSH_ADDR_START_4                                             0x140a
#define mmGARLIC_FLUSH_ADDR_START_5                                             0x140c
#define mmGARLIC_FLUSH_ADDR_START_6                                             0x140e
#define mmGARLIC_FLUSH_ADDR_START_7                                             0x1410
#define mmGARLIC_FLUSH_ADDR_END_0                                               0x1403
#define mmGARLIC_FLUSH_ADDR_END_1                                               0x1405
#define mmGARLIC_FLUSH_ADDR_END_2                                               0x1407
#define mmGARLIC_FLUSH_ADDR_END_3                                               0x1409
#define mmGARLIC_FLUSH_ADDR_END_4                                               0x140b
#define mmGARLIC_FLUSH_ADDR_END_5                                               0x140d
#define mmGARLIC_FLUSH_ADDR_END_6                                               0x140f
#define mmGARLIC_FLUSH_ADDR_END_7                                               0x1411
#define mmGARLIC_FLUSH_REQ                                                      0x1412
#define mmGPU_GARLIC_FLUSH_REQ                                                  0x1413
#define mmGPU_GARLIC_FLUSH_DONE                                                 0x1414
#define mmGARLIC_COHE_CP_RB0_WPTR                                               0x1415
#define mmGARLIC_COHE_CP_RB1_WPTR                                               0x1416
#define mmGARLIC_COHE_CP_RB2_WPTR                                               0x1417
#define mmGARLIC_COHE_UVD_RBC_RB_WPTR                                           0x1418
#define mmGARLIC_COHE_SDMA0_GFX_RB_WPTR                                         0x1419
#define mmGARLIC_COHE_SDMA1_GFX_RB_WPTR                                         0x141a
#define mmGARLIC_COHE_CP_DMA_ME_COMMAND                                         0x141b
#define mmGARLIC_COHE_CP_DMA_PFP_COMMAND                                        0x141c
#define mmGARLIC_COHE_SAM_SAB_RBI_WPTR                                          0x141d
#define mmGARLIC_COHE_SAM_SAB_RBO_WPTR                                          0x141e
#define mmGARLIC_COHE_VCE_OUT_RB_WPTR                                           0x141f
#define mmGARLIC_COHE_VCE_RB_WPTR2                                              0x1420
#define mmGARLIC_COHE_VCE_RB_WPTR                                               0x1421
#define mmGARLIC_COHE_SDMA2_GFX_RB_WPTR                                         0x1422
#define mmGARLIC_COHE_SDMA3_GFX_RB_WPTR                                         0x1423
#define mmGARLIC_COHE_CP_DMA_PIO_COMMAND                                        0x1424
#define mmGARLIC_COHE_GARLIC_FLUSH_REQ                                          0x1425
#define mmREMAP_HDP_MEM_FLUSH_CNTL                                              0x1426
#define mmREMAP_HDP_REG_FLUSH_CNTL                                              0x1427
#define mmBIOS_SCRATCH_0                                                        0x5c9
#define mmBIOS_SCRATCH_1                                                        0x5ca
#define mmBIOS_SCRATCH_2                                                        0x5cb
#define mmBIOS_SCRATCH_3                                                        0x5cc
#define mmBIOS_SCRATCH_4                                                        0x5cd
#define mmBIOS_SCRATCH_5                                                        0x5ce
#define mmBIOS_SCRATCH_6                                                        0x5cf
#define mmBIOS_SCRATCH_7                                                        0x5d0
#define mmBIOS_SCRATCH_8                                                        0x5d1
#define mmBIOS_SCRATCH_9                                                        0x5d2
#define mmBIOS_SCRATCH_10                                                       0x5d3
#define mmBIOS_SCRATCH_11                                                       0x5d4
#define mmBIOS_SCRATCH_12                                                       0x5d5
#define mmBIOS_SCRATCH_13                                                       0x5d6
#define mmBIOS_SCRATCH_14                                                       0x5d7
#define mmBIOS_SCRATCH_15                                                       0x5d8
#define mmBIF_RB_CNTL                                                           0x1530
#define mmBIF_RB_BASE                                                           0x1531
#define mmBIF_RB_RPTR                                                           0x1532
#define mmBIF_RB_WPTR                                                           0x1533
#define mmBIF_RB_WPTR_ADDR_HI                                                   0x1534
#define mmBIF_RB_WPTR_ADDR_LO                                                   0x1535
#define mmVENDOR_ID                                                             0x0
#define mmDEVICE_ID                                                             0x0
#define mmCOMMAND                                                               0x1
#define mmSTATUS                                                                0x1
#define mmREVISION_ID                                                           0x2
#define mmPROG_INTERFACE                                                        0x2
#define mmSUB_CLASS                                                             0x2
#define mmBASE_CLASS                                                            0x2
#define mmCACHE_LINE                                                            0x3
#define mmLATENCY                                                               0x3
#define mmHEADER                                                                0x3
#define mmBIST                                                                  0x3
#define mmBASE_ADDR_1                                                           0x4
#define mmBASE_ADDR_2                                                           0x5
#define mmBASE_ADDR_3                                                           0x6
#define mmBASE_ADDR_4                                                           0x7
#define mmBASE_ADDR_5                                                           0x8
#define mmBASE_ADDR_6                                                           0x9
#define mmROM_BASE_ADDR                                                         0xc
#define mmCAP_PTR                                                               0xd
#define mmINTERRUPT_LINE                                                        0xf
#define mmINTERRUPT_PIN                                                         0xf
#define mmADAPTER_ID                                                            0xb
#define mmMIN_GRANT                                                             0xf
#define mmMAX_LATENCY                                                           0xf
#define mmVENDOR_CAP_LIST                                                       0x12
#define mmADAPTER_ID_W                                                          0x13
#define mmPMI_CAP_LIST                                                          0x14
#define mmPMI_CAP                                                               0x14
#define mmPMI_STATUS_CNTL                                                       0x15
#define mmPCIE_CAP_LIST                                                         0x16
#define mmPCIE_CAP                                                              0x16
#define mmDEVICE_CAP                                                            0x17
#define mmDEVICE_CNTL                                                           0x18
#define mmDEVICE_STATUS                                                         0x18
#define mmLINK_CAP                                                              0x19
#define mmLINK_CNTL                                                             0x1a
#define mmLINK_STATUS                                                           0x1a
#define mmDEVICE_CAP2                                                           0x1f
#define mmDEVICE_CNTL2                                                          0x20
#define mmDEVICE_STATUS2                                                        0x20
#define mmLINK_CAP2                                                             0x21
#define mmLINK_CNTL2                                                            0x22
#define mmLINK_STATUS2                                                          0x22
#define mmMSI_CAP_LIST                                                          0x28
#define mmMSI_MSG_CNTL                                                          0x28
#define mmMSI_MSG_ADDR_LO                                                       0x29
#define mmMSI_MSG_ADDR_HI                                                       0x2a
#define mmMSI_MSG_DATA_64                                                       0x2b
#define mmMSI_MSG_DATA                                                          0x2a
#define mmPCIE_VENDOR_SPECIFIC_ENH_CAP_LIST                                     0x40
#define mmPCIE_VENDOR_SPECIFIC_HDR                                              0x41
#define mmPCIE_VENDOR_SPECIFIC1                                                 0x42
#define mmPCIE_VENDOR_SPECIFIC2                                                 0x43
#define mmPCIE_VC_ENH_CAP_LIST                                                  0x44
#define mmPCIE_PORT_VC_CAP_REG1                                                 0x45
#define mmPCIE_PORT_VC_CAP_REG2                                                 0x46
#define mmPCIE_PORT_VC_CNTL                                                     0x47
#define mmPCIE_PORT_VC_STATUS                                                   0x47
#define mmPCIE_VC0_RESOURCE_CAP                                                 0x48
#define mmPCIE_VC0_RESOURCE_CNTL                                                0x49
#define mmPCIE_VC0_RESOURCE_STATUS                                              0x4a
#define mmPCIE_VC1_RESOURCE_CAP                                                 0x4b
#define mmPCIE_VC1_RESOURCE_CNTL                                                0x4c
#define mmPCIE_VC1_RESOURCE_STATUS                                              0x4d
#define mmPCIE_DEV_SERIAL_NUM_ENH_CAP_LIST                                      0x50
#define mmPCIE_DEV_SERIAL_NUM_DW1                                               0x51
#define mmPCIE_DEV_SERIAL_NUM_DW2                                               0x52
#define mmPCIE_ADV_ERR_RPT_ENH_CAP_LIST                                         0x54
#define mmPCIE_UNCORR_ERR_STATUS                                                0x55
#define mmPCIE_UNCORR_ERR_MASK                                                  0x56
#define mmPCIE_UNCORR_ERR_SEVERITY                                              0x57
#define mmPCIE_CORR_ERR_STATUS                                                  0x58
#define mmPCIE_CORR_ERR_MASK                                                    0x59
#define mmPCIE_ADV_ERR_CAP_CNTL                                                 0x5a
#define mmPCIE_HDR_LOG0                                                         0x5b
#define mmPCIE_HDR_LOG1                                                         0x5c
#define mmPCIE_HDR_LOG2                                                         0x5d
#define mmPCIE_HDR_LOG3                                                         0x5e
#define mmPCIE_TLP_PREFIX_LOG0                                                  0x62
#define mmPCIE_TLP_PREFIX_LOG1                                                  0x63
#define mmPCIE_TLP_PREFIX_LOG2                                                  0x64
#define mmPCIE_TLP_PREFIX_LOG3                                                  0x65
#define mmPCIE_BAR_ENH_CAP_LIST                                                 0x80
#define mmPCIE_BAR1_CAP                                                         0x81
#define mmPCIE_BAR1_CNTL                                                        0x82
#define mmPCIE_BAR2_CAP                                                         0x83
#define mmPCIE_BAR2_CNTL                                                        0x84
#define mmPCIE_BAR3_CAP                                                         0x85
#define mmPCIE_BAR3_CNTL                                                        0x86
#define mmPCIE_BAR4_CAP                                                         0x87
#define mmPCIE_BAR4_CNTL                                                        0x88
#define mmPCIE_BAR5_CAP                                                         0x89
#define mmPCIE_BAR5_CNTL                                                        0x8a
#define mmPCIE_BAR6_CAP                                                         0x8b
#define mmPCIE_BAR6_CNTL                                                        0x8c
#define mmPCIE_PWR_BUDGET_ENH_CAP_LIST                                          0x90
#define mmPCIE_PWR_BUDGET_DATA_SELECT                                           0x91
#define mmPCIE_PWR_BUDGET_DATA                                                  0x92
#define mmPCIE_PWR_BUDGET_CAP                                                   0x93
#define mmPCIE_DPA_ENH_CAP_LIST                                                 0x94
#define mmPCIE_DPA_CAP                                                          0x95
#define mmPCIE_DPA_LATENCY_INDICATOR                                            0x96
#define mmPCIE_DPA_STATUS                                                       0x97
#define mmPCIE_DPA_CNTL                                                         0x97
#define mmPCIE_DPA_SUBSTATE_PWR_ALLOC_0                                         0x98
#define mmPCIE_DPA_SUBSTATE_PWR_ALLOC_1                                         0x98
#define mmPCIE_DPA_SUBSTATE_PWR_ALLOC_2                                         0x98
#define mmPCIE_DPA_SUBSTATE_PWR_ALLOC_3                                         0x98
#define mmPCIE_DPA_SUBSTATE_PWR_ALLOC_4                                         0x99
#define mmPCIE_DPA_SUBSTATE_PWR_ALLOC_5                                         0x99
#define mmPCIE_DPA_SUBSTATE_PWR_ALLOC_6                                         0x99
#define mmPCIE_DPA_SUBSTATE_PWR_ALLOC_7                                         0x99
#define mmPCIE_SECONDARY_ENH_CAP_LIST                                           0x9c
#define mmPCIE_LINK_CNTL3                                                       0x9d
#define mmPCIE_LANE_ERROR_STATUS                                                0x9e
#define mmPCIE_LANE_0_EQUALIZATION_CNTL                                         0x9f
#define mmPCIE_LANE_1_EQUALIZATION_CNTL                                         0x9f
#define mmPCIE_LANE_2_EQUALIZATION_CNTL                                         0xa0
#define mmPCIE_LANE_3_EQUALIZATION_CNTL                                         0xa0
#define mmPCIE_LANE_4_EQUALIZATION_CNTL                                         0xa1
#define mmPCIE_LANE_5_EQUALIZATION_CNTL                                         0xa1
#define mmPCIE_LANE_6_EQUALIZATION_CNTL                                         0xa2
#define mmPCIE_LANE_7_EQUALIZATION_CNTL                                         0xa2
#define mmPCIE_LANE_8_EQUALIZATION_CNTL                                         0xa3
#define mmPCIE_LANE_9_EQUALIZATION_CNTL                                         0xa3
#define mmPCIE_LANE_10_EQUALIZATION_CNTL                                        0xa4
#define mmPCIE_LANE_11_EQUALIZATION_CNTL                                        0xa4
#define mmPCIE_LANE_12_EQUALIZATION_CNTL                                        0xa5
#define mmPCIE_LANE_13_EQUALIZATION_CNTL                                        0xa5
#define mmPCIE_LANE_14_EQUALIZATION_CNTL                                        0xa6
#define mmPCIE_LANE_15_EQUALIZATION_CNTL                                        0xa6
#define mmPCIE_ACS_ENH_CAP_LIST                                                 0xa8
#define mmPCIE_ACS_CAP                                                          0xa9
#define mmPCIE_ACS_CNTL                                                         0xa9
#define mmPCIE_ATS_ENH_CAP_LIST                                                 0xac
#define mmPCIE_ATS_CAP                                                          0xad
#define mmPCIE_ATS_CNTL                                                         0xad
#define mmPCIE_PAGE_REQ_ENH_CAP_LIST                                            0xb0
#define mmPCIE_PAGE_REQ_CNTL                                                    0xb1
#define mmPCIE_PAGE_REQ_STATUS                                                  0xb1
#define mmPCIE_OUTSTAND_PAGE_REQ_CAPACITY                                       0xb2
#define mmPCIE_OUTSTAND_PAGE_REQ_ALLOC                                          0xb3
#define mmPCIE_PASID_ENH_CAP_LIST                                               0xb4
#define mmPCIE_PASID_CAP                                                        0xb5
#define mmPCIE_PASID_CNTL                                                       0xb5
#define mmPCIE_TPH_REQR_ENH_CAP_LIST                                            0xb8
#define mmPCIE_TPH_REQR_CAP                                                     0xb9
#define mmPCIE_TPH_REQR_CNTL                                                    0xba
#define mmPCIE_MC_ENH_CAP_LIST                                                  0xbc
#define mmPCIE_MC_CAP                                                           0xbd
#define mmPCIE_MC_CNTL                                                          0xbd
#define mmPCIE_MC_ADDR0                                                         0xbe
#define mmPCIE_MC_ADDR1                                                         0xbf
#define mmPCIE_MC_RCV0                                                          0xc0
#define mmPCIE_MC_RCV1                                                          0xc1
#define mmPCIE_MC_BLOCK_ALL0                                                    0xc2
#define mmPCIE_MC_BLOCK_ALL1                                                    0xc3
#define mmPCIE_MC_BLOCK_UNTRANSLATED_0                                          0xc4
#define mmPCIE_MC_BLOCK_UNTRANSLATED_1                                          0xc5
#define mmPCIE_LTR_ENH_CAP_LIST                                                 0xc8
#define mmPCIE_LTR_CAP                                                          0xc9
#define ixMM_INDEX_IND                                                          0x1090000
#define ixMM_INDEX_HI_IND                                                       0x1090006
#define ixMM_DATA_IND                                                           0x1090001
#define ixBIF_MM_INDACCESS_CNTL_IND                                             0x1091500
#define ixBUS_CNTL_IND                                                          0x1091508
#define ixCONFIG_CNTL_IND                                                       0x1091509
#define ixCONFIG_MEMSIZE_IND                                                    0x109150a
#define ixCONFIG_F0_BASE_IND                                                    0x109150b
#define ixCONFIG_APER_SIZE_IND                                                  0x109150c
#define ixCONFIG_REG_APER_SIZE_IND                                              0x109150d
#define ixBIF_SCRATCH0_IND                                                      0x109150e
#define ixBIF_SCRATCH1_IND                                                      0x109150f
#define ixBX_RESET_EN_IND                                                       0x1091514
#define ixMM_CFGREGS_CNTL_IND                                                   0x1091513
#define ixHW_DEBUG_IND                                                          0x1091515
#define ixMASTER_CREDIT_CNTL_IND                                                0x1091516
#define ixSLAVE_REQ_CREDIT_CNTL_IND                                             0x1091517
#define ixBX_RESET_CNTL_IND                                                     0x1091518
#define ixINTERRUPT_CNTL_IND                                                    0x109151a
#define ixINTERRUPT_CNTL2_IND                                                   0x109151b
#define ixBIF_DEBUG_CNTL_IND                                                    0x109151c
#define ixBIF_DEBUG_MUX_IND                                                     0x109151d
#define ixBIF_DEBUG_OUT_IND                                                     0x109151e
#define ixHDP_REG_COHERENCY_FLUSH_CNTL_IND                                      0x1091528
#define ixHDP_MEM_COHERENCY_FLUSH_CNTL_IND                                      0x1091520
#define ixCLKREQB_PAD_CNTL_IND                                                  0x1091521
#define ixSMBDAT_PAD_CNTL_IND                                                   0x1091522
#define ixSMBCLK_PAD_CNTL_IND                                                   0x1091523
#define ixBIF_XDMA_LO_IND                                                       0x10914c0
#define ixBIF_XDMA_HI_IND                                                       0x10914c1
#define ixBIF_FEATURES_CONTROL_MISC_IND                                         0x10914c2
#define ixBIF_DOORBELL_CNTL_IND                                                 0x10914c3
#define ixBIF_SLVARB_MODE_IND                                                   0x10914c4
#define ixBIF_FB_EN_IND                                                         0x1091524
#define ixBIF_BUSNUM_CNTL1_IND                                                  0x1091525
#define ixBIF_BUSNUM_LIST0_IND                                                  0x1091526
#define ixBIF_BUSNUM_LIST1_IND                                                  0x1091527
#define ixBIF_BUSNUM_CNTL2_IND                                                  0x109152b
#define ixBIF_BUSY_DELAY_CNTR_IND                                               0x1091529
#define ixBIF_PERFMON_CNTL_IND                                                  0x109152c
#define ixBIF_PERFCOUNTER0_RESULT_IND                                           0x109152d
#define ixBIF_PERFCOUNTER1_RESULT_IND                                           0x109152e
#define ixSLAVE_HANG_PROTECTION_CNTL_IND                                        0x1091536
#define ixGPU_HDP_FLUSH_REQ_IND                                                 0x1091537
#define ixGPU_HDP_FLUSH_DONE_IND                                                0x1091538
#define ixSLAVE_HANG_ERROR_IND                                                  0x109153b
#define ixCAPTURE_HOST_BUSNUM_IND                                               0x109153c
#define ixHOST_BUSNUM_IND                                                       0x109153d
#define ixPEER_REG_RANGE0_IND                                                   0x109153e
#define ixPEER_REG_RANGE1_IND                                                   0x109153f
#define ixPEER0_FB_OFFSET_HI_IND                                                0x10914f3
#define ixPEER0_FB_OFFSET_LO_IND                                                0x10914f2
#define ixPEER1_FB_OFFSET_HI_IND                                                0x10914f1
#define ixPEER1_FB_OFFSET_LO_IND                                                0x10914f0
#define ixPEER2_FB_OFFSET_HI_IND                                                0x10914ef
#define ixPEER2_FB_OFFSET_LO_IND                                                0x10914ee
#define ixPEER3_FB_OFFSET_HI_IND                                                0x10914ed
#define ixPEER3_FB_OFFSET_LO_IND                                                0x10914ec
#define ixDBG_BYPASS_SRBM_ACCESS_IND                                            0x10914eb
#define ixSMBUS_BACO_DUMMY_IND                                                  0x10914c6
#define ixBIF_DEVFUNCNUM_LIST0_IND                                              0x10914e8
#define ixBIF_DEVFUNCNUM_LIST1_IND                                              0x10914e7
#define ixBACO_CNTL_IND                                                         0x10914e5
#define ixBF_ANA_ISO_CNTL_IND                                                   0x10914c7
#define ixMEM_TYPE_CNTL_IND                                                     0x10914e4
#define ixBIF_BACO_DEBUG_IND                                                    0x10914df
#define ixBIF_BACO_DEBUG_LATCH_IND                                              0x10914dc
#define ixBACO_CNTL_MISC_IND                                                    0x10914db
#define ixSMU_BIF_VDDGFX_PWR_STATUS_IND                                         0x10914f8
#define ixBIF_VDDGFX_GFX0_LOWER_IND                                             0x1091428
#define ixBIF_VDDGFX_GFX0_UPPER_IND                                             0x1091429
#define ixBIF_VDDGFX_GFX1_LOWER_IND                                             0x109142a
#define ixBIF_VDDGFX_GFX1_UPPER_IND                                             0x109142b
#define ixBIF_VDDGFX_GFX2_LOWER_IND                                             0x109142c
#define ixBIF_VDDGFX_GFX2_UPPER_IND                                             0x109142d
#define ixBIF_VDDGFX_GFX3_LOWER_IND                                             0x109142e
#define ixBIF_VDDGFX_GFX3_UPPER_IND                                             0x109142f
#define ixBIF_VDDGFX_GFX4_LOWER_IND                                             0x1091430
#define ixBIF_VDDGFX_GFX4_UPPER_IND                                             0x1091431
#define ixBIF_VDDGFX_GFX5_LOWER_IND                                             0x1091432
#define ixBIF_VDDGFX_GFX5_UPPER_IND                                             0x1091433
#define ixBIF_VDDGFX_RSV1_LOWER_IND                                             0x1091434
#define ixBIF_VDDGFX_RSV1_UPPER_IND                                             0x1091435
#define ixBIF_VDDGFX_RSV2_LOWER_IND                                             0x1091436
#define ixBIF_VDDGFX_RSV2_UPPER_IND                                             0x1091437
#define ixBIF_VDDGFX_RSV3_LOWER_IND                                             0x1091438
#define ixBIF_VDDGFX_RSV3_UPPER_IND                                             0x1091439
#define ixBIF_VDDGFX_RSV4_LOWER_IND                                             0x109143a
#define ixBIF_VDDGFX_RSV4_UPPER_IND                                             0x109143b
#define ixBIF_VDDGFX_FB_CMP_IND                                                 0x109143c
#define ixBIF_DOORBELL_GBLAPER1_LOWER_IND                                       0x10914fc
#define ixBIF_DOORBELL_GBLAPER1_UPPER_IND                                       0x10914fd
#define ixBIF_DOORBELL_GBLAPER2_LOWER_IND                                       0x10914fe
#define ixBIF_DOORBELL_GBLAPER2_UPPER_IND                                       0x10914ff
#define ixBIF_SMU_INDEX_IND                                                     0x109143d
#define ixBIF_SMU_DATA_IND                                                      0x109143e
#define ixIMPCTL_RESET_IND                                                      0x10914f5
#define ixGARLIC_FLUSH_CNTL_IND                                                 0x1091401
#define ixGARLIC_FLUSH_REQ_IND                                                  0x1091412
#define ixGPU_GARLIC_FLUSH_REQ_IND                                              0x1091413
#define ixGPU_GARLIC_FLUSH_DONE_IND                                             0x1091414
#define ixGARLIC_COHE_CP_RB0_WPTR_IND                                           0x1091415
#define ixGARLIC_COHE_CP_RB1_WPTR_IND                                           0x1091416
#define ixGARLIC_COHE_CP_RB2_WPTR_IND                                           0x1091417
#define ixGARLIC_COHE_UVD_RBC_RB_WPTR_IND                                       0x1091418
#define ixGARLIC_COHE_SDMA0_GFX_RB_WPTR_IND                                     0x1091419
#define ixGARLIC_COHE_SDMA1_GFX_RB_WPTR_IND                                     0x109141a
#define ixGARLIC_COHE_CP_DMA_ME_COMMAND_IND                                     0x109141b
#define ixGARLIC_COHE_CP_DMA_PFP_COMMAND_IND                                    0x109141c
#define ixGARLIC_COHE_SAM_SAB_RBI_WPTR_IND                                      0x109141d
#define ixGARLIC_COHE_SAM_SAB_RBO_WPTR_IND                                      0x109141e
#define ixGARLIC_COHE_VCE_OUT_RB_WPTR_IND                                       0x109141f
#define ixGARLIC_COHE_VCE_RB_WPTR2_IND                                          0x1091420
#define ixGARLIC_COHE_VCE_RB_WPTR_IND                                           0x1091421
#define ixGARLIC_COHE_SDMA2_GFX_RB_WPTR_IND                                     0x1091422
#define ixGARLIC_COHE_SDMA3_GFX_RB_WPTR_IND                                     0x1091423
#define ixGARLIC_COHE_CP_DMA_PIO_COMMAND_IND                                    0x1091424
#define ixGARLIC_COHE_GARLIC_FLUSH_REQ_IND                                      0x1091425
#define ixREMAP_HDP_MEM_FLUSH_CNTL_IND                                          0x1091426
#define ixREMAP_HDP_REG_FLUSH_CNTL_IND                                          0x1091427
#define ixBIOS_SCRATCH_0_IND                                                    0x10905c9
#define ixBIOS_SCRATCH_1_IND                                                    0x10905ca
#define ixBIOS_SCRATCH_2_IND                                                    0x10905cb
#define ixBIOS_SCRATCH_3_IND                                                    0x10905cc
#define ixBIOS_SCRATCH_4_IND                                                    0x10905cd
#define ixBIOS_SCRATCH_5_IND                                                    0x10905ce
#define ixBIOS_SCRATCH_6_IND                                                    0x10905cf
#define ixBIOS_SCRATCH_7_IND                                                    0x10905d0
#define ixBIOS_SCRATCH_8_IND                                                    0x10905d1
#define ixBIOS_SCRATCH_9_IND                                                    0x10905d2
#define ixBIOS_SCRATCH_10_IND                                                   0x10905d3
#define ixBIOS_SCRATCH_11_IND                                                   0x10905d4
#define ixBIOS_SCRATCH_12_IND                                                   0x10905d5
#define ixBIOS_SCRATCH_13_IND                                                   0x10905d6
#define ixBIOS_SCRATCH_14_IND                                                   0x10905d7
#define ixBIOS_SCRATCH_15_IND                                                   0x10905d8
#define ixBIF_RB_CNTL_IND                                                       0x1091530
#define ixBIF_RB_BASE_IND                                                       0x1091531
#define ixBIF_RB_RPTR_IND                                                       0x1091532
#define ixBIF_RB_WPTR_IND                                                       0x1091533
#define ixBIF_RB_WPTR_ADDR_HI_IND                                               0x1091534
#define ixBIF_RB_WPTR_ADDR_LO_IND                                               0x1091535
#define mmNB_GBIF_INDEX                                                         0x34
#define mmNB_GBIF_DATA                                                          0x35
#define mmPCIE_INDEX                                                            0xe
#define mmPCIE_DATA                                                             0xf
#define mmPCIE_INDEX_2                                                          0xc
#define mmPCIE_DATA_2                                                           0xd
#define ixPCIE_RESERVED                                                         0x1400000
#define ixPCIE_SCRATCH                                                          0x1400001
#define ixPCIE_HW_DEBUG                                                         0x1400002
#define ixPCIE_RX_NUM_NAK                                                       0x140000e
#define ixPCIE_RX_NUM_NAK_GENERATED                                             0x140000f
#define ixPCIE_CNTL                                                             0x1400010
#define ixPCIE_CONFIG_CNTL                                                      0x1400011
#define ixPCIE_DEBUG_CNTL                                                       0x1400012
#define ixPCIE_INT_CNTL                                                         0x140001a
#define ixPCIE_INT_STATUS                                                       0x140001b
#define ixPCIE_CNTL2                                                            0x140001c
#define ixPCIE_RX_CNTL2                                                         0x140001d
#define ixPCIE_TX_F0_ATTR_CNTL                                                  0x140001e
#define ixPCIE_TX_F1_F2_ATTR_CNTL                                               0x140001f
#define ixPCIE_CI_CNTL                                                          0x1400020
#define ixPCIE_BUS_CNTL                                                         0x1400021
#define ixPCIE_LC_STATE6                                                        0x1400022
#define ixPCIE_LC_STATE7                                                        0x1400023
#define ixPCIE_LC_STATE8                                                        0x1400024
#define ixPCIE_LC_STATE9                                                        0x1400025
#define ixPCIE_LC_STATE10                                                       0x1400026
#define ixPCIE_LC_STATE11                                                       0x1400027
#define ixPCIE_LC_STATUS1                                                       0x1400028
#define ixPCIE_LC_STATUS2                                                       0x1400029
#define ixPCIE_WPR_CNTL                                                         0x1400030
#define ixPCIE_RX_LAST_TLP0                                                     0x1400031
#define ixPCIE_RX_LAST_TLP1                                                     0x1400032
#define ixPCIE_RX_LAST_TLP2                                                     0x1400033
#define ixPCIE_RX_LAST_TLP3                                                     0x1400034
#define ixPCIE_TX_LAST_TLP0                                                     0x1400035
#define ixPCIE_TX_LAST_TLP1                                                     0x1400036
#define ixPCIE_TX_LAST_TLP2                                                     0x1400037
#define ixPCIE_TX_LAST_TLP3                                                     0x1400038
#define ixPCIE_I2C_REG_ADDR_EXPAND                                              0x140003a
#define ixPCIE_I2C_REG_DATA                                                     0x140003b
#define ixPCIE_CFG_CNTL                                                         0x140003c
#define ixPCIE_P_CNTL                                                           0x1400040
#define ixPCIE_P_BUF_STATUS                                                     0x1400041
#define ixPCIE_P_DECODER_STATUS                                                 0x1400042
#define ixPCIE_P_MISC_STATUS                                                    0x1400043
#define ixPCIE_P_RCV_L0S_FTS_DET                                                0x1400050
#define ixPCIE_OBFF_CNTL                                                        0x1400061
#define ixPCIE_TX_LTR_CNTL                                                      0x1400060
#define ixPCIE_PERF_COUNT_CNTL                                                  0x1400080
#define ixPCIE_PERF_CNTL_TXCLK                                                  0x1400081
#define ixPCIE_PERF_COUNT0_TXCLK                                                0x1400082
#define ixPCIE_PERF_COUNT1_TXCLK                                                0x1400083
#define ixPCIE_PERF_CNTL_MST_R_CLK                                              0x1400084
#define ixPCIE_PERF_COUNT0_MST_R_CLK                                            0x1400085
#define ixPCIE_PERF_COUNT1_MST_R_CLK                                            0x1400086
#define ixPCIE_PERF_CNTL_MST_C_CLK                                              0x1400087
#define ixPCIE_PERF_COUNT0_MST_C_CLK                                            0x1400088
#define ixPCIE_PERF_COUNT1_MST_C_CLK                                            0x1400089
#define ixPCIE_PERF_CNTL_SLV_R_CLK                                              0x140008a
#define ixPCIE_PERF_COUNT0_SLV_R_CLK                                            0x140008b
#define ixPCIE_PERF_COUNT1_SLV_R_CLK                                            0x140008c
#define ixPCIE_PERF_CNTL_SLV_S_C_CLK                                            0x140008d
#define ixPCIE_PERF_COUNT0_SLV_S_C_CLK                                          0x140008e
#define ixPCIE_PERF_COUNT1_SLV_S_C_CLK                                          0x140008f
#define ixPCIE_PERF_CNTL_SLV_NS_C_CLK                                           0x1400090
#define ixPCIE_PERF_COUNT0_SLV_NS_C_CLK                                         0x1400091
#define ixPCIE_PERF_COUNT1_SLV_NS_C_CLK                                         0x1400092
#define ixPCIE_PERF_CNTL_EVENT0_PORT_SEL                                        0x1400093
#define ixPCIE_PERF_CNTL_EVENT1_PORT_SEL                                        0x1400094
#define ixPCIE_PERF_CNTL_TXCLK2                                                 0x1400095
#define ixPCIE_PERF_COUNT0_TXCLK2                                               0x1400096
#define ixPCIE_PERF_COUNT1_TXCLK2                                               0x1400097
#define ixPCIE_STRAP_F0                                                         0x14000b0
#define ixPCIE_STRAP_F1                                                         0x14000b1
#define ixPCIE_STRAP_F2                                                         0x14000b2
#define ixPCIE_STRAP_F3                                                         0x14000b3
#define ixPCIE_STRAP_F4                                                         0x14000b4
#define ixPCIE_STRAP_F5                                                         0x14000b5
#define ixPCIE_STRAP_F6                                                         0x14000b6
#define ixPCIE_STRAP_F7                                                         0x14000b7
#define ixPCIE_STRAP_MISC                                                       0x14000c0
#define ixPCIE_STRAP_MISC2                                                      0x14000c1
#define ixPCIE_STRAP_PI                                                         0x14000c2
#define ixPCIE_STRAP_I2C_BD                                                     0x14000c4
#define ixPCIE_PRBS_CLR                                                         0x14000c8
#define ixPCIE_PRBS_STATUS1                                                     0x14000c9
#define ixPCIE_PRBS_STATUS2                                                     0x14000ca
#define ixPCIE_PRBS_FREERUN                                                     0x14000cb
#define ixPCIE_PRBS_MISC                                                        0x14000cc
#define ixPCIE_PRBS_USER_PATTERN                                                0x14000cd
#define ixPCIE_PRBS_LO_BITCNT                                                   0x14000ce
#define ixPCIE_PRBS_HI_BITCNT                                                   0x14000cf
#define ixPCIE_PRBS_ERRCNT_0                                                    0x14000d0
#define ixPCIE_PRBS_ERRCNT_1                                                    0x14000d1
#define ixPCIE_PRBS_ERRCNT_2                                                    0x14000d2
#define ixPCIE_PRBS_ERRCNT_3                                                    0x14000d3
#define ixPCIE_PRBS_ERRCNT_4                                                    0x14000d4
#define ixPCIE_PRBS_ERRCNT_5                                                    0x14000d5
#define ixPCIE_PRBS_ERRCNT_6                                                    0x14000d6
#define ixPCIE_PRBS_ERRCNT_7                                                    0x14000d7
#define ixPCIE_PRBS_ERRCNT_8                                                    0x14000d8
#define ixPCIE_PRBS_ERRCNT_9                                                    0x14000d9
#define ixPCIE_PRBS_ERRCNT_10                                                   0x14000da
#define ixPCIE_PRBS_ERRCNT_11                                                   0x14000db
#define ixPCIE_PRBS_ERRCNT_12                                                   0x14000dc
#define ixPCIE_PRBS_ERRCNT_13                                                   0x14000dd
#define ixPCIE_PRBS_ERRCNT_14                                                   0x14000de
#define ixPCIE_PRBS_ERRCNT_15                                                   0x14000df
#define ixPCIE_F0_DPA_CAP                                                       0x14000e0
#define ixPCIE_F0_DPA_LATENCY_INDICATOR                                         0x14000e4
#define ixPCIE_F0_DPA_CNTL                                                      0x14000e5
#define ixPCIE_F0_DPA_SUBSTATE_PWR_ALLOC_0                                      0x14000e7
#define ixPCIE_F0_DPA_SUBSTATE_PWR_ALLOC_1                                      0x14000e8
#define ixPCIE_F0_DPA_SUBSTATE_PWR_ALLOC_2                                      0x14000e9
#define ixPCIE_F0_DPA_SUBSTATE_PWR_ALLOC_3                                      0x14000ea
#define ixPCIE_F0_DPA_SUBSTATE_PWR_ALLOC_4                                      0x14000eb
#define ixPCIE_F0_DPA_SUBSTATE_PWR_ALLOC_5                                      0x14000ec
#define ixPCIE_F0_DPA_SUBSTATE_PWR_ALLOC_6                                      0x14000ed
#define ixPCIE_F0_DPA_SUBSTATE_PWR_ALLOC_7                                      0x14000ee
#define ixPCIEP_RESERVED                                                        0x10010000
#define ixPCIEP_SCRATCH                                                         0x10010001
#define ixPCIEP_HW_DEBUG                                                        0x10010002
#define ixPCIEP_PORT_CNTL                                                       0x10010010
#define ixPCIE_TX_CNTL                                                          0x10010020
#define ixPCIE_TX_REQUESTER_ID                                                  0x10010021
#define ixPCIE_TX_VENDOR_SPECIFIC                                               0x10010022
#define ixPCIE_TX_REQUEST_NUM_CNTL                                              0x10010023
#define ixPCIE_TX_SEQ                                                           0x10010024
#define ixPCIE_TX_REPLAY                                                        0x10010025
#define ixPCIE_TX_ACK_LATENCY_LIMIT                                             0x10010026
#define ixPCIE_TX_CREDITS_ADVT_P                                                0x10010030
#define ixPCIE_TX_CREDITS_ADVT_NP                                               0x10010031
#define ixPCIE_TX_CREDITS_ADVT_CPL                                              0x10010032
#define ixPCIE_TX_CREDITS_INIT_P                                                0x10010033
#define ixPCIE_TX_CREDITS_INIT_NP                                               0x10010034
#define ixPCIE_TX_CREDITS_INIT_CPL                                              0x10010035
#define ixPCIE_TX_CREDITS_STATUS                                                0x10010036
#define ixPCIE_TX_CREDITS_FCU_THRESHOLD                                         0x10010037
#define ixPCIE_P_PORT_LANE_STATUS                                               0x10010050
#define ixPCIE_FC_P                                                             0x10010060
#define ixPCIE_FC_NP                                                            0x10010061
#define ixPCIE_FC_CPL                                                           0x10010062
#define ixPCIE_ERR_CNTL                                                         0x1001006a
#define ixPCIE_RX_CNTL                                                          0x10010070
#define ixPCIE_RX_EXPECTED_SEQNUM                                               0x10010071
#define ixPCIE_RX_VENDOR_SPECIFIC                                               0x10010072
#define ixPCIE_RX_CNTL3                                                         0x10010074
#define ixPCIE_RX_CREDITS_ALLOCATED_P                                           0x10010080
#define ixPCIE_RX_CREDITS_ALLOCATED_NP                                          0x10010081
#define ixPCIE_RX_CREDITS_ALLOCATED_CPL                                         0x10010082
#define ixPCIE_LC_CNTL                                                          0x100100a0
#define ixPCIE_LC_CNTL2                                                         0x100100b1
#define ixPCIE_LC_CNTL3                                                         0x100100b5
#define ixPCIE_LC_CNTL4                                                         0x100100b6
#define ixPCIE_LC_CNTL5                                                         0x100100b7
#define ixPCIE_LC_BW_CHANGE_CNTL                                                0x100100b2
#define ixPCIE_LC_TRAINING_CNTL                                                 0x100100a1
#define ixPCIE_LC_LINK_WIDTH_CNTL                                               0x100100a2
#define ixPCIE_LC_N_FTS_CNTL                                                    0x100100a3
#define ixPCIE_LC_SPEED_CNTL                                                    0x100100a4
#define ixPCIE_LC_CDR_CNTL                                                      0x100100b3
#define ixPCIE_LC_LANE_CNTL                                                     0x100100b4
#define ixPCIE_LC_FORCE_COEFF                                                   0x100100b8
#define ixPCIE_LC_BEST_EQ_SETTINGS                                              0x100100b9
#define ixPCIE_LC_FORCE_EQ_REQ_COEFF                                            0x100100ba
#define ixPCIE_LC_STATE0                                                        0x100100a5
#define ixPCIE_LC_STATE1                                                        0x100100a6
#define ixPCIE_LC_STATE2                                                        0x100100a7
#define ixPCIE_LC_STATE3                                                        0x100100a8
#define ixPCIE_LC_STATE4                                                        0x100100a9
#define ixPCIE_LC_STATE5                                                        0x100100aa
#define ixPCIEP_STRAP_LC                                                        0x100100c0
#define ixPCIEP_STRAP_MISC                                                      0x100100c1
#define ixPCIEP_BCH_ECC_CNTL                                                    0x100100d0
#define mmBIF_RFE_SNOOP_REG                                                     0x27
#define mmBIF_RFE_WARMRST_CNTL                                                  0x1459
#define mmBIF_RFE_SOFTRST_CNTL                                                  0x1441
#define mmBIF_RFE_CLIENT_SOFTRST_TRIGGER                                        0x1442
#define mmBIF_RFE_MASTER_SOFTRST_TRIGGER                                        0x1443
#define mmBIF_PWDN_COMMAND                                                      0x1444
#define mmBIF_PWDN_STATUS                                                       0x1445
#define mmBIF_RFE_MST_FBU_CMDSTATUS                                             0x1446
#define mmBIF_RFE_MST_RWREG_RFEWGBIF_CMDSTATUS                                  0x1447
#define mmBIF_RFE_MST_BX_CMDSTATUS                                              0x1448
#define mmBIF_RFE_MST_TMOUT_STATUS                                              0x144b
#define mmBIF_RFE_MMCFG_CNTL                                                    0x144c
#define ixBIF_CLOCKS_BITS_IND                                                   0x1301489
#define ixBIF_LNCNT_RESET_IND                                                   0x1301488
#define ixLNCNT_CONTROL_IND                                                     0x1301487
#define ixNEW_REFCLKB_TIMER_IND                                                 0x1301485
#define ixNEW_REFCLKB_TIMER_1_IND                                               0x1301484
#define ixBIF_CLK_PDWN_DELAY_TIMER_IND                                          0x1301483
#define ixBIF_RESET_EN_IND                                                      0x1301482
#define ixBIF_PIF_TXCLK_SWITCH_TIMER_IND                                        0x1301481
#define ixBIF_BACO_MSIC_IND                                                     0x1301480
#define ixBIF_RESET_CNTL_IND                                                    0x1301486
#define ixBIF_RFE_CNTL_MISC_IND                                                 0x130148c
#define ixBIF_MEM_PG_CNTL_IND                                                   0x130148a
#define mmNB_GBIF_INDEX                                                         0x34
#define mmNB_GBIF_DATA                                                          0x35
#define mmBIF_CLOCKS_BITS                                                       0x1489
#define mmBIF_LNCNT_RESET                                                       0x1488
#define mmLNCNT_CONTROL                                                         0x1487
#define mmNEW_REFCLKB_TIMER                                                     0x1485
#define mmNEW_REFCLKB_TIMER_1                                                   0x1484
#define mmBIF_CLK_PDWN_DELAY_TIMER                                              0x1483
#define mmBIF_RESET_EN                                                          0x1482
#define mmBIF_PIF_TXCLK_SWITCH_TIMER                                            0x1481
#define mmBIF_BACO_MSIC                                                         0x1480
#define mmBIF_RESET_CNTL                                                        0x1486
#define mmBIF_RFE_CNTL_MISC                                                     0x148c
#define mmBIF_MEM_PG_CNTL                                                       0x148a
#define mmC_PCIE_P_INDEX                                                        0x38
#define mmC_PCIE_P_DATA                                                         0x39
#define ixD2F1_PCIE_PORT_INDEX                                                  0x2000038
#define ixD2F1_PCIE_PORT_DATA                                                   0x2000039
#define ixD2F1_PCIEP_RESERVED                                                   0x0
#define ixD2F1_PCIEP_SCRATCH                                                    0x1
#define ixD2F1_PCIEP_HW_DEBUG                                                   0x2
#define ixD2F1_PCIEP_PORT_CNTL                                                  0x10
#define ixD2F1_PCIE_TX_CNTL                                                     0x20
#define ixD2F1_PCIE_TX_REQUESTER_ID                                             0x21
#define ixD2F1_PCIE_TX_VENDOR_SPECIFIC                                          0x22
#define ixD2F1_PCIE_TX_REQUEST_NUM_CNTL                                         0x23
#define ixD2F1_PCIE_TX_SEQ                                                      0x24
#define ixD2F1_PCIE_TX_REPLAY                                                   0x25
#define ixD2F1_PCIE_TX_ACK_LATENCY_LIMIT                                        0x26
#define ixD2F1_PCIE_TX_CREDITS_ADVT_P                                           0x30
#define ixD2F1_PCIE_TX_CREDITS_ADVT_NP                                          0x31
#define ixD2F1_PCIE_TX_CREDITS_ADVT_CPL                                         0x32
#define ixD2F1_PCIE_TX_CREDITS_INIT_P                                           0x33
#define ixD2F1_PCIE_TX_CREDITS_INIT_NP                                          0x34
#define ixD2F1_PCIE_TX_CREDITS_INIT_CPL                                         0x35
#define ixD2F1_PCIE_TX_CREDITS_STATUS                                           0x36
#define ixD2F1_PCIE_TX_CREDITS_FCU_THRESHOLD                                    0x37
#define ixD2F1_PCIE_P_PORT_LANE_STATUS                                          0x50
#define ixD2F1_PCIE_FC_P                                                        0x60
#define ixD2F1_PCIE_FC_NP                                                       0x61
#define ixD2F1_PCIE_FC_CPL                                                      0x62
#define ixD2F1_PCIE_ERR_CNTL                                                    0x6a
#define ixD2F1_PCIE_RX_CNTL                                                     0x70
#define ixD2F1_PCIE_RX_EXPECTED_SEQNUM                                          0x71
#define ixD2F1_PCIE_RX_VENDOR_SPECIFIC                                          0x72
#define ixD2F1_PCIE_RX_CNTL3                                                    0x74
#define ixD2F1_PCIE_RX_CREDITS_ALLOCATED_P                                      0x80
#define ixD2F1_PCIE_RX_CREDITS_ALLOCATED_NP                                     0x81
#define ixD2F1_PCIE_RX_CREDITS_ALLOCATED_CPL                                    0x82
#define ixD2F1_PCIEP_ERROR_INJECT_PHYSICAL                                      0x83
#define ixD2F1_PCIEP_ERROR_INJECT_TRANSACTION                                   0x84
#define ixD2F1_PCIE_LC_CNTL                                                     0xa0
#define ixD2F1_PCIE_LC_CNTL2                                                    0xb1
#define ixD2F1_PCIE_LC_CNTL3                                                    0xb5
#define ixD2F1_PCIE_LC_CNTL4                                                    0xb6
#define ixD2F1_PCIE_LC_CNTL5                                                    0xb7
#define ixD2F1_PCIE_LC_CNTL6                                                    0xbb
#define ixD2F1_PCIE_LC_BW_CHANGE_CNTL                                           0xb2
#define ixD2F1_PCIE_LC_TRAINING_CNTL                                            0xa1
#define ixD2F1_PCIE_LC_LINK_WIDTH_CNTL                                          0xa2
#define ixD2F1_PCIE_LC_N_FTS_CNTL                                               0xa3
#define ixD2F1_PCIE_LC_SPEED_CNTL                                               0xa4
#define ixD2F1_PCIE_LC_CDR_CNTL                                                 0xb3
#define ixD2F1_PCIE_LC_LANE_CNTL                                                0xb4
#define ixD2F1_PCIE_LC_FORCE_COEFF                                              0xb8
#define ixD2F1_PCIE_LC_BEST_EQ_SETTINGS                                         0xb9
#define ixD2F1_PCIE_LC_FORCE_EQ_REQ_COEFF                                       0xba
#define ixD2F1_PCIE_LC_STATE0                                                   0xa5
#define ixD2F1_PCIE_LC_STATE1                                                   0xa6
#define ixD2F1_PCIE_LC_STATE2                                                   0xa7
#define ixD2F1_PCIE_LC_STATE3                                                   0xa8
#define ixD2F1_PCIE_LC_STATE4                                                   0xa9
#define ixD2F1_PCIE_LC_STATE5                                                   0xaa
#define ixD2F1_PCIEP_STRAP_LC                                                   0xc0
#define ixD2F1_PCIEP_STRAP_MISC                                                 0xc1
#define ixD2F1_PCIEP_BCH_ECC_CNTL                                               0xd0
#define ixD2F1_PCIEP_HPGI_PRIVATE                                               0xd2
#define ixD2F1_PCIEP_HPGI                                                       0xda
#define ixD2F1_VENDOR_ID                                                        0x2000000
#define ixD2F1_DEVICE_ID                                                        0x2000000
#define ixD2F1_COMMAND                                                          0x2000001
#define ixD2F1_STATUS                                                           0x2000001
#define ixD2F1_REVISION_ID                                                      0x2000002
#define ixD2F1_PROG_INTERFACE                                                   0x2000002
#define ixD2F1_SUB_CLASS                                                        0x2000002
#define ixD2F1_BASE_CLASS                                                       0x2000002
#define ixD2F1_CACHE_LINE                                                       0x2000003
#define ixD2F1_LATENCY                                                          0x2000003
#define ixD2F1_HEADER                                                           0x2000003
#define ixD2F1_BIST                                                             0x2000003
#define ixD2F1_SUB_BUS_NUMBER_LATENCY                                           0x2000006
#define ixD2F1_IO_BASE_LIMIT                                                    0x2000007
#define ixD2F1_SECONDARY_STATUS                                                 0x2000007
#define ixD2F1_MEM_BASE_LIMIT                                                   0x2000008
#define ixD2F1_PREF_BASE_LIMIT                                                  0x2000009
#define ixD2F1_PREF_BASE_UPPER                                                  0x200000a
#define ixD2F1_PREF_LIMIT_UPPER                                                 0x200000b
#define ixD2F1_IO_BASE_LIMIT_HI                                                 0x200000c
#define ixD2F1_IRQ_BRIDGE_CNTL                                                  0x200000f
#define ixD2F1_CAP_PTR                                                          0x200000d
#define ixD2F1_INTERRUPT_LINE                                                   0x200000f
#define ixD2F1_INTERRUPT_PIN                                                    0x200000f
#define ixD2F1_EXT_BRIDGE_CNTL                                                  0x2000010
#define ixD2F1_PMI_CAP_LIST                                                     0x2000014
#define ixD2F1_PMI_CAP                                                          0x2000014
#define ixD2F1_PMI_STATUS_CNTL                                                  0x2000015
#define ixD2F1_PCIE_CAP_LIST                                                    0x2000016
#define ixD2F1_PCIE_CAP                                                         0x2000016
#define ixD2F1_DEVICE_CAP                                                       0x2000017
#define ixD2F1_DEVICE_CNTL                                                      0x2000018
#define ixD2F1_DEVICE_STATUS                                                    0x2000018
#define ixD2F1_LINK_CAP                                                         0x2000019
#define ixD2F1_LINK_CNTL                                                        0x200001a
#define ixD2F1_LINK_STATUS                                                      0x200001a
#define ixD2F1_SLOT_CAP                                                         0x200001b
#define ixD2F1_SLOT_CNTL                                                        0x200001c
#define ixD2F1_SLOT_STATUS                                                      0x200001c
#define ixD2F1_ROOT_CNTL                                                        0x200001d
#define ixD2F1_ROOT_CAP                                                         0x200001d
#define ixD2F1_ROOT_STATUS                                                      0x200001e
#define ixD2F1_DEVICE_CAP2                                                      0x200001f
#define ixD2F1_DEVICE_CNTL2                                                     0x2000020
#define ixD2F1_DEVICE_STATUS2                                                   0x2000020
#define ixD2F1_LINK_CAP2                                                        0x2000021
#define ixD2F1_LINK_CNTL2                                                       0x2000022
#define ixD2F1_LINK_STATUS2                                                     0x2000022
#define ixD2F1_SLOT_CAP2                                                        0x2000023
#define ixD2F1_SLOT_CNTL2                                                       0x2000024
#define ixD2F1_SLOT_STATUS2                                                     0x2000024
#define ixD2F1_MSI_CAP_LIST                                                     0x2000028
#define ixD2F1_MSI_MSG_CNTL                                                     0x2000028
#define ixD2F1_MSI_MSG_ADDR_LO                                                  0x2000029
#define ixD2F1_MSI_MSG_ADDR_HI                                                  0x200002a
#define ixD2F1_MSI_MSG_DATA_64                                                  0x200002b
#define ixD2F1_MSI_MSG_DATA                                                     0x200002a
#define ixD2F1_SSID_CAP_LIST                                                    0x2000030
#define ixD2F1_SSID_CAP                                                         0x2000031
#define ixD2F1_MSI_MAP_CAP_LIST                                                 0x2000032
#define ixD2F1_MSI_MAP_CAP                                                      0x2000032
#define ixD2F1_MSI_MAP_ADDR_LO                                                  0x2000033
#define ixD2F1_MSI_MAP_ADDR_HI                                                  0x2000034
#define ixD2F1_PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST                                0x2000040
#define ixD2F1_PCIE_VENDOR_SPECIFIC_HDR                                         0x2000041
#define ixD2F1_PCIE_VENDOR_SPECIFIC1                                            0x2000042
#define ixD2F1_PCIE_VENDOR_SPECIFIC2                                            0x2000043
#define ixD2F1_PCIE_VC_ENH_CAP_LIST                                             0x2000044
#define ixD2F1_PCIE_PORT_VC_CAP_REG1                                            0x2000045
#define ixD2F1_PCIE_PORT_VC_CAP_REG2                                            0x2000046
#define ixD2F1_PCIE_PORT_VC_CNTL                                                0x2000047
#define ixD2F1_PCIE_PORT_VC_STATUS                                              0x2000047
#define ixD2F1_PCIE_VC0_RESOURCE_CAP                                            0x2000048
#define ixD2F1_PCIE_VC0_RESOURCE_CNTL                                           0x2000049
#define ixD2F1_PCIE_VC0_RESOURCE_STATUS                                         0x200004a
#define ixD2F1_PCIE_VC1_RESOURCE_CAP                                            0x200004b
#define ixD2F1_PCIE_VC1_RESOURCE_CNTL                                           0x200004c
#define ixD2F1_PCIE_VC1_RESOURCE_STATUS                                         0x200004d
#define ixD2F1_PCIE_DEV_SERIAL_NUM_ENH_CAP_LIST                                 0x2000050
#define ixD2F1_PCIE_DEV_SERIAL_NUM_DW1                                          0x2000051
#define ixD2F1_PCIE_DEV_SERIAL_NUM_DW2                                          0x2000052
#define ixD2F1_PCIE_ADV_ERR_RPT_ENH_CAP_LIST                                    0x2000054
#define ixD2F1_PCIE_UNCORR_ERR_STATUS                                           0x2000055
#define ixD2F1_PCIE_UNCORR_ERR_MASK                                             0x2000056
#define ixD2F1_PCIE_UNCORR_ERR_SEVERITY                                         0x2000057
#define ixD2F1_PCIE_CORR_ERR_STATUS                                             0x2000058
#define ixD2F1_PCIE_CORR_ERR_MASK                                               0x2000059
#define ixD2F1_PCIE_ADV_ERR_CAP_CNTL                                            0x200005a
#define ixD2F1_PCIE_HDR_LOG0                                                    0x200005b
#define ixD2F1_PCIE_HDR_LOG1                                                    0x200005c
#define ixD2F1_PCIE_HDR_LOG2                                                    0x200005d
#define ixD2F1_PCIE_HDR_LOG3                                                    0x200005e
#define ixD2F1_PCIE_ROOT_ERR_CMD                                                0x200005f
#define ixD2F1_PCIE_ROOT_ERR_STATUS                                             0x2000060
#define ixD2F1_PCIE_ERR_SRC_ID                                                  0x2000061
#define ixD2F1_PCIE_TLP_PREFIX_LOG0                                             0x2000062
#define ixD2F1_PCIE_TLP_PREFIX_LOG1                                             0x2000063
#define ixD2F1_PCIE_TLP_PREFIX_LOG2                                             0x2000064
#define ixD2F1_PCIE_TLP_PREFIX_LOG3                                             0x2000065
#define ixD2F1_PCIE_SECONDARY_ENH_CAP_LIST                                      0x200009c
#define ixD2F1_PCIE_LINK_CNTL3                                                  0x200009d
#define ixD2F1_PCIE_LANE_ERROR_STATUS                                           0x200009e
#define ixD2F1_PCIE_LANE_0_EQUALIZATION_CNTL                                    0x200009f
#define ixD2F1_PCIE_LANE_1_EQUALIZATION_CNTL                                    0x200009f
#define ixD2F1_PCIE_LANE_2_EQUALIZATION_CNTL                                    0x20000a0
#define ixD2F1_PCIE_LANE_3_EQUALIZATION_CNTL                                    0x20000a0
#define ixD2F1_PCIE_LANE_4_EQUALIZATION_CNTL                                    0x20000a1
#define ixD2F1_PCIE_LANE_5_EQUALIZATION_CNTL                                    0x20000a1
#define ixD2F1_PCIE_LANE_6_EQUALIZATION_CNTL                                    0x20000a2
#define ixD2F1_PCIE_LANE_7_EQUALIZATION_CNTL                                    0x20000a2
#define ixD2F1_PCIE_LANE_8_EQUALIZATION_CNTL                                    0x20000a3
#define ixD2F1_PCIE_LANE_9_EQUALIZATION_CNTL                                    0x20000a3
#define ixD2F1_PCIE_LANE_10_EQUALIZATION_CNTL                                   0x20000a4
#define ixD2F1_PCIE_LANE_11_EQUALIZATION_CNTL                                   0x20000a4
#define ixD2F1_PCIE_LANE_12_EQUALIZATION_CNTL                                   0x20000a5
#define ixD2F1_PCIE_LANE_13_EQUALIZATION_CNTL                                   0x20000a5
#define ixD2F1_PCIE_LANE_14_EQUALIZATION_CNTL                                   0x20000a6
#define ixD2F1_PCIE_LANE_15_EQUALIZATION_CNTL                                   0x20000a6
#define ixD2F1_PCIE_ACS_ENH_CAP_LIST                                            0x20000a8
#define ixD2F1_PCIE_ACS_CAP                                                     0x20000a9
#define ixD2F1_PCIE_ACS_CNTL                                                    0x20000a9
#define ixD2F1_PCIE_MC_ENH_CAP_LIST                                             0x20000bc
#define ixD2F1_PCIE_MC_CAP                                                      0x20000bd
#define ixD2F1_PCIE_MC_CNTL                                                     0x20000bd
#define ixD2F1_PCIE_MC_ADDR0                                                    0x20000be
#define ixD2F1_PCIE_MC_ADDR1                                                    0x20000bf
#define ixD2F1_PCIE_MC_RCV0                                                     0x20000c0
#define ixD2F1_PCIE_MC_RCV1                                                     0x20000c1
#define ixD2F1_PCIE_MC_BLOCK_ALL0                                               0x20000c2
#define ixD2F1_PCIE_MC_BLOCK_ALL1                                               0x20000c3
#define ixD2F1_PCIE_MC_BLOCK_UNTRANSLATED_0                                     0x20000c4
#define ixD2F1_PCIE_MC_BLOCK_UNTRANSLATED_1                                     0x20000c5
#define ixD2F1_PCIE_MC_OVERLAY_BAR0                                             0x20000c6
#define ixD2F1_PCIE_MC_OVERLAY_BAR1                                             0x20000c7
#define ixD2F2_PCIE_PORT_INDEX                                                  0x3000038
#define ixD2F2_PCIE_PORT_DATA                                                   0x3000039
#define ixD2F2_PCIEP_RESERVED                                                   0x0
#define ixD2F2_PCIEP_SCRATCH                                                    0x1
#define ixD2F2_PCIEP_HW_DEBUG                                                   0x2
#define ixD2F2_PCIEP_PORT_CNTL                                                  0x10
#define ixD2F2_PCIE_TX_CNTL                                                     0x20
#define ixD2F2_PCIE_TX_REQUESTER_ID                                             0x21
#define ixD2F2_PCIE_TX_VENDOR_SPECIFIC                                          0x22
#define ixD2F2_PCIE_TX_REQUEST_NUM_CNTL                                         0x23
#define ixD2F2_PCIE_TX_SEQ                                                      0x24
#define ixD2F2_PCIE_TX_REPLAY                                                   0x25
#define ixD2F2_PCIE_TX_ACK_LATENCY_LIMIT                                        0x26
#define ixD2F2_PCIE_TX_CREDITS_ADVT_P                                           0x30
#define ixD2F2_PCIE_TX_CREDITS_ADVT_NP                                          0x31
#define ixD2F2_PCIE_TX_CREDITS_ADVT_CPL                                         0x32
#define ixD2F2_PCIE_TX_CREDITS_INIT_P                                           0x33
#define ixD2F2_PCIE_TX_CREDITS_INIT_NP                                          0x34
#define ixD2F2_PCIE_TX_CREDITS_INIT_CPL                                         0x35
#define ixD2F2_PCIE_TX_CREDITS_STATUS                                           0x36
#define ixD2F2_PCIE_TX_CREDITS_FCU_THRESHOLD                                    0x37
#define ixD2F2_PCIE_P_PORT_LANE_STATUS                                          0x50
#define ixD2F2_PCIE_FC_P                                                        0x60
#define ixD2F2_PCIE_FC_NP                                                       0x61
#define ixD2F2_PCIE_FC_CPL                                                      0x62
#define ixD2F2_PCIE_ERR_CNTL                                                    0x6a
#define ixD2F2_PCIE_RX_CNTL                                                     0x70
#define ixD2F2_PCIE_RX_EXPECTED_SEQNUM                                          0x71
#define ixD2F2_PCIE_RX_VENDOR_SPECIFIC                                          0x72
#define ixD2F2_PCIE_RX_CNTL3                                                    0x74
#define ixD2F2_PCIE_RX_CREDITS_ALLOCATED_P                                      0x80
#define ixD2F2_PCIE_RX_CREDITS_ALLOCATED_NP                                     0x81
#define ixD2F2_PCIE_RX_CREDITS_ALLOCATED_CPL                                    0x82
#define ixD2F2_PCIEP_ERROR_INJECT_PHYSICAL                                      0x83
#define ixD2F2_PCIEP_ERROR_INJECT_TRANSACTION                                   0x84
#define ixD2F2_PCIE_LC_CNTL                                                     0xa0
#define ixD2F2_PCIE_LC_CNTL2                                                    0xb1
#define ixD2F2_PCIE_LC_CNTL3                                                    0xb5
#define ixD2F2_PCIE_LC_CNTL4                                                    0xb6
#define ixD2F2_PCIE_LC_CNTL5                                                    0xb7
#define ixD2F2_PCIE_LC_CNTL6                                                    0xbb
#define ixD2F2_PCIE_LC_BW_CHANGE_CNTL                                           0xb2
#define ixD2F2_PCIE_LC_TRAINING_CNTL                                            0xa1
#define ixD2F2_PCIE_LC_LINK_WIDTH_CNTL                                          0xa2
#define ixD2F2_PCIE_LC_N_FTS_CNTL                                               0xa3
#define ixD2F2_PCIE_LC_SPEED_CNTL                                               0xa4
#define ixD2F2_PCIE_LC_CDR_CNTL                                                 0xb3
#define ixD2F2_PCIE_LC_LANE_CNTL                                                0xb4
#define ixD2F2_PCIE_LC_FORCE_COEFF                                              0xb8
#define ixD2F2_PCIE_LC_BEST_EQ_SETTINGS                                         0xb9
#define ixD2F2_PCIE_LC_FORCE_EQ_REQ_COEFF                                       0xba
#define ixD2F2_PCIE_LC_STATE0                                                   0xa5
#define ixD2F2_PCIE_LC_STATE1                                                   0xa6
#define ixD2F2_PCIE_LC_STATE2                                                   0xa7
#define ixD2F2_PCIE_LC_STATE3                                                   0xa8
#define ixD2F2_PCIE_LC_STATE4                                                   0xa9
#define ixD2F2_PCIE_LC_STATE5                                                   0xaa
#define ixD2F2_PCIEP_STRAP_LC                                                   0xc0
#define ixD2F2_PCIEP_STRAP_MISC                                                 0xc1
#define ixD2F2_PCIEP_BCH_ECC_CNTL                                               0xd0
#define ixD2F2_PCIEP_HPGI_PRIVATE                                               0xd2
#define ixD2F2_PCIEP_HPGI                                                       0xda
#define ixD2F2_VENDOR_ID                                                        0x3000000
#define ixD2F2_DEVICE_ID                                                        0x3000000
#define ixD2F2_COMMAND                                                          0x3000001
#define ixD2F2_STATUS                                                           0x3000001
#define ixD2F2_REVISION_ID                                                      0x3000002
#define ixD2F2_PROG_INTERFACE                                                   0x3000002
#define ixD2F2_SUB_CLASS                                                        0x3000002
#define ixD2F2_BASE_CLASS                                                       0x3000002
#define ixD2F2_CACHE_LINE                                                       0x3000003
#define ixD2F2_LATENCY                                                          0x3000003
#define ixD2F2_HEADER                                                           0x3000003
#define ixD2F2_BIST                                                             0x3000003
#define ixD2F2_SUB_BUS_NUMBER_LATENCY                                           0x3000006
#define ixD2F2_IO_BASE_LIMIT                                                    0x3000007
#define ixD2F2_SECONDARY_STATUS                                                 0x3000007
#define ixD2F2_MEM_BASE_LIMIT                                                   0x3000008
#define ixD2F2_PREF_BASE_LIMIT                                                  0x3000009
#define ixD2F2_PREF_BASE_UPPER                                                  0x300000a
#define ixD2F2_PREF_LIMIT_UPPER                                                 0x300000b
#define ixD2F2_IO_BASE_LIMIT_HI                                                 0x300000c
#define ixD2F2_IRQ_BRIDGE_CNTL                                                  0x300000f
#define ixD2F2_CAP_PTR                                                          0x300000d
#define ixD2F2_INTERRUPT_LINE                                                   0x300000f
#define ixD2F2_INTERRUPT_PIN                                                    0x300000f
#define ixD2F2_EXT_BRIDGE_CNTL                                                  0x3000010
#define ixD2F2_PMI_CAP_LIST                                                     0x3000014
#define ixD2F2_PMI_CAP                                                          0x3000014
#define ixD2F2_PMI_STATUS_CNTL                                                  0x3000015
#define ixD2F2_PCIE_CAP_LIST                                                    0x3000016
#define ixD2F2_PCIE_CAP                                                         0x3000016
#define ixD2F2_DEVICE_CAP                                                       0x3000017
#define ixD2F2_DEVICE_CNTL                                                      0x3000018
#define ixD2F2_DEVICE_STATUS                                                    0x3000018
#define ixD2F2_LINK_CAP                                                         0x3000019
#define ixD2F2_LINK_CNTL                                                        0x300001a
#define ixD2F2_LINK_STATUS                                                      0x300001a
#define ixD2F2_SLOT_CAP                                                         0x300001b
#define ixD2F2_SLOT_CNTL                                                        0x300001c
#define ixD2F2_SLOT_STATUS                                                      0x300001c
#define ixD2F2_ROOT_CNTL                                                        0x300001d
#define ixD2F2_ROOT_CAP                                                         0x300001d
#define ixD2F2_ROOT_STATUS                                                      0x300001e
#define ixD2F2_DEVICE_CAP2                                                      0x300001f
#define ixD2F2_DEVICE_CNTL2                                                     0x3000020
#define ixD2F2_DEVICE_STATUS2                                                   0x3000020
#define ixD2F2_LINK_CAP2                                                        0x3000021
#define ixD2F2_LINK_CNTL2                                                       0x3000022
#define ixD2F2_LINK_STATUS2                                                     0x3000022
#define ixD2F2_SLOT_CAP2                                                        0x3000023
#define ixD2F2_SLOT_CNTL2                                                       0x3000024
#define ixD2F2_SLOT_STATUS2                                                     0x3000024
#define ixD2F2_MSI_CAP_LIST                                                     0x3000028
#define ixD2F2_MSI_MSG_CNTL                                                     0x3000028
#define ixD2F2_MSI_MSG_ADDR_LO                                                  0x3000029
#define ixD2F2_MSI_MSG_ADDR_HI                                                  0x300002a
#define ixD2F2_MSI_MSG_DATA_64                                                  0x300002b
#define ixD2F2_MSI_MSG_DATA                                                     0x300002a
#define ixD2F2_SSID_CAP_LIST                                                    0x3000030
#define ixD2F2_SSID_CAP                                                         0x3000031
#define ixD2F2_MSI_MAP_CAP_LIST                                                 0x3000032
#define ixD2F2_MSI_MAP_CAP                                                      0x3000032
#define ixD2F2_MSI_MAP_ADDR_LO                                                  0x3000033
#define ixD2F2_MSI_MAP_ADDR_HI                                                  0x3000034
#define ixD2F2_PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST                                0x3000040
#define ixD2F2_PCIE_VENDOR_SPECIFIC_HDR                                         0x3000041
#define ixD2F2_PCIE_VENDOR_SPECIFIC1                                            0x3000042
#define ixD2F2_PCIE_VENDOR_SPECIFIC2                                            0x3000043
#define ixD2F2_PCIE_VC_ENH_CAP_LIST                                             0x3000044
#define ixD2F2_PCIE_PORT_VC_CAP_REG1                                            0x3000045
#define ixD2F2_PCIE_PORT_VC_CAP_REG2                                            0x3000046
#define ixD2F2_PCIE_PORT_VC_CNTL                                                0x3000047
#define ixD2F2_PCIE_PORT_VC_STATUS                                              0x3000047
#define ixD2F2_PCIE_VC0_RESOURCE_CAP                                            0x3000048
#define ixD2F2_PCIE_VC0_RESOURCE_CNTL                                           0x3000049
#define ixD2F2_PCIE_VC0_RESOURCE_STATUS                                         0x300004a
#define ixD2F2_PCIE_VC1_RESOURCE_CAP                                            0x300004b
#define ixD2F2_PCIE_VC1_RESOURCE_CNTL                                           0x300004c
#define ixD2F2_PCIE_VC1_RESOURCE_STATUS                                         0x300004d
#define ixD2F2_PCIE_DEV_SERIAL_NUM_ENH_CAP_LIST                                 0x3000050
#define ixD2F2_PCIE_DEV_SERIAL_NUM_DW1                                          0x3000051
#define ixD2F2_PCIE_DEV_SERIAL_NUM_DW2                                          0x3000052
#define ixD2F2_PCIE_ADV_ERR_RPT_ENH_CAP_LIST                                    0x3000054
#define ixD2F2_PCIE_UNCORR_ERR_STATUS                                           0x3000055
#define ixD2F2_PCIE_UNCORR_ERR_MASK                                             0x3000056
#define ixD2F2_PCIE_UNCORR_ERR_SEVERITY                                         0x3000057
#define ixD2F2_PCIE_CORR_ERR_STATUS                                             0x3000058
#define ixD2F2_PCIE_CORR_ERR_MASK                                               0x3000059
#define ixD2F2_PCIE_ADV_ERR_CAP_CNTL                                            0x300005a
#define ixD2F2_PCIE_HDR_LOG0                                                    0x300005b
#define ixD2F2_PCIE_HDR_LOG1                                                    0x300005c
#define ixD2F2_PCIE_HDR_LOG2                                                    0x300005d
#define ixD2F2_PCIE_HDR_LOG3                                                    0x300005e
#define ixD2F2_PCIE_ROOT_ERR_CMD                                                0x300005f
#define ixD2F2_PCIE_ROOT_ERR_STATUS                                             0x3000060
#define ixD2F2_PCIE_ERR_SRC_ID                                                  0x3000061
#define ixD2F2_PCIE_TLP_PREFIX_LOG0                                             0x3000062
#define ixD2F2_PCIE_TLP_PREFIX_LOG1                                             0x3000063
#define ixD2F2_PCIE_TLP_PREFIX_LOG2                                             0x3000064
#define ixD2F2_PCIE_TLP_PREFIX_LOG3                                             0x3000065
#define ixD2F2_PCIE_SECONDARY_ENH_CAP_LIST                                      0x300009c
#define ixD2F2_PCIE_LINK_CNTL3                                                  0x300009d
#define ixD2F2_PCIE_LANE_ERROR_STATUS                                           0x300009e
#define ixD2F2_PCIE_LANE_0_EQUALIZATION_CNTL                                    0x300009f
#define ixD2F2_PCIE_LANE_1_EQUALIZATION_CNTL                                    0x300009f
#define ixD2F2_PCIE_LANE_2_EQUALIZATION_CNTL                                    0x30000a0
#define ixD2F2_PCIE_LANE_3_EQUALIZATION_CNTL                                    0x30000a0
#define ixD2F2_PCIE_LANE_4_EQUALIZATION_CNTL                                    0x30000a1
#define ixD2F2_PCIE_LANE_5_EQUALIZATION_CNTL                                    0x30000a1
#define ixD2F2_PCIE_LANE_6_EQUALIZATION_CNTL                                    0x30000a2
#define ixD2F2_PCIE_LANE_7_EQUALIZATION_CNTL                                    0x30000a2
#define ixD2F2_PCIE_LANE_8_EQUALIZATION_CNTL                                    0x30000a3
#define ixD2F2_PCIE_LANE_9_EQUALIZATION_CNTL                                    0x30000a3
#define ixD2F2_PCIE_LANE_10_EQUALIZATION_CNTL                                   0x30000a4
#define ixD2F2_PCIE_LANE_11_EQUALIZATION_CNTL                                   0x30000a4
#define ixD2F2_PCIE_LANE_12_EQUALIZATION_CNTL                                   0x30000a5
#define ixD2F2_PCIE_LANE_13_EQUALIZATION_CNTL                                   0x30000a5
#define ixD2F2_PCIE_LANE_14_EQUALIZATION_CNTL                                   0x30000a6
#define ixD2F2_PCIE_LANE_15_EQUALIZATION_CNTL                                   0x30000a6
#define ixD2F2_PCIE_ACS_ENH_CAP_LIST                                            0x30000a8
#define ixD2F2_PCIE_ACS_CAP                                                     0x30000a9
#define ixD2F2_PCIE_ACS_CNTL                                                    0x30000a9
#define ixD2F2_PCIE_MC_ENH_CAP_LIST                                             0x30000bc
#define ixD2F2_PCIE_MC_CAP                                                      0x30000bd
#define ixD2F2_PCIE_MC_CNTL                                                     0x30000bd
#define ixD2F2_PCIE_MC_ADDR0                                                    0x30000be
#define ixD2F2_PCIE_MC_ADDR1                                                    0x30000bf
#define ixD2F2_PCIE_MC_RCV0                                                     0x30000c0
#define ixD2F2_PCIE_MC_RCV1                                                     0x30000c1
#define ixD2F2_PCIE_MC_BLOCK_ALL0                                               0x30000c2
#define ixD2F2_PCIE_MC_BLOCK_ALL1                                               0x30000c3
#define ixD2F2_PCIE_MC_BLOCK_UNTRANSLATED_0                                     0x30000c4
#define ixD2F2_PCIE_MC_BLOCK_UNTRANSLATED_1                                     0x30000c5
#define ixD2F2_PCIE_MC_OVERLAY_BAR0                                             0x30000c6
#define ixD2F2_PCIE_MC_OVERLAY_BAR1                                             0x30000c7
#define ixD2F3_PCIE_PORT_INDEX                                                  0x4000038
#define ixD2F3_PCIE_PORT_DATA                                                   0x4000039
#define ixD2F3_PCIEP_RESERVED                                                   0x0
#define ixD2F3_PCIEP_SCRATCH                                                    0x1
#define ixD2F3_PCIEP_HW_DEBUG                                                   0x2
#define ixD2F3_PCIEP_PORT_CNTL                                                  0x10
#define ixD2F3_PCIE_TX_CNTL                                                     0x20
#define ixD2F3_PCIE_TX_REQUESTER_ID                                             0x21
#define ixD2F3_PCIE_TX_VENDOR_SPECIFIC                                          0x22
#define ixD2F3_PCIE_TX_REQUEST_NUM_CNTL                                         0x23
#define ixD2F3_PCIE_TX_SEQ                                                      0x24
#define ixD2F3_PCIE_TX_REPLAY                                                   0x25
#define ixD2F3_PCIE_TX_ACK_LATENCY_LIMIT                                        0x26
#define ixD2F3_PCIE_TX_CREDITS_ADVT_P                                           0x30
#define ixD2F3_PCIE_TX_CREDITS_ADVT_NP                                          0x31
#define ixD2F3_PCIE_TX_CREDITS_ADVT_CPL                                         0x32
#define ixD2F3_PCIE_TX_CREDITS_INIT_P                                           0x33
#define ixD2F3_PCIE_TX_CREDITS_INIT_NP                                          0x34
#define ixD2F3_PCIE_TX_CREDITS_INIT_CPL                                         0x35
#define ixD2F3_PCIE_TX_CREDITS_STATUS                                           0x36
#define ixD2F3_PCIE_TX_CREDITS_FCU_THRESHOLD                                    0x37
#define ixD2F3_PCIE_P_PORT_LANE_STATUS                                          0x50
#define ixD2F3_PCIE_FC_P                                                        0x60
#define ixD2F3_PCIE_FC_NP                                                       0x61
#define ixD2F3_PCIE_FC_CPL                                                      0x62
#define ixD2F3_PCIE_ERR_CNTL                                                    0x6a
#define ixD2F3_PCIE_RX_CNTL                                                     0x70
#define ixD2F3_PCIE_RX_EXPECTED_SEQNUM                                          0x71
#define ixD2F3_PCIE_RX_VENDOR_SPECIFIC                                          0x72
#define ixD2F3_PCIE_RX_CNTL3                                                    0x74
#define ixD2F3_PCIE_RX_CREDITS_ALLOCATED_P                                      0x80
#define ixD2F3_PCIE_RX_CREDITS_ALLOCATED_NP                                     0x81
#define ixD2F3_PCIE_RX_CREDITS_ALLOCATED_CPL                                    0x82
#define ixD2F3_PCIEP_ERROR_INJECT_PHYSICAL                                      0x83
#define ixD2F3_PCIEP_ERROR_INJECT_TRANSACTION                                   0x84
#define ixD2F3_PCIE_LC_CNTL                                                     0xa0
#define ixD2F3_PCIE_LC_CNTL2                                                    0xb1
#define ixD2F3_PCIE_LC_CNTL3                                                    0xb5
#define ixD2F3_PCIE_LC_CNTL4                                                    0xb6
#define ixD2F3_PCIE_LC_CNTL5                                                    0xb7
#define ixD2F3_PCIE_LC_CNTL6                                                    0xbb
#define ixD2F3_PCIE_LC_BW_CHANGE_CNTL                                           0xb2
#define ixD2F3_PCIE_LC_TRAINING_CNTL                                            0xa1
#define ixD2F3_PCIE_LC_LINK_WIDTH_CNTL                                          0xa2
#define ixD2F3_PCIE_LC_N_FTS_CNTL                                               0xa3
#define ixD2F3_PCIE_LC_SPEED_CNTL                                               0xa4
#define ixD2F3_PCIE_LC_CDR_CNTL                                                 0xb3
#define ixD2F3_PCIE_LC_LANE_CNTL                                                0xb4
#define ixD2F3_PCIE_LC_FORCE_COEFF                                              0xb8
#define ixD2F3_PCIE_LC_BEST_EQ_SETTINGS                                         0xb9
#define ixD2F3_PCIE_LC_FORCE_EQ_REQ_COEFF                                       0xba
#define ixD2F3_PCIE_LC_STATE0                                                   0xa5
#define ixD2F3_PCIE_LC_STATE1                                                   0xa6
#define ixD2F3_PCIE_LC_STATE2                                                   0xa7
#define ixD2F3_PCIE_LC_STATE3                                                   0xa8
#define ixD2F3_PCIE_LC_STATE4                                                   0xa9
#define ixD2F3_PCIE_LC_STATE5                                                   0xaa
#define ixD2F3_PCIEP_STRAP_LC                                                   0xc0
#define ixD2F3_PCIEP_STRAP_MISC                                                 0xc1
#define ixD2F3_PCIEP_BCH_ECC_CNTL                                               0xd0
#define ixD2F3_PCIEP_HPGI_PRIVATE                                               0xd2
#define ixD2F3_PCIEP_HPGI                                                       0xda
#define ixD2F3_VENDOR_ID                                                        0x4000000
#define ixD2F3_DEVICE_ID                                                        0x4000000
#define ixD2F3_COMMAND                                                          0x4000001
#define ixD2F3_STATUS                                                           0x4000001
#define ixD2F3_REVISION_ID                                                      0x4000002
#define ixD2F3_PROG_INTERFACE                                                   0x4000002
#define ixD2F3_SUB_CLASS                                                        0x4000002
#define ixD2F3_BASE_CLASS                                                       0x4000002
#define ixD2F3_CACHE_LINE                                                       0x4000003
#define ixD2F3_LATENCY                                                          0x4000003
#define ixD2F3_HEADER                                                           0x4000003
#define ixD2F3_BIST                                                             0x4000003
#define ixD2F3_SUB_BUS_NUMBER_LATENCY                                           0x4000006
#define ixD2F3_IO_BASE_LIMIT                                                    0x4000007
#define ixD2F3_SECONDARY_STATUS                                                 0x4000007
#define ixD2F3_MEM_BASE_LIMIT                                                   0x4000008
#define ixD2F3_PREF_BASE_LIMIT                                                  0x4000009
#define ixD2F3_PREF_BASE_UPPER                                                  0x400000a
#define ixD2F3_PREF_LIMIT_UPPER                                                 0x400000b
#define ixD2F3_IO_BASE_LIMIT_HI                                                 0x400000c
#define ixD2F3_IRQ_BRIDGE_CNTL                                                  0x400000f
#define ixD2F3_CAP_PTR                                                          0x400000d
#define ixD2F3_INTERRUPT_LINE                                                   0x400000f
#define ixD2F3_INTERRUPT_PIN                                                    0x400000f
#define ixD2F3_EXT_BRIDGE_CNTL                                                  0x4000010
#define ixD2F3_PMI_CAP_LIST                                                     0x4000014
#define ixD2F3_PMI_CAP                                                          0x4000014
#define ixD2F3_PMI_STATUS_CNTL                                                  0x4000015
#define ixD2F3_PCIE_CAP_LIST                                                    0x4000016
#define ixD2F3_PCIE_CAP                                                         0x4000016
#define ixD2F3_DEVICE_CAP                                                       0x4000017
#define ixD2F3_DEVICE_CNTL                                                      0x4000018
#define ixD2F3_DEVICE_STATUS                                                    0x4000018
#define ixD2F3_LINK_CAP                                                         0x4000019
#define ixD2F3_LINK_CNTL                                                        0x400001a
#define ixD2F3_LINK_STATUS                                                      0x400001a
#define ixD2F3_SLOT_CAP                                                         0x400001b
#define ixD2F3_SLOT_CNTL                                                        0x400001c
#define ixD2F3_SLOT_STATUS                                                      0x400001c
#define ixD2F3_ROOT_CNTL                                                        0x400001d
#define ixD2F3_ROOT_CAP                                                         0x400001d
#define ixD2F3_ROOT_STATUS                                                      0x400001e
#define ixD2F3_DEVICE_CAP2                                                      0x400001f
#define ixD2F3_DEVICE_CNTL2                                                     0x4000020
#define ixD2F3_DEVICE_STATUS2                                                   0x4000020
#define ixD2F3_LINK_CAP2                                                        0x4000021
#define ixD2F3_LINK_CNTL2                                                       0x4000022
#define ixD2F3_LINK_STATUS2                                                     0x4000022
#define ixD2F3_SLOT_CAP2                                                        0x4000023
#define ixD2F3_SLOT_CNTL2                                                       0x4000024
#define ixD2F3_SLOT_STATUS2                                                     0x4000024
#define ixD2F3_MSI_CAP_LIST                                                     0x4000028
#define ixD2F3_MSI_MSG_CNTL                                                     0x4000028
#define ixD2F3_MSI_MSG_ADDR_LO                                                  0x4000029
#define ixD2F3_MSI_MSG_ADDR_HI                                                  0x400002a
#define ixD2F3_MSI_MSG_DATA_64                                                  0x400002b
#define ixD2F3_MSI_MSG_DATA                                                     0x400002a
#define ixD2F3_SSID_CAP_LIST                                                    0x4000030
#define ixD2F3_SSID_CAP                                                         0x4000031
#define ixD2F3_MSI_MAP_CAP_LIST                                                 0x4000032
#define ixD2F3_MSI_MAP_CAP                                                      0x4000032
#define ixD2F3_MSI_MAP_ADDR_LO                                                  0x4000033
#define ixD2F3_MSI_MAP_ADDR_HI                                                  0x4000034
#define ixD2F3_PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST                                0x4000040
#define ixD2F3_PCIE_VENDOR_SPECIFIC_HDR                                         0x4000041
#define ixD2F3_PCIE_VENDOR_SPECIFIC1                                            0x4000042
#define ixD2F3_PCIE_VENDOR_SPECIFIC2                                            0x4000043
#define ixD2F3_PCIE_VC_ENH_CAP_LIST                                             0x4000044
#define ixD2F3_PCIE_PORT_VC_CAP_REG1                                            0x4000045
#define ixD2F3_PCIE_PORT_VC_CAP_REG2                                            0x4000046
#define ixD2F3_PCIE_PORT_VC_CNTL                                                0x4000047
#define ixD2F3_PCIE_PORT_VC_STATUS                                              0x4000047
#define ixD2F3_PCIE_VC0_RESOURCE_CAP                                            0x4000048
#define ixD2F3_PCIE_VC0_RESOURCE_CNTL                                           0x4000049
#define ixD2F3_PCIE_VC0_RESOURCE_STATUS                                         0x400004a
#define ixD2F3_PCIE_VC1_RESOURCE_CAP                                            0x400004b
#define ixD2F3_PCIE_VC1_RESOURCE_CNTL                                           0x400004c
#define ixD2F3_PCIE_VC1_RESOURCE_STATUS                                         0x400004d
#define ixD2F3_PCIE_DEV_SERIAL_NUM_ENH_CAP_LIST                                 0x4000050
#define ixD2F3_PCIE_DEV_SERIAL_NUM_DW1                                          0x4000051
#define ixD2F3_PCIE_DEV_SERIAL_NUM_DW2                                          0x4000052
#define ixD2F3_PCIE_ADV_ERR_RPT_ENH_CAP_LIST                                    0x4000054
#define ixD2F3_PCIE_UNCORR_ERR_STATUS                                           0x4000055
#define ixD2F3_PCIE_UNCORR_ERR_MASK                                             0x4000056
#define ixD2F3_PCIE_UNCORR_ERR_SEVERITY                                         0x4000057
#define ixD2F3_PCIE_CORR_ERR_STATUS                                             0x4000058
#define ixD2F3_PCIE_CORR_ERR_MASK                                               0x4000059
#define ixD2F3_PCIE_ADV_ERR_CAP_CNTL                                            0x400005a
#define ixD2F3_PCIE_HDR_LOG0                                                    0x400005b
#define ixD2F3_PCIE_HDR_LOG1                                                    0x400005c
#define ixD2F3_PCIE_HDR_LOG2                                                    0x400005d
#define ixD2F3_PCIE_HDR_LOG3                                                    0x400005e
#define ixD2F3_PCIE_ROOT_ERR_CMD                                                0x400005f
#define ixD2F3_PCIE_ROOT_ERR_STATUS                                             0x4000060
#define ixD2F3_PCIE_ERR_SRC_ID                                                  0x4000061
#define ixD2F3_PCIE_TLP_PREFIX_LOG0                                             0x4000062
#define ixD2F3_PCIE_TLP_PREFIX_LOG1                                             0x4000063
#define ixD2F3_PCIE_TLP_PREFIX_LOG2                                             0x4000064
#define ixD2F3_PCIE_TLP_PREFIX_LOG3                                             0x4000065
#define ixD2F3_PCIE_SECONDARY_ENH_CAP_LIST                                      0x400009c
#define ixD2F3_PCIE_LINK_CNTL3                                                  0x400009d
#define ixD2F3_PCIE_LANE_ERROR_STATUS                                           0x400009e
#define ixD2F3_PCIE_LANE_0_EQUALIZATION_CNTL                                    0x400009f
#define ixD2F3_PCIE_LANE_1_EQUALIZATION_CNTL                                    0x400009f
#define ixD2F3_PCIE_LANE_2_EQUALIZATION_CNTL                                    0x40000a0
#define ixD2F3_PCIE_LANE_3_EQUALIZATION_CNTL                                    0x40000a0
#define ixD2F3_PCIE_LANE_4_EQUALIZATION_CNTL                                    0x40000a1
#define ixD2F3_PCIE_LANE_5_EQUALIZATION_CNTL                                    0x40000a1
#define ixD2F3_PCIE_LANE_6_EQUALIZATION_CNTL                                    0x40000a2
#define ixD2F3_PCIE_LANE_7_EQUALIZATION_CNTL                                    0x40000a2
#define ixD2F3_PCIE_LANE_8_EQUALIZATION_CNTL                                    0x40000a3
#define ixD2F3_PCIE_LANE_9_EQUALIZATION_CNTL                                    0x40000a3
#define ixD2F3_PCIE_LANE_10_EQUALIZATION_CNTL                                   0x40000a4
#define ixD2F3_PCIE_LANE_11_EQUALIZATION_CNTL                                   0x40000a4
#define ixD2F3_PCIE_LANE_12_EQUALIZATION_CNTL                                   0x40000a5
#define ixD2F3_PCIE_LANE_13_EQUALIZATION_CNTL                                   0x40000a5
#define ixD2F3_PCIE_LANE_14_EQUALIZATION_CNTL                                   0x40000a6
#define ixD2F3_PCIE_LANE_15_EQUALIZATION_CNTL                                   0x40000a6
#define ixD2F3_PCIE_ACS_ENH_CAP_LIST                                            0x40000a8
#define ixD2F3_PCIE_ACS_CAP                                                     0x40000a9
#define ixD2F3_PCIE_ACS_CNTL                                                    0x40000a9
#define ixD2F3_PCIE_MC_ENH_CAP_LIST                                             0x40000bc
#define ixD2F3_PCIE_MC_CAP                                                      0x40000bd
#define ixD2F3_PCIE_MC_CNTL                                                     0x40000bd
#define ixD2F3_PCIE_MC_ADDR0                                                    0x40000be
#define ixD2F3_PCIE_MC_ADDR1                                                    0x40000bf
#define ixD2F3_PCIE_MC_RCV0                                                     0x40000c0
#define ixD2F3_PCIE_MC_RCV1                                                     0x40000c1
#define ixD2F3_PCIE_MC_BLOCK_ALL0                                               0x40000c2
#define ixD2F3_PCIE_MC_BLOCK_ALL1                                               0x40000c3
#define ixD2F3_PCIE_MC_BLOCK_UNTRANSLATED_0                                     0x40000c4
#define ixD2F3_PCIE_MC_BLOCK_UNTRANSLATED_1                                     0x40000c5
#define ixD2F3_PCIE_MC_OVERLAY_BAR0                                             0x40000c6
#define ixD2F3_PCIE_MC_OVERLAY_BAR1                                             0x40000c7
#define ixD2F4_PCIE_PORT_INDEX                                                  0x5000038
#define ixD2F4_PCIE_PORT_DATA                                                   0x5000039
#define ixD2F4_PCIEP_RESERVED                                                   0x0
#define ixD2F4_PCIEP_SCRATCH                                                    0x1
#define ixD2F4_PCIEP_HW_DEBUG                                                   0x2
#define ixD2F4_PCIEP_PORT_CNTL                                                  0x10
#define ixD2F4_PCIE_TX_CNTL                                                     0x20
#define ixD2F4_PCIE_TX_REQUESTER_ID                                             0x21
#define ixD2F4_PCIE_TX_VENDOR_SPECIFIC                                          0x22
#define ixD2F4_PCIE_TX_REQUEST_NUM_CNTL                                         0x23
#define ixD2F4_PCIE_TX_SEQ                                                      0x24
#define ixD2F4_PCIE_TX_REPLAY                                                   0x25
#define ixD2F4_PCIE_TX_ACK_LATENCY_LIMIT                                        0x26
#define ixD2F4_PCIE_TX_CREDITS_ADVT_P                                           0x30
#define ixD2F4_PCIE_TX_CREDITS_ADVT_NP                                          0x31
#define ixD2F4_PCIE_TX_CREDITS_ADVT_CPL                                         0x32
#define ixD2F4_PCIE_TX_CREDITS_INIT_P                                           0x33
#define ixD2F4_PCIE_TX_CREDITS_INIT_NP                                          0x34
#define ixD2F4_PCIE_TX_CREDITS_INIT_CPL                                         0x35
#define ixD2F4_PCIE_TX_CREDITS_STATUS                                           0x36
#define ixD2F4_PCIE_TX_CREDITS_FCU_THRESHOLD                                    0x37
#define ixD2F4_PCIE_P_PORT_LANE_STATUS                                          0x50
#define ixD2F4_PCIE_FC_P                                                        0x60
#define ixD2F4_PCIE_FC_NP                                                       0x61
#define ixD2F4_PCIE_FC_CPL                                                      0x62
#define ixD2F4_PCIE_ERR_CNTL                                                    0x6a
#define ixD2F4_PCIE_RX_CNTL                                                     0x70
#define ixD2F4_PCIE_RX_EXPECTED_SEQNUM                                          0x71
#define ixD2F4_PCIE_RX_VENDOR_SPECIFIC                                          0x72
#define ixD2F4_PCIE_RX_CNTL3                                                    0x74
#define ixD2F4_PCIE_RX_CREDITS_ALLOCATED_P                                      0x80
#define ixD2F4_PCIE_RX_CREDITS_ALLOCATED_NP                                     0x81
#define ixD2F4_PCIE_RX_CREDITS_ALLOCATED_CPL                                    0x82
#define ixD2F4_PCIEP_ERROR_INJECT_PHYSICAL                                      0x83
#define ixD2F4_PCIEP_ERROR_INJECT_TRANSACTION                                   0x84
#define ixD2F4_PCIE_LC_CNTL                                                     0xa0
#define ixD2F4_PCIE_LC_CNTL2                                                    0xb1
#define ixD2F4_PCIE_LC_CNTL3                                                    0xb5
#define ixD2F4_PCIE_LC_CNTL4                                                    0xb6
#define ixD2F4_PCIE_LC_CNTL5                                                    0xb7
#define ixD2F4_PCIE_LC_CNTL6                                                    0xbb
#define ixD2F4_PCIE_LC_BW_CHANGE_CNTL                                           0xb2
#define ixD2F4_PCIE_LC_TRAINING_CNTL                                            0xa1
#define ixD2F4_PCIE_LC_LINK_WIDTH_CNTL                                          0xa2
#define ixD2F4_PCIE_LC_N_FTS_CNTL                                               0xa3
#define ixD2F4_PCIE_LC_SPEED_CNTL                                               0xa4
#define ixD2F4_PCIE_LC_CDR_CNTL                                                 0xb3
#define ixD2F4_PCIE_LC_LANE_CNTL                                                0xb4
#define ixD2F4_PCIE_LC_FORCE_COEFF                                              0xb8
#define ixD2F4_PCIE_LC_BEST_EQ_SETTINGS                                         0xb9
#define ixD2F4_PCIE_LC_FORCE_EQ_REQ_COEFF                                       0xba
#define ixD2F4_PCIE_LC_STATE0                                                   0xa5
#define ixD2F4_PCIE_LC_STATE1                                                   0xa6
#define ixD2F4_PCIE_LC_STATE2                                                   0xa7
#define ixD2F4_PCIE_LC_STATE3                                                   0xa8
#define ixD2F4_PCIE_LC_STATE4                                                   0xa9
#define ixD2F4_PCIE_LC_STATE5                                                   0xaa
#define ixD2F4_PCIEP_STRAP_LC                                                   0xc0
#define ixD2F4_PCIEP_STRAP_MISC                                                 0xc1
#define ixD2F4_PCIEP_BCH_ECC_CNTL                                               0xd0
#define ixD2F4_PCIEP_HPGI_PRIVATE                                               0xd2
#define ixD2F4_PCIEP_HPGI                                                       0xda
#define ixD2F4_VENDOR_ID                                                        0x5000000
#define ixD2F4_DEVICE_ID                                                        0x5000000
#define ixD2F4_COMMAND                                                          0x5000001
#define ixD2F4_STATUS                                                           0x5000001
#define ixD2F4_REVISION_ID                                                      0x5000002
#define ixD2F4_PROG_INTERFACE                                                   0x5000002
#define ixD2F4_SUB_CLASS                                                        0x5000002
#define ixD2F4_BASE_CLASS                                                       0x5000002
#define ixD2F4_CACHE_LINE                                                       0x5000003
#define ixD2F4_LATENCY                                                          0x5000003
#define ixD2F4_HEADER                                                           0x5000003
#define ixD2F4_BIST                                                             0x5000003
#define ixD2F4_SUB_BUS_NUMBER_LATENCY                                           0x5000006
#define ixD2F4_IO_BASE_LIMIT                                                    0x5000007
#define ixD2F4_SECONDARY_STATUS                                                 0x5000007
#define ixD2F4_MEM_BASE_LIMIT                                                   0x5000008
#define ixD2F4_PREF_BASE_LIMIT                                                  0x5000009
#define ixD2F4_PREF_BASE_UPPER                                                  0x500000a
#define ixD2F4_PREF_LIMIT_UPPER                                                 0x500000b
#define ixD2F4_IO_BASE_LIMIT_HI                                                 0x500000c
#define ixD2F4_IRQ_BRIDGE_CNTL                                                  0x500000f
#define ixD2F4_CAP_PTR                                                          0x500000d
#define ixD2F4_INTERRUPT_LINE                                                   0x500000f
#define ixD2F4_INTERRUPT_PIN                                                    0x500000f
#define ixD2F4_EXT_BRIDGE_CNTL                                                  0x5000010
#define ixD2F4_PMI_CAP_LIST                                                     0x5000014
#define ixD2F4_PMI_CAP                                                          0x5000014
#define ixD2F4_PMI_STATUS_CNTL                                                  0x5000015
#define ixD2F4_PCIE_CAP_LIST                                                    0x5000016
#define ixD2F4_PCIE_CAP                                                         0x5000016
#define ixD2F4_DEVICE_CAP                                                       0x5000017
#define ixD2F4_DEVICE_CNTL                                                      0x5000018
#define ixD2F4_DEVICE_STATUS                                                    0x5000018
#define ixD2F4_LINK_CAP                                                         0x5000019
#define ixD2F4_LINK_CNTL                                                        0x500001a
#define ixD2F4_LINK_STATUS                                                      0x500001a
#define ixD2F4_SLOT_CAP                                                         0x500001b
#define ixD2F4_SLOT_CNTL                                                        0x500001c
#define ixD2F4_SLOT_STATUS                                                      0x500001c
#define ixD2F4_ROOT_CNTL                                                        0x500001d
#define ixD2F4_ROOT_CAP                                                         0x500001d
#define ixD2F4_ROOT_STATUS                                                      0x500001e
#define ixD2F4_DEVICE_CAP2                                                      0x500001f
#define ixD2F4_DEVICE_CNTL2                                                     0x5000020
#define ixD2F4_DEVICE_STATUS2                                                   0x5000020
#define ixD2F4_LINK_CAP2                                                        0x5000021
#define ixD2F4_LINK_CNTL2                                                       0x5000022
#define ixD2F4_LINK_STATUS2                                                     0x5000022
#define ixD2F4_SLOT_CAP2                                                        0x5000023
#define ixD2F4_SLOT_CNTL2                                                       0x5000024
#define ixD2F4_SLOT_STATUS2                                                     0x5000024
#define ixD2F4_MSI_CAP_LIST                                                     0x5000028
#define ixD2F4_MSI_MSG_CNTL                                                     0x5000028
#define ixD2F4_MSI_MSG_ADDR_LO                                                  0x5000029
#define ixD2F4_MSI_MSG_ADDR_HI                                                  0x500002a
#define ixD2F4_MSI_MSG_DATA_64                                                  0x500002b
#define ixD2F4_MSI_MSG_DATA                                                     0x500002a
#define ixD2F4_SSID_CAP_LIST                                                    0x5000030
#define ixD2F4_SSID_CAP                                                         0x5000031
#define ixD2F4_MSI_MAP_CAP_LIST                                                 0x5000032
#define ixD2F4_MSI_MAP_CAP                                                      0x5000032
#define ixD2F4_MSI_MAP_ADDR_LO                                                  0x5000033
#define ixD2F4_MSI_MAP_ADDR_HI                                                  0x5000034
#define ixD2F4_PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST                                0x5000040
#define ixD2F4_PCIE_VENDOR_SPECIFIC_HDR                                         0x5000041
#define ixD2F4_PCIE_VENDOR_SPECIFIC1                                            0x5000042
#define ixD2F4_PCIE_VENDOR_SPECIFIC2                                            0x5000043
#define ixD2F4_PCIE_VC_ENH_CAP_LIST                                             0x5000044
#define ixD2F4_PCIE_PORT_VC_CAP_REG1                                            0x5000045
#define ixD2F4_PCIE_PORT_VC_CAP_REG2                                            0x5000046
#define ixD2F4_PCIE_PORT_VC_CNTL                                                0x5000047
#define ixD2F4_PCIE_PORT_VC_STATUS                                              0x5000047
#define ixD2F4_PCIE_VC0_RESOURCE_CAP                                            0x5000048
#define ixD2F4_PCIE_VC0_RESOURCE_CNTL                                           0x5000049
#define ixD2F4_PCIE_VC0_RESOURCE_STATUS                                         0x500004a
#define ixD2F4_PCIE_VC1_RESOURCE_CAP                                            0x500004b
#define ixD2F4_PCIE_VC1_RESOURCE_CNTL                                           0x500004c
#define ixD2F4_PCIE_VC1_RESOURCE_STATUS                                         0x500004d
#define ixD2F4_PCIE_DEV_SERIAL_NUM_ENH_CAP_LIST                                 0x5000050
#define ixD2F4_PCIE_DEV_SERIAL_NUM_DW1                                          0x5000051
#define ixD2F4_PCIE_DEV_SERIAL_NUM_DW2                                          0x5000052
#define ixD2F4_PCIE_ADV_ERR_RPT_ENH_CAP_LIST                                    0x5000054
#define ixD2F4_PCIE_UNCORR_ERR_STATUS                                           0x5000055
#define ixD2F4_PCIE_UNCORR_ERR_MASK                                             0x5000056
#define ixD2F4_PCIE_UNCORR_ERR_SEVERITY                                         0x5000057
#define ixD2F4_PCIE_CORR_ERR_STATUS                                             0x5000058
#define ixD2F4_PCIE_CORR_ERR_MASK                                               0x5000059
#define ixD2F4_PCIE_ADV_ERR_CAP_CNTL                                            0x500005a
#define ixD2F4_PCIE_HDR_LOG0                                                    0x500005b
#define ixD2F4_PCIE_HDR_LOG1                                                    0x500005c
#define ixD2F4_PCIE_HDR_LOG2                                                    0x500005d
#define ixD2F4_PCIE_HDR_LOG3                                                    0x500005e
#define ixD2F4_PCIE_ROOT_ERR_CMD                                                0x500005f
#define ixD2F4_PCIE_ROOT_ERR_STATUS                                             0x5000060
#define ixD2F4_PCIE_ERR_SRC_ID                                                  0x5000061
#define ixD2F4_PCIE_TLP_PREFIX_LOG0                                             0x5000062
#define ixD2F4_PCIE_TLP_PREFIX_LOG1                                             0x5000063
#define ixD2F4_PCIE_TLP_PREFIX_LOG2                                             0x5000064
#define ixD2F4_PCIE_TLP_PREFIX_LOG3                                             0x5000065
#define ixD2F4_PCIE_SECONDARY_ENH_CAP_LIST                                      0x500009c
#define ixD2F4_PCIE_LINK_CNTL3                                                  0x500009d
#define ixD2F4_PCIE_LANE_ERROR_STATUS                                           0x500009e
#define ixD2F4_PCIE_LANE_0_EQUALIZATION_CNTL                                    0x500009f
#define ixD2F4_PCIE_LANE_1_EQUALIZATION_CNTL                                    0x500009f
#define ixD2F4_PCIE_LANE_2_EQUALIZATION_CNTL                                    0x50000a0
#define ixD2F4_PCIE_LANE_3_EQUALIZATION_CNTL                                    0x50000a0
#define ixD2F4_PCIE_LANE_4_EQUALIZATION_CNTL                                    0x50000a1
#define ixD2F4_PCIE_LANE_5_EQUALIZATION_CNTL                                    0x50000a1
#define ixD2F4_PCIE_LANE_6_EQUALIZATION_CNTL                                    0x50000a2
#define ixD2F4_PCIE_LANE_7_EQUALIZATION_CNTL                                    0x50000a2
#define ixD2F4_PCIE_LANE_8_EQUALIZATION_CNTL                                    0x50000a3
#define ixD2F4_PCIE_LANE_9_EQUALIZATION_CNTL                                    0x50000a3
#define ixD2F4_PCIE_LANE_10_EQUALIZATION_CNTL                                   0x50000a4
#define ixD2F4_PCIE_LANE_11_EQUALIZATION_CNTL                                   0x50000a4
#define ixD2F4_PCIE_LANE_12_EQUALIZATION_CNTL                                   0x50000a5
#define ixD2F4_PCIE_LANE_13_EQUALIZATION_CNTL                                   0x50000a5
#define ixD2F4_PCIE_LANE_14_EQUALIZATION_CNTL                                   0x50000a6
#define ixD2F4_PCIE_LANE_15_EQUALIZATION_CNTL                                   0x50000a6
#define ixD2F4_PCIE_ACS_ENH_CAP_LIST                                            0x50000a8
#define ixD2F4_PCIE_ACS_CAP                                                     0x50000a9
#define ixD2F4_PCIE_ACS_CNTL                                                    0x50000a9
#define ixD2F4_PCIE_MC_ENH_CAP_LIST                                             0x50000bc
#define ixD2F4_PCIE_MC_CAP                                                      0x50000bd
#define ixD2F4_PCIE_MC_CNTL                                                     0x50000bd
#define ixD2F4_PCIE_MC_ADDR0                                                    0x50000be
#define ixD2F4_PCIE_MC_ADDR1                                                    0x50000bf
#define ixD2F4_PCIE_MC_RCV0                                                     0x50000c0
#define ixD2F4_PCIE_MC_RCV1                                                     0x50000c1
#define ixD2F4_PCIE_MC_BLOCK_ALL0                                               0x50000c2
#define ixD2F4_PCIE_MC_BLOCK_ALL1                                               0x50000c3
#define ixD2F4_PCIE_MC_BLOCK_UNTRANSLATED_0                                     0x50000c4
#define ixD2F4_PCIE_MC_BLOCK_UNTRANSLATED_1                                     0x50000c5
#define ixD2F4_PCIE_MC_OVERLAY_BAR0                                             0x50000c6
#define ixD2F4_PCIE_MC_OVERLAY_BAR1                                             0x50000c7
#define ixD2F5_PCIE_PORT_INDEX                                                  0x6000038
#define ixD2F5_PCIE_PORT_DATA                                                   0x6000039
#define ixD2F5_PCIEP_RESERVED                                                   0x0
#define ixD2F5_PCIEP_SCRATCH                                                    0x1
#define ixD2F5_PCIEP_HW_DEBUG                                                   0x2
#define ixD2F5_PCIEP_PORT_CNTL                                                  0x10
#define ixD2F5_PCIE_TX_CNTL                                                     0x20
#define ixD2F5_PCIE_TX_REQUESTER_ID                                             0x21
#define ixD2F5_PCIE_TX_VENDOR_SPECIFIC                                          0x22
#define ixD2F5_PCIE_TX_REQUEST_NUM_CNTL                                         0x23
#define ixD2F5_PCIE_TX_SEQ                                                      0x24
#define ixD2F5_PCIE_TX_REPLAY                                                   0x25
#define ixD2F5_PCIE_TX_ACK_LATENCY_LIMIT                                        0x26
#define ixD2F5_PCIE_TX_CREDITS_ADVT_P                                           0x30
#define ixD2F5_PCIE_TX_CREDITS_ADVT_NP                                          0x31
#define ixD2F5_PCIE_TX_CREDITS_ADVT_CPL                                         0x32
#define ixD2F5_PCIE_TX_CREDITS_INIT_P                                           0x33
#define ixD2F5_PCIE_TX_CREDITS_INIT_NP                                          0x34
#define ixD2F5_PCIE_TX_CREDITS_INIT_CPL                                         0x35
#define ixD2F5_PCIE_TX_CREDITS_STATUS                                           0x36
#define ixD2F5_PCIE_TX_CREDITS_FCU_THRESHOLD                                    0x37
#define ixD2F5_PCIE_P_PORT_LANE_STATUS                                          0x50
#define ixD2F5_PCIE_FC_P                                                        0x60
#define ixD2F5_PCIE_FC_NP                                                       0x61
#define ixD2F5_PCIE_FC_CPL                                                      0x62
#define ixD2F5_PCIE_ERR_CNTL                                                    0x6a
#define ixD2F5_PCIE_RX_CNTL                                                     0x70
#define ixD2F5_PCIE_RX_EXPECTED_SEQNUM                                          0x71
#define ixD2F5_PCIE_RX_VENDOR_SPECIFIC                                          0x72
#define ixD2F5_PCIE_RX_CNTL3                                                    0x74
#define ixD2F5_PCIE_RX_CREDITS_ALLOCATED_P                                      0x80
#define ixD2F5_PCIE_RX_CREDITS_ALLOCATED_NP                                     0x81
#define ixD2F5_PCIE_RX_CREDITS_ALLOCATED_CPL                                    0x82
#define ixD2F5_PCIEP_ERROR_INJECT_PHYSICAL                                      0x83
#define ixD2F5_PCIEP_ERROR_INJECT_TRANSACTION                                   0x84
#define ixD2F5_PCIE_LC_CNTL                                                     0xa0
#define ixD2F5_PCIE_LC_CNTL2                                                    0xb1
#define ixD2F5_PCIE_LC_CNTL3                                                    0xb5
#define ixD2F5_PCIE_LC_CNTL4                                                    0xb6
#define ixD2F5_PCIE_LC_CNTL5                                                    0xb7
#define ixD2F5_PCIE_LC_CNTL6                                                    0xbb
#define ixD2F5_PCIE_LC_BW_CHANGE_CNTL                                           0xb2
#define ixD2F5_PCIE_LC_TRAINING_CNTL                                            0xa1
#define ixD2F5_PCIE_LC_LINK_WIDTH_CNTL                                          0xa2
#define ixD2F5_PCIE_LC_N_FTS_CNTL                                               0xa3
#define ixD2F5_PCIE_LC_SPEED_CNTL                                               0xa4
#define ixD2F5_PCIE_LC_CDR_CNTL                                                 0xb3
#define ixD2F5_PCIE_LC_LANE_CNTL                                                0xb4
#define ixD2F5_PCIE_LC_FORCE_COEFF                                              0xb8
#define ixD2F5_PCIE_LC_BEST_EQ_SETTINGS                                         0xb9
#define ixD2F5_PCIE_LC_FORCE_EQ_REQ_COEFF                                       0xba
#define ixD2F5_PCIE_LC_STATE0                                                   0xa5
#define ixD2F5_PCIE_LC_STATE1                                                   0xa6
#define ixD2F5_PCIE_LC_STATE2                                                   0xa7
#define ixD2F5_PCIE_LC_STATE3                                                   0xa8
#define ixD2F5_PCIE_LC_STATE4                                                   0xa9
#define ixD2F5_PCIE_LC_STATE5                                                   0xaa
#define ixD2F5_PCIEP_STRAP_LC                                                   0xc0
#define ixD2F5_PCIEP_STRAP_MISC                                                 0xc1
#define ixD2F5_PCIEP_BCH_ECC_CNTL                                               0xd0
#define ixD2F5_PCIEP_HPGI_PRIVATE                                               0xd2
#define ixD2F5_PCIEP_HPGI                                                       0xda
#define ixD2F5_VENDOR_ID                                                        0x6000000
#define ixD2F5_DEVICE_ID                                                        0x6000000
#define ixD2F5_COMMAND                                                          0x6000001
#define ixD2F5_STATUS                                                           0x6000001
#define ixD2F5_REVISION_ID                                                      0x6000002
#define ixD2F5_PROG_INTERFACE                                                   0x6000002
#define ixD2F5_SUB_CLASS                                                        0x6000002
#define ixD2F5_BASE_CLASS                                                       0x6000002
#define ixD2F5_CACHE_LINE                                                       0x6000003
#define ixD2F5_LATENCY                                                          0x6000003
#define ixD2F5_HEADER                                                           0x6000003
#define ixD2F5_BIST                                                             0x6000003
#define ixD2F5_SUB_BUS_NUMBER_LATENCY                                           0x6000006
#define ixD2F5_IO_BASE_LIMIT                                                    0x6000007
#define ixD2F5_SECONDARY_STATUS                                                 0x6000007
#define ixD2F5_MEM_BASE_LIMIT                                                   0x6000008
#define ixD2F5_PREF_BASE_LIMIT                                                  0x6000009
#define ixD2F5_PREF_BASE_UPPER                                                  0x600000a
#define ixD2F5_PREF_LIMIT_UPPER                                                 0x600000b
#define ixD2F5_IO_BASE_LIMIT_HI                                                 0x600000c
#define ixD2F5_IRQ_BRIDGE_CNTL                                                  0x600000f
#define ixD2F5_CAP_PTR                                                          0x600000d
#define ixD2F5_INTERRUPT_LINE                                                   0x600000f
#define ixD2F5_INTERRUPT_PIN                                                    0x600000f
#define ixD2F5_EXT_BRIDGE_CNTL                                                  0x6000010
#define ixD2F5_PMI_CAP_LIST                                                     0x6000014
#define ixD2F5_PMI_CAP                                                          0x6000014
#define ixD2F5_PMI_STATUS_CNTL                                                  0x6000015
#define ixD2F5_PCIE_CAP_LIST                                                    0x6000016
#define ixD2F5_PCIE_CAP                                                         0x6000016
#define ixD2F5_DEVICE_CAP                                                       0x6000017
#define ixD2F5_DEVICE_CNTL                                                      0x6000018
#define ixD2F5_DEVICE_STATUS                                                    0x6000018
#define ixD2F5_LINK_CAP                                                         0x6000019
#define ixD2F5_LINK_CNTL                                                        0x600001a
#define ixD2F5_LINK_STATUS                                                      0x600001a
#define ixD2F5_SLOT_CAP                                                         0x600001b
#define ixD2F5_SLOT_CNTL                                                        0x600001c
#define ixD2F5_SLOT_STATUS                                                      0x600001c
#define ixD2F5_ROOT_CNTL                                                        0x600001d
#define ixD2F5_ROOT_CAP                                                         0x600001d
#define ixD2F5_ROOT_STATUS                                                      0x600001e
#define ixD2F5_DEVICE_CAP2                                                      0x600001f
#define ixD2F5_DEVICE_CNTL2                                                     0x6000020
#define ixD2F5_DEVICE_STATUS2                                                   0x6000020
#define ixD2F5_LINK_CAP2                                                        0x6000021
#define ixD2F5_LINK_CNTL2                                                       0x6000022
#define ixD2F5_LINK_STATUS2                                                     0x6000022
#define ixD2F5_SLOT_CAP2                                                        0x6000023
#define ixD2F5_SLOT_CNTL2                                                       0x6000024
#define ixD2F5_SLOT_STATUS2                                                     0x6000024
#define ixD2F5_MSI_CAP_LIST                                                     0x6000028
#define ixD2F5_MSI_MSG_CNTL                                                     0x6000028
#define ixD2F5_MSI_MSG_ADDR_LO                                                  0x6000029
#define ixD2F5_MSI_MSG_ADDR_HI                                                  0x600002a
#define ixD2F5_MSI_MSG_DATA_64                                                  0x600002b
#define ixD2F5_MSI_MSG_DATA                                                     0x600002a
#define ixD2F5_SSID_CAP_LIST                                                    0x6000030
#define ixD2F5_SSID_CAP                                                         0x6000031
#define ixD2F5_MSI_MAP_CAP_LIST                                                 0x6000032
#define ixD2F5_MSI_MAP_CAP                                                      0x6000032
#define ixD2F5_MSI_MAP_ADDR_LO                                                  0x6000033
#define ixD2F5_MSI_MAP_ADDR_HI                                                  0x6000034
#define ixD2F5_PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST                                0x6000040
#define ixD2F5_PCIE_VENDOR_SPECIFIC_HDR                                         0x6000041
#define ixD2F5_PCIE_VENDOR_SPECIFIC1                                            0x6000042
#define ixD2F5_PCIE_VENDOR_SPECIFIC2                                            0x6000043
#define ixD2F5_PCIE_VC_ENH_CAP_LIST                                             0x6000044
#define ixD2F5_PCIE_PORT_VC_CAP_REG1                                            0x6000045
#define ixD2F5_PCIE_PORT_VC_CAP_REG2                                            0x6000046
#define ixD2F5_PCIE_PORT_VC_CNTL                                                0x6000047
#define ixD2F5_PCIE_PORT_VC_STATUS                                              0x6000047
#define ixD2F5_PCIE_VC0_RESOURCE_CAP                                            0x6000048
#define ixD2F5_PCIE_VC0_RESOURCE_CNTL                                           0x6000049
#define ixD2F5_PCIE_VC0_RESOURCE_STATUS                                         0x600004a
#define ixD2F5_PCIE_VC1_RESOURCE_CAP                                            0x600004b
#define ixD2F5_PCIE_VC1_RESOURCE_CNTL                                           0x600004c
#define ixD2F5_PCIE_VC1_RESOURCE_STATUS                                         0x600004d
#define ixD2F5_PCIE_DEV_SERIAL_NUM_ENH_CAP_LIST                                 0x6000050
#define ixD2F5_PCIE_DEV_SERIAL_NUM_DW1                                          0x6000051
#define ixD2F5_PCIE_DEV_SERIAL_NUM_DW2                                          0x6000052
#define ixD2F5_PCIE_ADV_ERR_RPT_ENH_CAP_LIST                                    0x6000054
#define ixD2F5_PCIE_UNCORR_ERR_STATUS                                           0x6000055
#define ixD2F5_PCIE_UNCORR_ERR_MASK                                             0x6000056
#define ixD2F5_PCIE_UNCORR_ERR_SEVERITY                                         0x6000057
#define ixD2F5_PCIE_CORR_ERR_STATUS                                             0x6000058
#define ixD2F5_PCIE_CORR_ERR_MASK                                               0x6000059
#define ixD2F5_PCIE_ADV_ERR_CAP_CNTL                                            0x600005a
#define ixD2F5_PCIE_HDR_LOG0                                                    0x600005b
#define ixD2F5_PCIE_HDR_LOG1                                                    0x600005c
#define ixD2F5_PCIE_HDR_LOG2                                                    0x600005d
#define ixD2F5_PCIE_HDR_LOG3                                                    0x600005e
#define ixD2F5_PCIE_ROOT_ERR_CMD                                                0x600005f
#define ixD2F5_PCIE_ROOT_ERR_STATUS                                             0x6000060
#define ixD2F5_PCIE_ERR_SRC_ID                                                  0x6000061
#define ixD2F5_PCIE_TLP_PREFIX_LOG0                                             0x6000062
#define ixD2F5_PCIE_TLP_PREFIX_LOG1                                             0x6000063
#define ixD2F5_PCIE_TLP_PREFIX_LOG2                                             0x6000064
#define ixD2F5_PCIE_TLP_PREFIX_LOG3                                             0x6000065
#define ixD2F5_PCIE_SECONDARY_ENH_CAP_LIST                                      0x600009c
#define ixD2F5_PCIE_LINK_CNTL3                                                  0x600009d
#define ixD2F5_PCIE_LANE_ERROR_STATUS                                           0x600009e
#define ixD2F5_PCIE_LANE_0_EQUALIZATION_CNTL                                    0x600009f
#define ixD2F5_PCIE_LANE_1_EQUALIZATION_CNTL                                    0x600009f
#define ixD2F5_PCIE_LANE_2_EQUALIZATION_CNTL                                    0x60000a0
#define ixD2F5_PCIE_LANE_3_EQUALIZATION_CNTL                                    0x60000a0
#define ixD2F5_PCIE_LANE_4_EQUALIZATION_CNTL                                    0x60000a1
#define ixD2F5_PCIE_LANE_5_EQUALIZATION_CNTL                                    0x60000a1
#define ixD2F5_PCIE_LANE_6_EQUALIZATION_CNTL                                    0x60000a2
#define ixD2F5_PCIE_LANE_7_EQUALIZATION_CNTL                                    0x60000a2
#define ixD2F5_PCIE_LANE_8_EQUALIZATION_CNTL                                    0x60000a3
#define ixD2F5_PCIE_LANE_9_EQUALIZATION_CNTL                                    0x60000a3
#define ixD2F5_PCIE_LANE_10_EQUALIZATION_CNTL                                   0x60000a4
#define ixD2F5_PCIE_LANE_11_EQUALIZATION_CNTL                                   0x60000a4
#define ixD2F5_PCIE_LANE_12_EQUALIZATION_CNTL                                   0x60000a5
#define ixD2F5_PCIE_LANE_13_EQUALIZATION_CNTL                                   0x60000a5
#define ixD2F5_PCIE_LANE_14_EQUALIZATION_CNTL                                   0x60000a6
#define ixD2F5_PCIE_LANE_15_EQUALIZATION_CNTL                                   0x60000a6
#define ixD2F5_PCIE_ACS_ENH_CAP_LIST                                            0x60000a8
#define ixD2F5_PCIE_ACS_CAP                                                     0x60000a9
#define ixD2F5_PCIE_ACS_CNTL                                                    0x60000a9
#define ixD2F5_PCIE_MC_ENH_CAP_LIST                                             0x60000bc
#define ixD2F5_PCIE_MC_CAP                                                      0x60000bd
#define ixD2F5_PCIE_MC_CNTL                                                     0x60000bd
#define ixD2F5_PCIE_MC_ADDR0                                                    0x60000be
#define ixD2F5_PCIE_MC_ADDR1                                                    0x60000bf
#define ixD2F5_PCIE_MC_RCV0                                                     0x60000c0
#define ixD2F5_PCIE_MC_RCV1                                                     0x60000c1
#define ixD2F5_PCIE_MC_BLOCK_ALL0                                               0x60000c2
#define ixD2F5_PCIE_MC_BLOCK_ALL1                                               0x60000c3
#define ixD2F5_PCIE_MC_BLOCK_UNTRANSLATED_0                                     0x60000c4
#define ixD2F5_PCIE_MC_BLOCK_UNTRANSLATED_1                                     0x60000c5
#define ixD2F5_PCIE_MC_OVERLAY_BAR0                                             0x60000c6
#define ixD2F5_PCIE_MC_OVERLAY_BAR1                                             0x60000c7
#define ixD3F1_PCIE_PORT_INDEX                                                  0x7000038
#define ixD3F1_PCIE_PORT_DATA                                                   0x7000039
#define ixD3F1_PCIEP_RESERVED                                                   0x0
#define ixD3F1_PCIEP_SCRATCH                                                    0x1
#define ixD3F1_PCIEP_HW_DEBUG                                                   0x2
#define ixD3F1_PCIEP_PORT_CNTL                                                  0x10
#define ixD3F1_PCIE_TX_CNTL                                                     0x20
#define ixD3F1_PCIE_TX_REQUESTER_ID                                             0x21
#define ixD3F1_PCIE_TX_VENDOR_SPECIFIC                                          0x22
#define ixD3F1_PCIE_TX_REQUEST_NUM_CNTL                                         0x23
#define ixD3F1_PCIE_TX_SEQ                                                      0x24
#define ixD3F1_PCIE_TX_REPLAY                                                   0x25
#define ixD3F1_PCIE_TX_ACK_LATENCY_LIMIT                                        0x26
#define ixD3F1_PCIE_TX_CREDITS_ADVT_P                                           0x30
#define ixD3F1_PCIE_TX_CREDITS_ADVT_NP                                          0x31
#define ixD3F1_PCIE_TX_CREDITS_ADVT_CPL                                         0x32
#define ixD3F1_PCIE_TX_CREDITS_INIT_P                                           0x33
#define ixD3F1_PCIE_TX_CREDITS_INIT_NP                                          0x34
#define ixD3F1_PCIE_TX_CREDITS_INIT_CPL                                         0x35
#define ixD3F1_PCIE_TX_CREDITS_STATUS                                           0x36
#define ixD3F1_PCIE_TX_CREDITS_FCU_THRESHOLD                                    0x37
#define ixD3F1_PCIE_P_PORT_LANE_STATUS                                          0x50
#define ixD3F1_PCIE_FC_P                                                        0x60
#define ixD3F1_PCIE_FC_NP                                                       0x61
#define ixD3F1_PCIE_FC_CPL                                                      0x62
#define ixD3F1_PCIE_ERR_CNTL                                                    0x6a
#define ixD3F1_PCIE_RX_CNTL                                                     0x70
#define ixD3F1_PCIE_RX_EXPECTED_SEQNUM                                          0x71
#define ixD3F1_PCIE_RX_VENDOR_SPECIFIC                                          0x72
#define ixD3F1_PCIE_RX_CNTL3                                                    0x74
#define ixD3F1_PCIE_RX_CREDITS_ALLOCATED_P                                      0x80
#define ixD3F1_PCIE_RX_CREDITS_ALLOCATED_NP                                     0x81
#define ixD3F1_PCIE_RX_CREDITS_ALLOCATED_CPL                                    0x82
#define ixD3F1_PCIEP_ERROR_INJECT_PHYSICAL                                      0x83
#define ixD3F1_PCIEP_ERROR_INJECT_TRANSACTION                                   0x84
#define ixD3F1_PCIE_LC_CNTL                                                     0xa0
#define ixD3F1_PCIE_LC_CNTL2                                                    0xb1
#define ixD3F1_PCIE_LC_CNTL3                                                    0xb5
#define ixD3F1_PCIE_LC_CNTL4                                                    0xb6
#define ixD3F1_PCIE_LC_CNTL5                                                    0xb7
#define ixD3F1_PCIE_LC_CNTL6                                                    0xbb
#define ixD3F1_PCIE_LC_BW_CHANGE_CNTL                                           0xb2
#define ixD3F1_PCIE_LC_TRAINING_CNTL                                            0xa1
#define ixD3F1_PCIE_LC_LINK_WIDTH_CNTL                                          0xa2
#define ixD3F1_PCIE_LC_N_FTS_CNTL                                               0xa3
#define ixD3F1_PCIE_LC_SPEED_CNTL                                               0xa4
#define ixD3F1_PCIE_LC_CDR_CNTL                                                 0xb3
#define ixD3F1_PCIE_LC_LANE_CNTL                                                0xb4
#define ixD3F1_PCIE_LC_FORCE_COEFF                                              0xb8
#define ixD3F1_PCIE_LC_BEST_EQ_SETTINGS                                         0xb9
#define ixD3F1_PCIE_LC_FORCE_EQ_REQ_COEFF                                       0xba
#define ixD3F1_PCIE_LC_STATE0                                                   0xa5
#define ixD3F1_PCIE_LC_STATE1                                                   0xa6
#define ixD3F1_PCIE_LC_STATE2                                                   0xa7
#define ixD3F1_PCIE_LC_STATE3                                                   0xa8
#define ixD3F1_PCIE_LC_STATE4                                                   0xa9
#define ixD3F1_PCIE_LC_STATE5                                                   0xaa
#define ixD3F1_PCIEP_STRAP_LC                                                   0xc0
#define ixD3F1_PCIEP_STRAP_MISC                                                 0xc1
#define ixD3F1_PCIEP_BCH_ECC_CNTL                                               0xd0
#define ixD3F1_PCIEP_HPGI_PRIVATE                                               0xd2
#define ixD3F1_PCIEP_HPGI                                                       0xda
#define ixD3F1_VENDOR_ID                                                        0x7000000
#define ixD3F1_DEVICE_ID                                                        0x7000000
#define ixD3F1_COMMAND                                                          0x7000001
#define ixD3F1_STATUS                                                           0x7000001
#define ixD3F1_REVISION_ID                                                      0x7000002
#define ixD3F1_PROG_INTERFACE                                                   0x7000002
#define ixD3F1_SUB_CLASS                                                        0x7000002
#define ixD3F1_BASE_CLASS                                                       0x7000002
#define ixD3F1_CACHE_LINE                                                       0x7000003
#define ixD3F1_LATENCY                                                          0x7000003
#define ixD3F1_HEADER                                                           0x7000003
#define ixD3F1_BIST                                                             0x7000003
#define ixD3F1_SUB_BUS_NUMBER_LATENCY                                           0x7000006
#define ixD3F1_IO_BASE_LIMIT                                                    0x7000007
#define ixD3F1_SECONDARY_STATUS                                                 0x7000007
#define ixD3F1_MEM_BASE_LIMIT                                                   0x7000008
#define ixD3F1_PREF_BASE_LIMIT                                                  0x7000009
#define ixD3F1_PREF_BASE_UPPER                                                  0x700000a
#define ixD3F1_PREF_LIMIT_UPPER                                                 0x700000b
#define ixD3F1_IO_BASE_LIMIT_HI                                                 0x700000c
#define ixD3F1_IRQ_BRIDGE_CNTL                                                  0x700000f
#define ixD3F1_CAP_PTR                                                          0x700000d
#define ixD3F1_INTERRUPT_LINE                                                   0x700000f
#define ixD3F1_INTERRUPT_PIN                                                    0x700000f
#define ixD3F1_EXT_BRIDGE_CNTL                                                  0x7000010
#define ixD3F1_PMI_CAP_LIST                                                     0x7000014
#define ixD3F1_PMI_CAP                                                          0x7000014
#define ixD3F1_PMI_STATUS_CNTL                                                  0x7000015
#define ixD3F1_PCIE_CAP_LIST                                                    0x7000016
#define ixD3F1_PCIE_CAP                                                         0x7000016
#define ixD3F1_DEVICE_CAP                                                       0x7000017
#define ixD3F1_DEVICE_CNTL                                                      0x7000018
#define ixD3F1_DEVICE_STATUS                                                    0x7000018
#define ixD3F1_LINK_CAP                                                         0x7000019
#define ixD3F1_LINK_CNTL                                                        0x700001a
#define ixD3F1_LINK_STATUS                                                      0x700001a
#define ixD3F1_SLOT_CAP                                                         0x700001b
#define ixD3F1_SLOT_CNTL                                                        0x700001c
#define ixD3F1_SLOT_STATUS                                                      0x700001c
#define ixD3F1_ROOT_CNTL                                                        0x700001d
#define ixD3F1_ROOT_CAP                                                         0x700001d
#define ixD3F1_ROOT_STATUS                                                      0x700001e
#define ixD3F1_DEVICE_CAP2                                                      0x700001f
#define ixD3F1_DEVICE_CNTL2                                                     0x7000020
#define ixD3F1_DEVICE_STATUS2                                                   0x7000020
#define ixD3F1_LINK_CAP2                                                        0x7000021
#define ixD3F1_LINK_CNTL2                                                       0x7000022
#define ixD3F1_LINK_STATUS2                                                     0x7000022
#define ixD3F1_SLOT_CAP2                                                        0x7000023
#define ixD3F1_SLOT_CNTL2                                                       0x7000024
#define ixD3F1_SLOT_STATUS2                                                     0x7000024
#define ixD3F1_MSI_CAP_LIST                                                     0x7000028
#define ixD3F1_MSI_MSG_CNTL                                                     0x7000028
#define ixD3F1_MSI_MSG_ADDR_LO                                                  0x7000029
#define ixD3F1_MSI_MSG_ADDR_HI                                                  0x700002a
#define ixD3F1_MSI_MSG_DATA_64                                                  0x700002b
#define ixD3F1_MSI_MSG_DATA                                                     0x700002a
#define ixD3F1_SSID_CAP_LIST                                                    0x7000030
#define ixD3F1_SSID_CAP                                                         0x7000031
#define ixD3F1_MSI_MAP_CAP_LIST                                                 0x7000032
#define ixD3F1_MSI_MAP_CAP                                                      0x7000032
#define ixD3F1_MSI_MAP_ADDR_LO                                                  0x7000033
#define ixD3F1_MSI_MAP_ADDR_HI                                                  0x7000034
#define ixD3F1_PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST                                0x7000040
#define ixD3F1_PCIE_VENDOR_SPECIFIC_HDR                                         0x7000041
#define ixD3F1_PCIE_VENDOR_SPECIFIC1                                            0x7000042
#define ixD3F1_PCIE_VENDOR_SPECIFIC2                                            0x7000043
#define ixD3F1_PCIE_VC_ENH_CAP_LIST                                             0x7000044
#define ixD3F1_PCIE_PORT_VC_CAP_REG1                                            0x7000045
#define ixD3F1_PCIE_PORT_VC_CAP_REG2                                            0x7000046
#define ixD3F1_PCIE_PORT_VC_CNTL                                                0x7000047
#define ixD3F1_PCIE_PORT_VC_STATUS                                              0x7000047
#define ixD3F1_PCIE_VC0_RESOURCE_CAP                                            0x7000048
#define ixD3F1_PCIE_VC0_RESOURCE_CNTL                                           0x7000049
#define ixD3F1_PCIE_VC0_RESOURCE_STATUS                                         0x700004a
#define ixD3F1_PCIE_VC1_RESOURCE_CAP                                            0x700004b
#define ixD3F1_PCIE_VC1_RESOURCE_CNTL                                           0x700004c
#define ixD3F1_PCIE_VC1_RESOURCE_STATUS                                         0x700004d
#define ixD3F1_PCIE_DEV_SERIAL_NUM_ENH_CAP_LIST                                 0x7000050
#define ixD3F1_PCIE_DEV_SERIAL_NUM_DW1                                          0x7000051
#define ixD3F1_PCIE_DEV_SERIAL_NUM_DW2                                          0x7000052
#define ixD3F1_PCIE_ADV_ERR_RPT_ENH_CAP_LIST                                    0x7000054
#define ixD3F1_PCIE_UNCORR_ERR_STATUS                                           0x7000055
#define ixD3F1_PCIE_UNCORR_ERR_MASK                                             0x7000056
#define ixD3F1_PCIE_UNCORR_ERR_SEVERITY                                         0x7000057
#define ixD3F1_PCIE_CORR_ERR_STATUS                                             0x7000058
#define ixD3F1_PCIE_CORR_ERR_MASK                                               0x7000059
#define ixD3F1_PCIE_ADV_ERR_CAP_CNTL                                            0x700005a
#define ixD3F1_PCIE_HDR_LOG0                                                    0x700005b
#define ixD3F1_PCIE_HDR_LOG1                                                    0x700005c
#define ixD3F1_PCIE_HDR_LOG2                                                    0x700005d
#define ixD3F1_PCIE_HDR_LOG3                                                    0x700005e
#define ixD3F1_PCIE_ROOT_ERR_CMD                                                0x700005f
#define ixD3F1_PCIE_ROOT_ERR_STATUS                                             0x7000060
#define ixD3F1_PCIE_ERR_SRC_ID                                                  0x7000061
#define ixD3F1_PCIE_TLP_PREFIX_LOG0                                             0x7000062
#define ixD3F1_PCIE_TLP_PREFIX_LOG1                                             0x7000063
#define ixD3F1_PCIE_TLP_PREFIX_LOG2                                             0x7000064
#define ixD3F1_PCIE_TLP_PREFIX_LOG3                                             0x7000065
#define ixD3F1_PCIE_SECONDARY_ENH_CAP_LIST                                      0x700009c
#define ixD3F1_PCIE_LINK_CNTL3                                                  0x700009d
#define ixD3F1_PCIE_LANE_ERROR_STATUS                                           0x700009e
#define ixD3F1_PCIE_LANE_0_EQUALIZATION_CNTL                                    0x700009f
#define ixD3F1_PCIE_LANE_1_EQUALIZATION_CNTL                                    0x700009f
#define ixD3F1_PCIE_LANE_2_EQUALIZATION_CNTL                                    0x70000a0
#define ixD3F1_PCIE_LANE_3_EQUALIZATION_CNTL                                    0x70000a0
#define ixD3F1_PCIE_LANE_4_EQUALIZATION_CNTL                                    0x70000a1
#define ixD3F1_PCIE_LANE_5_EQUALIZATION_CNTL                                    0x70000a1
#define ixD3F1_PCIE_LANE_6_EQUALIZATION_CNTL                                    0x70000a2
#define ixD3F1_PCIE_LANE_7_EQUALIZATION_CNTL                                    0x70000a2
#define ixD3F1_PCIE_LANE_8_EQUALIZATION_CNTL                                    0x70000a3
#define ixD3F1_PCIE_LANE_9_EQUALIZATION_CNTL                                    0x70000a3
#define ixD3F1_PCIE_LANE_10_EQUALIZATION_CNTL                                   0x70000a4
#define ixD3F1_PCIE_LANE_11_EQUALIZATION_CNTL                                   0x70000a4
#define ixD3F1_PCIE_LANE_12_EQUALIZATION_CNTL                                   0x70000a5
#define ixD3F1_PCIE_LANE_13_EQUALIZATION_CNTL                                   0x70000a5
#define ixD3F1_PCIE_LANE_14_EQUALIZATION_CNTL                                   0x70000a6
#define ixD3F1_PCIE_LANE_15_EQUALIZATION_CNTL                                   0x70000a6
#define ixD3F1_PCIE_ACS_ENH_CAP_LIST                                            0x70000a8
#define ixD3F1_PCIE_ACS_CAP                                                     0x70000a9
#define ixD3F1_PCIE_ACS_CNTL                                                    0x70000a9
#define ixD3F1_PCIE_MC_ENH_CAP_LIST                                             0x70000bc
#define ixD3F1_PCIE_MC_CAP                                                      0x70000bd
#define ixD3F1_PCIE_MC_CNTL                                                     0x70000bd
#define ixD3F1_PCIE_MC_ADDR0                                                    0x70000be
#define ixD3F1_PCIE_MC_ADDR1                                                    0x70000bf
#define ixD3F1_PCIE_MC_RCV0                                                     0x70000c0
#define ixD3F1_PCIE_MC_RCV1                                                     0x70000c1
#define ixD3F1_PCIE_MC_BLOCK_ALL0                                               0x70000c2
#define ixD3F1_PCIE_MC_BLOCK_ALL1                                               0x70000c3
#define ixD3F1_PCIE_MC_BLOCK_UNTRANSLATED_0                                     0x70000c4
#define ixD3F1_PCIE_MC_BLOCK_UNTRANSLATED_1                                     0x70000c5
#define ixD3F1_PCIE_MC_OVERLAY_BAR0                                             0x70000c6
#define ixD3F1_PCIE_MC_OVERLAY_BAR1                                             0x70000c7
#define ixD3F2_PCIE_PORT_INDEX                                                  0x8000038
#define ixD3F2_PCIE_PORT_DATA                                                   0x8000039
#define ixD3F2_PCIEP_RESERVED                                                   0x0
#define ixD3F2_PCIEP_SCRATCH                                                    0x1
#define ixD3F2_PCIEP_HW_DEBUG                                                   0x2
#define ixD3F2_PCIEP_PORT_CNTL                                                  0x10
#define ixD3F2_PCIE_TX_CNTL                                                     0x20
#define ixD3F2_PCIE_TX_REQUESTER_ID                                             0x21
#define ixD3F2_PCIE_TX_VENDOR_SPECIFIC                                          0x22
#define ixD3F2_PCIE_TX_REQUEST_NUM_CNTL                                         0x23
#define ixD3F2_PCIE_TX_SEQ                                                      0x24
#define ixD3F2_PCIE_TX_REPLAY                                                   0x25
#define ixD3F2_PCIE_TX_ACK_LATENCY_LIMIT                                        0x26
#define ixD3F2_PCIE_TX_CREDITS_ADVT_P                                           0x30
#define ixD3F2_PCIE_TX_CREDITS_ADVT_NP                                          0x31
#define ixD3F2_PCIE_TX_CREDITS_ADVT_CPL                                         0x32
#define ixD3F2_PCIE_TX_CREDITS_INIT_P                                           0x33
#define ixD3F2_PCIE_TX_CREDITS_INIT_NP                                          0x34
#define ixD3F2_PCIE_TX_CREDITS_INIT_CPL                                         0x35
#define ixD3F2_PCIE_TX_CREDITS_STATUS                                           0x36
#define ixD3F2_PCIE_TX_CREDITS_FCU_THRESHOLD                                    0x37
#define ixD3F2_PCIE_P_PORT_LANE_STATUS                                          0x50
#define ixD3F2_PCIE_FC_P                                                        0x60
#define ixD3F2_PCIE_FC_NP                                                       0x61
#define ixD3F2_PCIE_FC_CPL                                                      0x62
#define ixD3F2_PCIE_ERR_CNTL                                                    0x6a
#define ixD3F2_PCIE_RX_CNTL                                                     0x70
#define ixD3F2_PCIE_RX_EXPECTED_SEQNUM                                          0x71
#define ixD3F2_PCIE_RX_VENDOR_SPECIFIC                                          0x72
#define ixD3F2_PCIE_RX_CNTL3                                                    0x74
#define ixD3F2_PCIE_RX_CREDITS_ALLOCATED_P                                      0x80
#define ixD3F2_PCIE_RX_CREDITS_ALLOCATED_NP                                     0x81
#define ixD3F2_PCIE_RX_CREDITS_ALLOCATED_CPL                                    0x82
#define ixD3F2_PCIEP_ERROR_INJECT_PHYSICAL                                      0x83
#define ixD3F2_PCIEP_ERROR_INJECT_TRANSACTION                                   0x84
#define ixD3F2_PCIE_LC_CNTL                                                     0xa0
#define ixD3F2_PCIE_LC_CNTL2                                                    0xb1
#define ixD3F2_PCIE_LC_CNTL3                                                    0xb5
#define ixD3F2_PCIE_LC_CNTL4                                                    0xb6
#define ixD3F2_PCIE_LC_CNTL5                                                    0xb7
#define ixD3F2_PCIE_LC_CNTL6                                                    0xbb
#define ixD3F2_PCIE_LC_BW_CHANGE_CNTL                                           0xb2
#define ixD3F2_PCIE_LC_TRAINING_CNTL                                            0xa1
#define ixD3F2_PCIE_LC_LINK_WIDTH_CNTL                                          0xa2
#define ixD3F2_PCIE_LC_N_FTS_CNTL                                               0xa3
#define ixD3F2_PCIE_LC_SPEED_CNTL                                               0xa4
#define ixD3F2_PCIE_LC_CDR_CNTL                                                 0xb3
#define ixD3F2_PCIE_LC_LANE_CNTL                                                0xb4
#define ixD3F2_PCIE_LC_FORCE_COEFF                                              0xb8
#define ixD3F2_PCIE_LC_BEST_EQ_SETTINGS                                         0xb9
#define ixD3F2_PCIE_LC_FORCE_EQ_REQ_COEFF                                       0xba
#define ixD3F2_PCIE_LC_STATE0                                                   0xa5
#define ixD3F2_PCIE_LC_STATE1                                                   0xa6
#define ixD3F2_PCIE_LC_STATE2                                                   0xa7
#define ixD3F2_PCIE_LC_STATE3                                                   0xa8
#define ixD3F2_PCIE_LC_STATE4                                                   0xa9
#define ixD3F2_PCIE_LC_STATE5                                                   0xaa
#define ixD3F2_PCIEP_STRAP_LC                                                   0xc0
#define ixD3F2_PCIEP_STRAP_MISC                                                 0xc1
#define ixD3F2_PCIEP_BCH_ECC_CNTL                                               0xd0
#define ixD3F2_PCIEP_HPGI_PRIVATE                                               0xd2
#define ixD3F2_PCIEP_HPGI                                                       0xda
#define ixD3F2_VENDOR_ID                                                        0x8000000
#define ixD3F2_DEVICE_ID                                                        0x8000000
#define ixD3F2_COMMAND                                                          0x8000001
#define ixD3F2_STATUS                                                           0x8000001
#define ixD3F2_REVISION_ID                                                      0x8000002
#define ixD3F2_PROG_INTERFACE                                                   0x8000002
#define ixD3F2_SUB_CLASS                                                        0x8000002
#define ixD3F2_BASE_CLASS                                                       0x8000002
#define ixD3F2_CACHE_LINE                                                       0x8000003
#define ixD3F2_LATENCY                                                          0x8000003
#define ixD3F2_HEADER                                                           0x8000003
#define ixD3F2_BIST                                                             0x8000003
#define ixD3F2_SUB_BUS_NUMBER_LATENCY                                           0x8000006
#define ixD3F2_IO_BASE_LIMIT                                                    0x8000007
#define ixD3F2_SECONDARY_STATUS                                                 0x8000007
#define ixD3F2_MEM_BASE_LIMIT                                                   0x8000008
#define ixD3F2_PREF_BASE_LIMIT                                                  0x8000009
#define ixD3F2_PREF_BASE_UPPER                                                  0x800000a
#define ixD3F2_PREF_LIMIT_UPPER                                                 0x800000b
#define ixD3F2_IO_BASE_LIMIT_HI                                                 0x800000c
#define ixD3F2_IRQ_BRIDGE_CNTL                                                  0x800000f
#define ixD3F2_CAP_PTR                                                          0x800000d
#define ixD3F2_INTERRUPT_LINE                                                   0x800000f
#define ixD3F2_INTERRUPT_PIN                                                    0x800000f
#define ixD3F2_EXT_BRIDGE_CNTL                                                  0x8000010
#define ixD3F2_PMI_CAP_LIST                                                     0x8000014
#define ixD3F2_PMI_CAP                                                          0x8000014
#define ixD3F2_PMI_STATUS_CNTL                                                  0x8000015
#define ixD3F2_PCIE_CAP_LIST                                                    0x8000016
#define ixD3F2_PCIE_CAP                                                         0x8000016
#define ixD3F2_DEVICE_CAP                                                       0x8000017
#define ixD3F2_DEVICE_CNTL                                                      0x8000018
#define ixD3F2_DEVICE_STATUS                                                    0x8000018
#define ixD3F2_LINK_CAP                                                         0x8000019
#define ixD3F2_LINK_CNTL                                                        0x800001a
#define ixD3F2_LINK_STATUS                                                      0x800001a
#define ixD3F2_SLOT_CAP                                                         0x800001b
#define ixD3F2_SLOT_CNTL                                                        0x800001c
#define ixD3F2_SLOT_STATUS                                                      0x800001c
#define ixD3F2_ROOT_CNTL                                                        0x800001d
#define ixD3F2_ROOT_CAP                                                         0x800001d
#define ixD3F2_ROOT_STATUS                                                      0x800001e
#define ixD3F2_DEVICE_CAP2                                                      0x800001f
#define ixD3F2_DEVICE_CNTL2                                                     0x8000020
#define ixD3F2_DEVICE_STATUS2                                                   0x8000020
#define ixD3F2_LINK_CAP2                                                        0x8000021
#define ixD3F2_LINK_CNTL2                                                       0x8000022
#define ixD3F2_LINK_STATUS2                                                     0x8000022
#define ixD3F2_SLOT_CAP2                                                        0x8000023
#define ixD3F2_SLOT_CNTL2                                                       0x8000024
#define ixD3F2_SLOT_STATUS2                                                     0x8000024
#define ixD3F2_MSI_CAP_LIST                                                     0x8000028
#define ixD3F2_MSI_MSG_CNTL                                                     0x8000028
#define ixD3F2_MSI_MSG_ADDR_LO                                                  0x8000029
#define ixD3F2_MSI_MSG_ADDR_HI                                                  0x800002a
#define ixD3F2_MSI_MSG_DATA_64                                                  0x800002b
#define ixD3F2_MSI_MSG_DATA                                                     0x800002a
#define ixD3F2_SSID_CAP_LIST                                                    0x8000030
#define ixD3F2_SSID_CAP                                                         0x8000031
#define ixD3F2_MSI_MAP_CAP_LIST                                                 0x8000032
#define ixD3F2_MSI_MAP_CAP                                                      0x8000032
#define ixD3F2_MSI_MAP_ADDR_LO                                                  0x8000033
#define ixD3F2_MSI_MAP_ADDR_HI                                                  0x8000034
#define ixD3F2_PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST                                0x8000040
#define ixD3F2_PCIE_VENDOR_SPECIFIC_HDR                                         0x8000041
#define ixD3F2_PCIE_VENDOR_SPECIFIC1                                            0x8000042
#define ixD3F2_PCIE_VENDOR_SPECIFIC2                                            0x8000043
#define ixD3F2_PCIE_VC_ENH_CAP_LIST                                             0x8000044
#define ixD3F2_PCIE_PORT_VC_CAP_REG1                                            0x8000045
#define ixD3F2_PCIE_PORT_VC_CAP_REG2                                            0x8000046
#define ixD3F2_PCIE_PORT_VC_CNTL                                                0x8000047
#define ixD3F2_PCIE_PORT_VC_STATUS                                              0x8000047
#define ixD3F2_PCIE_VC0_RESOURCE_CAP                                            0x8000048
#define ixD3F2_PCIE_VC0_RESOURCE_CNTL                                           0x8000049
#define ixD3F2_PCIE_VC0_RESOURCE_STATUS                                         0x800004a
#define ixD3F2_PCIE_VC1_RESOURCE_CAP                                            0x800004b
#define ixD3F2_PCIE_VC1_RESOURCE_CNTL                                           0x800004c
#define ixD3F2_PCIE_VC1_RESOURCE_STATUS                                         0x800004d
#define ixD3F2_PCIE_DEV_SERIAL_NUM_ENH_CAP_LIST                                 0x8000050
#define ixD3F2_PCIE_DEV_SERIAL_NUM_DW1                                          0x8000051
#define ixD3F2_PCIE_DEV_SERIAL_NUM_DW2                                          0x8000052
#define ixD3F2_PCIE_ADV_ERR_RPT_ENH_CAP_LIST                                    0x8000054
#define ixD3F2_PCIE_UNCORR_ERR_STATUS                                           0x8000055
#define ixD3F2_PCIE_UNCORR_ERR_MASK                                             0x8000056
#define ixD3F2_PCIE_UNCORR_ERR_SEVERITY                                         0x8000057
#define ixD3F2_PCIE_CORR_ERR_STATUS                                             0x8000058
#define ixD3F2_PCIE_CORR_ERR_MASK                                               0x8000059
#define ixD3F2_PCIE_ADV_ERR_CAP_CNTL                                            0x800005a
#define ixD3F2_PCIE_HDR_LOG0                                                    0x800005b
#define ixD3F2_PCIE_HDR_LOG1                                                    0x800005c
#define ixD3F2_PCIE_HDR_LOG2                                                    0x800005d
#define ixD3F2_PCIE_HDR_LOG3                                                    0x800005e
#define ixD3F2_PCIE_ROOT_ERR_CMD                                                0x800005f
#define ixD3F2_PCIE_ROOT_ERR_STATUS                                             0x8000060
#define ixD3F2_PCIE_ERR_SRC_ID                                                  0x8000061
#define ixD3F2_PCIE_TLP_PREFIX_LOG0                                             0x8000062
#define ixD3F2_PCIE_TLP_PREFIX_LOG1                                             0x8000063
#define ixD3F2_PCIE_TLP_PREFIX_LOG2                                             0x8000064
#define ixD3F2_PCIE_TLP_PREFIX_LOG3                                             0x8000065
#define ixD3F2_PCIE_SECONDARY_ENH_CAP_LIST                                      0x800009c
#define ixD3F2_PCIE_LINK_CNTL3                                                  0x800009d
#define ixD3F2_PCIE_LANE_ERROR_STATUS                                           0x800009e
#define ixD3F2_PCIE_LANE_0_EQUALIZATION_CNTL                                    0x800009f
#define ixD3F2_PCIE_LANE_1_EQUALIZATION_CNTL                                    0x800009f
#define ixD3F2_PCIE_LANE_2_EQUALIZATION_CNTL                                    0x80000a0
#define ixD3F2_PCIE_LANE_3_EQUALIZATION_CNTL                                    0x80000a0
#define ixD3F2_PCIE_LANE_4_EQUALIZATION_CNTL                                    0x80000a1
#define ixD3F2_PCIE_LANE_5_EQUALIZATION_CNTL                                    0x80000a1
#define ixD3F2_PCIE_LANE_6_EQUALIZATION_CNTL                                    0x80000a2
#define ixD3F2_PCIE_LANE_7_EQUALIZATION_CNTL                                    0x80000a2
#define ixD3F2_PCIE_LANE_8_EQUALIZATION_CNTL                                    0x80000a3
#define ixD3F2_PCIE_LANE_9_EQUALIZATION_CNTL                                    0x80000a3
#define ixD3F2_PCIE_LANE_10_EQUALIZATION_CNTL                                   0x80000a4
#define ixD3F2_PCIE_LANE_11_EQUALIZATION_CNTL                                   0x80000a4
#define ixD3F2_PCIE_LANE_12_EQUALIZATION_CNTL                                   0x80000a5
#define ixD3F2_PCIE_LANE_13_EQUALIZATION_CNTL                                   0x80000a5
#define ixD3F2_PCIE_LANE_14_EQUALIZATION_CNTL                                   0x80000a6
#define ixD3F2_PCIE_LANE_15_EQUALIZATION_CNTL                                   0x80000a6
#define ixD3F2_PCIE_ACS_ENH_CAP_LIST                                            0x80000a8
#define ixD3F2_PCIE_ACS_CAP                                                     0x80000a9
#define ixD3F2_PCIE_ACS_CNTL                                                    0x80000a9
#define ixD3F2_PCIE_MC_ENH_CAP_LIST                                             0x80000bc
#define ixD3F2_PCIE_MC_CAP                                                      0x80000bd
#define ixD3F2_PCIE_MC_CNTL                                                     0x80000bd
#define ixD3F2_PCIE_MC_ADDR0                                                    0x80000be
#define ixD3F2_PCIE_MC_ADDR1                                                    0x80000bf
#define ixD3F2_PCIE_MC_RCV0                                                     0x80000c0
#define ixD3F2_PCIE_MC_RCV1                                                     0x80000c1
#define ixD3F2_PCIE_MC_BLOCK_ALL0                                               0x80000c2
#define ixD3F2_PCIE_MC_BLOCK_ALL1                                               0x80000c3
#define ixD3F2_PCIE_MC_BLOCK_UNTRANSLATED_0                                     0x80000c4
#define ixD3F2_PCIE_MC_BLOCK_UNTRANSLATED_1                                     0x80000c5
#define ixD3F2_PCIE_MC_OVERLAY_BAR0                                             0x80000c6
#define ixD3F2_PCIE_MC_OVERLAY_BAR1                                             0x80000c7
#define ixD3F3_PCIE_PORT_INDEX                                                  0x9000038
#define ixD3F3_PCIE_PORT_DATA                                                   0x9000039
#define ixD3F3_PCIEP_RESERVED                                                   0x0
#define ixD3F3_PCIEP_SCRATCH                                                    0x1
#define ixD3F3_PCIEP_HW_DEBUG                                                   0x2
#define ixD3F3_PCIEP_PORT_CNTL                                                  0x10
#define ixD3F3_PCIE_TX_CNTL                                                     0x20
#define ixD3F3_PCIE_TX_REQUESTER_ID                                             0x21
#define ixD3F3_PCIE_TX_VENDOR_SPECIFIC                                          0x22
#define ixD3F3_PCIE_TX_REQUEST_NUM_CNTL                                         0x23
#define ixD3F3_PCIE_TX_SEQ                                                      0x24
#define ixD3F3_PCIE_TX_REPLAY                                                   0x25
#define ixD3F3_PCIE_TX_ACK_LATENCY_LIMIT                                        0x26
#define ixD3F3_PCIE_TX_CREDITS_ADVT_P                                           0x30
#define ixD3F3_PCIE_TX_CREDITS_ADVT_NP                                          0x31
#define ixD3F3_PCIE_TX_CREDITS_ADVT_CPL                                         0x32
#define ixD3F3_PCIE_TX_CREDITS_INIT_P                                           0x33
#define ixD3F3_PCIE_TX_CREDITS_INIT_NP                                          0x34
#define ixD3F3_PCIE_TX_CREDITS_INIT_CPL                                         0x35
#define ixD3F3_PCIE_TX_CREDITS_STATUS                                           0x36
#define ixD3F3_PCIE_TX_CREDITS_FCU_THRESHOLD                                    0x37
#define ixD3F3_PCIE_P_PORT_LANE_STATUS                                          0x50
#define ixD3F3_PCIE_FC_P                                                        0x60
#define ixD3F3_PCIE_FC_NP                                                       0x61
#define ixD3F3_PCIE_FC_CPL                                                      0x62
#define ixD3F3_PCIE_ERR_CNTL                                                    0x6a
#define ixD3F3_PCIE_RX_CNTL                                                     0x70
#define ixD3F3_PCIE_RX_EXPECTED_SEQNUM                                          0x71
#define ixD3F3_PCIE_RX_VENDOR_SPECIFIC                                          0x72
#define ixD3F3_PCIE_RX_CNTL3                                                    0x74
#define ixD3F3_PCIE_RX_CREDITS_ALLOCATED_P                                      0x80
#define ixD3F3_PCIE_RX_CREDITS_ALLOCATED_NP                                     0x81
#define ixD3F3_PCIE_RX_CREDITS_ALLOCATED_CPL                                    0x82
#define ixD3F3_PCIEP_ERROR_INJECT_PHYSICAL                                      0x83
#define ixD3F3_PCIEP_ERROR_INJECT_TRANSACTION                                   0x84
#define ixD3F3_PCIE_LC_CNTL                                                     0xa0
#define ixD3F3_PCIE_LC_CNTL2                                                    0xb1
#define ixD3F3_PCIE_LC_CNTL3                                                    0xb5
#define ixD3F3_PCIE_LC_CNTL4                                                    0xb6
#define ixD3F3_PCIE_LC_CNTL5                                                    0xb7
#define ixD3F3_PCIE_LC_CNTL6                                                    0xbb
#define ixD3F3_PCIE_LC_BW_CHANGE_CNTL                                           0xb2
#define ixD3F3_PCIE_LC_TRAINING_CNTL                                            0xa1
#define ixD3F3_PCIE_LC_LINK_WIDTH_CNTL                                          0xa2
#define ixD3F3_PCIE_LC_N_FTS_CNTL                                               0xa3
#define ixD3F3_PCIE_LC_SPEED_CNTL                                               0xa4
#define ixD3F3_PCIE_LC_CDR_CNTL                                                 0xb3
#define ixD3F3_PCIE_LC_LANE_CNTL                                                0xb4
#define ixD3F3_PCIE_LC_FORCE_COEFF                                              0xb8
#define ixD3F3_PCIE_LC_BEST_EQ_SETTINGS                                         0xb9
#define ixD3F3_PCIE_LC_FORCE_EQ_REQ_COEFF                                       0xba
#define ixD3F3_PCIE_LC_STATE0                                                   0xa5
#define ixD3F3_PCIE_LC_STATE1                                                   0xa6
#define ixD3F3_PCIE_LC_STATE2                                                   0xa7
#define ixD3F3_PCIE_LC_STATE3                                                   0xa8
#define ixD3F3_PCIE_LC_STATE4                                                   0xa9
#define ixD3F3_PCIE_LC_STATE5                                                   0xaa
#define ixD3F3_PCIEP_STRAP_LC                                                   0xc0
#define ixD3F3_PCIEP_STRAP_MISC                                                 0xc1
#define ixD3F3_PCIEP_BCH_ECC_CNTL                                               0xd0
#define ixD3F3_PCIEP_HPGI_PRIVATE                                               0xd2
#define ixD3F3_PCIEP_HPGI                                                       0xda
#define ixD3F3_VENDOR_ID                                                        0x9000000
#define ixD3F3_DEVICE_ID                                                        0x9000000
#define ixD3F3_COMMAND                                                          0x9000001
#define ixD3F3_STATUS                                                           0x9000001
#define ixD3F3_REVISION_ID                                                      0x9000002
#define ixD3F3_PROG_INTERFACE                                                   0x9000002
#define ixD3F3_SUB_CLASS                                                        0x9000002
#define ixD3F3_BASE_CLASS                                                       0x9000002
#define ixD3F3_CACHE_LINE                                                       0x9000003
#define ixD3F3_LATENCY                                                          0x9000003
#define ixD3F3_HEADER                                                           0x9000003
#define ixD3F3_BIST                                                             0x9000003
#define ixD3F3_SUB_BUS_NUMBER_LATENCY                                           0x9000006
#define ixD3F3_IO_BASE_LIMIT                                                    0x9000007
#define ixD3F3_SECONDARY_STATUS                                                 0x9000007
#define ixD3F3_MEM_BASE_LIMIT                                                   0x9000008
#define ixD3F3_PREF_BASE_LIMIT                                                  0x9000009
#define ixD3F3_PREF_BASE_UPPER                                                  0x900000a
#define ixD3F3_PREF_LIMIT_UPPER                                                 0x900000b
#define ixD3F3_IO_BASE_LIMIT_HI                                                 0x900000c
#define ixD3F3_IRQ_BRIDGE_CNTL                                                  0x900000f
#define ixD3F3_CAP_PTR                                                          0x900000d
#define ixD3F3_INTERRUPT_LINE                                                   0x900000f
#define ixD3F3_INTERRUPT_PIN                                                    0x900000f
#define ixD3F3_EXT_BRIDGE_CNTL                                                  0x9000010
#define ixD3F3_PMI_CAP_LIST                                                     0x9000014
#define ixD3F3_PMI_CAP                                                          0x9000014
#define ixD3F3_PMI_STATUS_CNTL                                                  0x9000015
#define ixD3F3_PCIE_CAP_LIST                                                    0x9000016
#define ixD3F3_PCIE_CAP                                                         0x9000016
#define ixD3F3_DEVICE_CAP                                                       0x9000017
#define ixD3F3_DEVICE_CNTL                                                      0x9000018
#define ixD3F3_DEVICE_STATUS                                                    0x9000018
#define ixD3F3_LINK_CAP                                                         0x9000019
#define ixD3F3_LINK_CNTL                                                        0x900001a
#define ixD3F3_LINK_STATUS                                                      0x900001a
#define ixD3F3_SLOT_CAP                                                         0x900001b
#define ixD3F3_SLOT_CNTL                                                        0x900001c
#define ixD3F3_SLOT_STATUS                                                      0x900001c
#define ixD3F3_ROOT_CNTL                                                        0x900001d
#define ixD3F3_ROOT_CAP                                                         0x900001d
#define ixD3F3_ROOT_STATUS                                                      0x900001e
#define ixD3F3_DEVICE_CAP2                                                      0x900001f
#define ixD3F3_DEVICE_CNTL2                                                     0x9000020
#define ixD3F3_DEVICE_STATUS2                                                   0x9000020
#define ixD3F3_LINK_CAP2                                                        0x9000021
#define ixD3F3_LINK_CNTL2                                                       0x9000022
#define ixD3F3_LINK_STATUS2                                                     0x9000022
#define ixD3F3_SLOT_CAP2                                                        0x9000023
#define ixD3F3_SLOT_CNTL2                                                       0x9000024
#define ixD3F3_SLOT_STATUS2                                                     0x9000024
#define ixD3F3_MSI_CAP_LIST                                                     0x9000028
#define ixD3F3_MSI_MSG_CNTL                                                     0x9000028
#define ixD3F3_MSI_MSG_ADDR_LO                                                  0x9000029
#define ixD3F3_MSI_MSG_ADDR_HI                                                  0x900002a
#define ixD3F3_MSI_MSG_DATA_64                                                  0x900002b
#define ixD3F3_MSI_MSG_DATA                                                     0x900002a
#define ixD3F3_SSID_CAP_LIST                                                    0x9000030
#define ixD3F3_SSID_CAP                                                         0x9000031
#define ixD3F3_MSI_MAP_CAP_LIST                                                 0x9000032
#define ixD3F3_MSI_MAP_CAP                                                      0x9000032
#define ixD3F3_MSI_MAP_ADDR_LO                                                  0x9000033
#define ixD3F3_MSI_MAP_ADDR_HI                                                  0x9000034
#define ixD3F3_PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST                                0x9000040
#define ixD3F3_PCIE_VENDOR_SPECIFIC_HDR                                         0x9000041
#define ixD3F3_PCIE_VENDOR_SPECIFIC1                                            0x9000042
#define ixD3F3_PCIE_VENDOR_SPECIFIC2                                            0x9000043
#define ixD3F3_PCIE_VC_ENH_CAP_LIST                                             0x9000044
#define ixD3F3_PCIE_PORT_VC_CAP_REG1                                            0x9000045
#define ixD3F3_PCIE_PORT_VC_CAP_REG2                                            0x9000046
#define ixD3F3_PCIE_PORT_VC_CNTL                                                0x9000047
#define ixD3F3_PCIE_PORT_VC_STATUS                                              0x9000047
#define ixD3F3_PCIE_VC0_RESOURCE_CAP                                            0x9000048
#define ixD3F3_PCIE_VC0_RESOURCE_CNTL                                           0x9000049
#define ixD3F3_PCIE_VC0_RESOURCE_STATUS                                         0x900004a
#define ixD3F3_PCIE_VC1_RESOURCE_CAP                                            0x900004b
#define ixD3F3_PCIE_VC1_RESOURCE_CNTL                                           0x900004c
#define ixD3F3_PCIE_VC1_RESOURCE_STATUS                                         0x900004d
#define ixD3F3_PCIE_DEV_SERIAL_NUM_ENH_CAP_LIST                                 0x9000050
#define ixD3F3_PCIE_DEV_SERIAL_NUM_DW1                                          0x9000051
#define ixD3F3_PCIE_DEV_SERIAL_NUM_DW2                                          0x9000052
#define ixD3F3_PCIE_ADV_ERR_RPT_ENH_CAP_LIST                                    0x9000054
#define ixD3F3_PCIE_UNCORR_ERR_STATUS                                           0x9000055
#define ixD3F3_PCIE_UNCORR_ERR_MASK                                             0x9000056
#define ixD3F3_PCIE_UNCORR_ERR_SEVERITY                                         0x9000057
#define ixD3F3_PCIE_CORR_ERR_STATUS                                             0x9000058
#define ixD3F3_PCIE_CORR_ERR_MASK                                               0x9000059
#define ixD3F3_PCIE_ADV_ERR_CAP_CNTL                                            0x900005a
#define ixD3F3_PCIE_HDR_LOG0                                                    0x900005b
#define ixD3F3_PCIE_HDR_LOG1                                                    0x900005c
#define ixD3F3_PCIE_HDR_LOG2                                                    0x900005d
#define ixD3F3_PCIE_HDR_LOG3                                                    0x900005e
#define ixD3F3_PCIE_ROOT_ERR_CMD                                                0x900005f
#define ixD3F3_PCIE_ROOT_ERR_STATUS                                             0x9000060
#define ixD3F3_PCIE_ERR_SRC_ID                                                  0x9000061
#define ixD3F3_PCIE_TLP_PREFIX_LOG0                                             0x9000062
#define ixD3F3_PCIE_TLP_PREFIX_LOG1                                             0x9000063
#define ixD3F3_PCIE_TLP_PREFIX_LOG2                                             0x9000064
#define ixD3F3_PCIE_TLP_PREFIX_LOG3                                             0x9000065
#define ixD3F3_PCIE_SECONDARY_ENH_CAP_LIST                                      0x900009c
#define ixD3F3_PCIE_LINK_CNTL3                                                  0x900009d
#define ixD3F3_PCIE_LANE_ERROR_STATUS                                           0x900009e
#define ixD3F3_PCIE_LANE_0_EQUALIZATION_CNTL                                    0x900009f
#define ixD3F3_PCIE_LANE_1_EQUALIZATION_CNTL                                    0x900009f
#define ixD3F3_PCIE_LANE_2_EQUALIZATION_CNTL                                    0x90000a0
#define ixD3F3_PCIE_LANE_3_EQUALIZATION_CNTL                                    0x90000a0
#define ixD3F3_PCIE_LANE_4_EQUALIZATION_CNTL                                    0x90000a1
#define ixD3F3_PCIE_LANE_5_EQUALIZATION_CNTL                                    0x90000a1
#define ixD3F3_PCIE_LANE_6_EQUALIZATION_CNTL                                    0x90000a2
#define ixD3F3_PCIE_LANE_7_EQUALIZATION_CNTL                                    0x90000a2
#define ixD3F3_PCIE_LANE_8_EQUALIZATION_CNTL                                    0x90000a3
#define ixD3F3_PCIE_LANE_9_EQUALIZATION_CNTL                                    0x90000a3
#define ixD3F3_PCIE_LANE_10_EQUALIZATION_CNTL                                   0x90000a4
#define ixD3F3_PCIE_LANE_11_EQUALIZATION_CNTL                                   0x90000a4
#define ixD3F3_PCIE_LANE_12_EQUALIZATION_CNTL                                   0x90000a5
#define ixD3F3_PCIE_LANE_13_EQUALIZATION_CNTL                                   0x90000a5
#define ixD3F3_PCIE_LANE_14_EQUALIZATION_CNTL                                   0x90000a6
#define ixD3F3_PCIE_LANE_15_EQUALIZATION_CNTL                                   0x90000a6
#define ixD3F3_PCIE_ACS_ENH_CAP_LIST                                            0x90000a8
#define ixD3F3_PCIE_ACS_CAP                                                     0x90000a9
#define ixD3F3_PCIE_ACS_CNTL                                                    0x90000a9
#define ixD3F3_PCIE_MC_ENH_CAP_LIST                                             0x90000bc
#define ixD3F3_PCIE_MC_CAP                                                      0x90000bd
#define ixD3F3_PCIE_MC_CNTL                                                     0x90000bd
#define ixD3F3_PCIE_MC_ADDR0                                                    0x90000be
#define ixD3F3_PCIE_MC_ADDR1                                                    0x90000bf
#define ixD3F3_PCIE_MC_RCV0                                                     0x90000c0
#define ixD3F3_PCIE_MC_RCV1                                                     0x90000c1
#define ixD3F3_PCIE_MC_BLOCK_ALL0                                               0x90000c2
#define ixD3F3_PCIE_MC_BLOCK_ALL1                                               0x90000c3
#define ixD3F3_PCIE_MC_BLOCK_UNTRANSLATED_0                                     0x90000c4
#define ixD3F3_PCIE_MC_BLOCK_UNTRANSLATED_1                                     0x90000c5
#define ixD3F3_PCIE_MC_OVERLAY_BAR0                                             0x90000c6
#define ixD3F3_PCIE_MC_OVERLAY_BAR1                                             0x90000c7
#define ixD3F4_PCIE_PORT_INDEX                                                  0xa000038
#define ixD3F4_PCIE_PORT_DATA                                                   0xa000039
#define ixD3F4_PCIEP_RESERVED                                                   0x0
#define ixD3F4_PCIEP_SCRATCH                                                    0x1
#define ixD3F4_PCIEP_HW_DEBUG                                                   0x2
#define ixD3F4_PCIEP_PORT_CNTL                                                  0x10
#define ixD3F4_PCIE_TX_CNTL                                                     0x20
#define ixD3F4_PCIE_TX_REQUESTER_ID                                             0x21
#define ixD3F4_PCIE_TX_VENDOR_SPECIFIC                                          0x22
#define ixD3F4_PCIE_TX_REQUEST_NUM_CNTL                                         0x23
#define ixD3F4_PCIE_TX_SEQ                                                      0x24
#define ixD3F4_PCIE_TX_REPLAY                                                   0x25
#define ixD3F4_PCIE_TX_ACK_LATENCY_LIMIT                                        0x26
#define ixD3F4_PCIE_TX_CREDITS_ADVT_P                                           0x30
#define ixD3F4_PCIE_TX_CREDITS_ADVT_NP                                          0x31
#define ixD3F4_PCIE_TX_CREDITS_ADVT_CPL                                         0x32
#define ixD3F4_PCIE_TX_CREDITS_INIT_P                                           0x33
#define ixD3F4_PCIE_TX_CREDITS_INIT_NP                                          0x34
#define ixD3F4_PCIE_TX_CREDITS_INIT_CPL                                         0x35
#define ixD3F4_PCIE_TX_CREDITS_STATUS                                           0x36
#define ixD3F4_PCIE_TX_CREDITS_FCU_THRESHOLD                                    0x37
#define ixD3F4_PCIE_P_PORT_LANE_STATUS                                          0x50
#define ixD3F4_PCIE_FC_P                                                        0x60
#define ixD3F4_PCIE_FC_NP                                                       0x61
#define ixD3F4_PCIE_FC_CPL                                                      0x62
#define ixD3F4_PCIE_ERR_CNTL                                                    0x6a
#define ixD3F4_PCIE_RX_CNTL                                                     0x70
#define ixD3F4_PCIE_RX_EXPECTED_SEQNUM                                          0x71
#define ixD3F4_PCIE_RX_VENDOR_SPECIFIC                                          0x72
#define ixD3F4_PCIE_RX_CNTL3                                                    0x74
#define ixD3F4_PCIE_RX_CREDITS_ALLOCATED_P                                      0x80
#define ixD3F4_PCIE_RX_CREDITS_ALLOCATED_NP                                     0x81
#define ixD3F4_PCIE_RX_CREDITS_ALLOCATED_CPL                                    0x82
#define ixD3F4_PCIEP_ERROR_INJECT_PHYSICAL                                      0x83
#define ixD3F4_PCIEP_ERROR_INJECT_TRANSACTION                                   0x84
#define ixD3F4_PCIE_LC_CNTL                                                     0xa0
#define ixD3F4_PCIE_LC_CNTL2                                                    0xb1
#define ixD3F4_PCIE_LC_CNTL3                                                    0xb5
#define ixD3F4_PCIE_LC_CNTL4                                                    0xb6
#define ixD3F4_PCIE_LC_CNTL5                                                    0xb7
#define ixD3F4_PCIE_LC_CNTL6                                                    0xbb
#define ixD3F4_PCIE_LC_BW_CHANGE_CNTL                                           0xb2
#define ixD3F4_PCIE_LC_TRAINING_CNTL                                            0xa1
#define ixD3F4_PCIE_LC_LINK_WIDTH_CNTL                                          0xa2
#define ixD3F4_PCIE_LC_N_FTS_CNTL                                               0xa3
#define ixD3F4_PCIE_LC_SPEED_CNTL                                               0xa4
#define ixD3F4_PCIE_LC_CDR_CNTL                                                 0xb3
#define ixD3F4_PCIE_LC_LANE_CNTL                                                0xb4
#define ixD3F4_PCIE_LC_FORCE_COEFF                                              0xb8
#define ixD3F4_PCIE_LC_BEST_EQ_SETTINGS                                         0xb9
#define ixD3F4_PCIE_LC_FORCE_EQ_REQ_COEFF                                       0xba
#define ixD3F4_PCIE_LC_STATE0                                                   0xa5
#define ixD3F4_PCIE_LC_STATE1                                                   0xa6
#define ixD3F4_PCIE_LC_STATE2                                                   0xa7
#define ixD3F4_PCIE_LC_STATE3                                                   0xa8
#define ixD3F4_PCIE_LC_STATE4                                                   0xa9
#define ixD3F4_PCIE_LC_STATE5                                                   0xaa
#define ixD3F4_PCIEP_STRAP_LC                                                   0xc0
#define ixD3F4_PCIEP_STRAP_MISC                                                 0xc1
#define ixD3F4_PCIEP_BCH_ECC_CNTL                                               0xd0
#define ixD3F4_PCIEP_HPGI_PRIVATE                                               0xd2
#define ixD3F4_PCIEP_HPGI                                                       0xda
#define ixD3F4_VENDOR_ID                                                        0xa000000
#define ixD3F4_DEVICE_ID                                                        0xa000000
#define ixD3F4_COMMAND                                                          0xa000001
#define ixD3F4_STATUS                                                           0xa000001
#define ixD3F4_REVISION_ID                                                      0xa000002
#define ixD3F4_PROG_INTERFACE                                                   0xa000002
#define ixD3F4_SUB_CLASS                                                        0xa000002
#define ixD3F4_BASE_CLASS                                                       0xa000002
#define ixD3F4_CACHE_LINE                                                       0xa000003
#define ixD3F4_LATENCY                                                          0xa000003
#define ixD3F4_HEADER                                                           0xa000003
#define ixD3F4_BIST                                                             0xa000003
#define ixD3F4_SUB_BUS_NUMBER_LATENCY                                           0xa000006
#define ixD3F4_IO_BASE_LIMIT                                                    0xa000007
#define ixD3F4_SECONDARY_STATUS                                                 0xa000007
#define ixD3F4_MEM_BASE_LIMIT                                                   0xa000008
#define ixD3F4_PREF_BASE_LIMIT                                                  0xa000009
#define ixD3F4_PREF_BASE_UPPER                                                  0xa00000a
#define ixD3F4_PREF_LIMIT_UPPER                                                 0xa00000b
#define ixD3F4_IO_BASE_LIMIT_HI                                                 0xa00000c
#define ixD3F4_IRQ_BRIDGE_CNTL                                                  0xa00000f
#define ixD3F4_CAP_PTR                                                          0xa00000d
#define ixD3F4_INTERRUPT_LINE                                                   0xa00000f
#define ixD3F4_INTERRUPT_PIN                                                    0xa00000f
#define ixD3F4_EXT_BRIDGE_CNTL                                                  0xa000010
#define ixD3F4_PMI_CAP_LIST                                                     0xa000014
#define ixD3F4_PMI_CAP                                                          0xa000014
#define ixD3F4_PMI_STATUS_CNTL                                                  0xa000015
#define ixD3F4_PCIE_CAP_LIST                                                    0xa000016
#define ixD3F4_PCIE_CAP                                                         0xa000016
#define ixD3F4_DEVICE_CAP                                                       0xa000017
#define ixD3F4_DEVICE_CNTL                                                      0xa000018
#define ixD3F4_DEVICE_STATUS                                                    0xa000018
#define ixD3F4_LINK_CAP                                                         0xa000019
#define ixD3F4_LINK_CNTL                                                        0xa00001a
#define ixD3F4_LINK_STATUS                                                      0xa00001a
#define ixD3F4_SLOT_CAP                                                         0xa00001b
#define ixD3F4_SLOT_CNTL                                                        0xa00001c
#define ixD3F4_SLOT_STATUS                                                      0xa00001c
#define ixD3F4_ROOT_CNTL                                                        0xa00001d
#define ixD3F4_ROOT_CAP                                                         0xa00001d
#define ixD3F4_ROOT_STATUS                                                      0xa00001e
#define ixD3F4_DEVICE_CAP2                                                      0xa00001f
#define ixD3F4_DEVICE_CNTL2                                                     0xa000020
#define ixD3F4_DEVICE_STATUS2                                                   0xa000020
#define ixD3F4_LINK_CAP2                                                        0xa000021
#define ixD3F4_LINK_CNTL2                                                       0xa000022
#define ixD3F4_LINK_STATUS2                                                     0xa000022
#define ixD3F4_SLOT_CAP2                                                        0xa000023
#define ixD3F4_SLOT_CNTL2                                                       0xa000024
#define ixD3F4_SLOT_STATUS2                                                     0xa000024
#define ixD3F4_MSI_CAP_LIST                                                     0xa000028
#define ixD3F4_MSI_MSG_CNTL                                                     0xa000028
#define ixD3F4_MSI_MSG_ADDR_LO                                                  0xa000029
#define ixD3F4_MSI_MSG_ADDR_HI                                                  0xa00002a
#define ixD3F4_MSI_MSG_DATA_64                                                  0xa00002b
#define ixD3F4_MSI_MSG_DATA                                                     0xa00002a
#define ixD3F4_SSID_CAP_LIST                                                    0xa000030
#define ixD3F4_SSID_CAP                                                         0xa000031
#define ixD3F4_MSI_MAP_CAP_LIST                                                 0xa000032
#define ixD3F4_MSI_MAP_CAP                                                      0xa000032
#define ixD3F4_MSI_MAP_ADDR_LO                                                  0xa000033
#define ixD3F4_MSI_MAP_ADDR_HI                                                  0xa000034
#define ixD3F4_PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST                                0xa000040
#define ixD3F4_PCIE_VENDOR_SPECIFIC_HDR                                         0xa000041
#define ixD3F4_PCIE_VENDOR_SPECIFIC1                                            0xa000042
#define ixD3F4_PCIE_VENDOR_SPECIFIC2                                            0xa000043
#define ixD3F4_PCIE_VC_ENH_CAP_LIST                                             0xa000044
#define ixD3F4_PCIE_PORT_VC_CAP_REG1                                            0xa000045
#define ixD3F4_PCIE_PORT_VC_CAP_REG2                                            0xa000046
#define ixD3F4_PCIE_PORT_VC_CNTL                                                0xa000047
#define ixD3F4_PCIE_PORT_VC_STATUS                                              0xa000047
#define ixD3F4_PCIE_VC0_RESOURCE_CAP                                            0xa000048
#define ixD3F4_PCIE_VC0_RESOURCE_CNTL                                           0xa000049
#define ixD3F4_PCIE_VC0_RESOURCE_STATUS                                         0xa00004a
#define ixD3F4_PCIE_VC1_RESOURCE_CAP                                            0xa00004b
#define ixD3F4_PCIE_VC1_RESOURCE_CNTL                                           0xa00004c
#define ixD3F4_PCIE_VC1_RESOURCE_STATUS                                         0xa00004d
#define ixD3F4_PCIE_DEV_SERIAL_NUM_ENH_CAP_LIST                                 0xa000050
#define ixD3F4_PCIE_DEV_SERIAL_NUM_DW1                                          0xa000051
#define ixD3F4_PCIE_DEV_SERIAL_NUM_DW2                                          0xa000052
#define ixD3F4_PCIE_ADV_ERR_RPT_ENH_CAP_LIST                                    0xa000054
#define ixD3F4_PCIE_UNCORR_ERR_STATUS                                           0xa000055
#define ixD3F4_PCIE_UNCORR_ERR_MASK                                             0xa000056
#define ixD3F4_PCIE_UNCORR_ERR_SEVERITY                                         0xa000057
#define ixD3F4_PCIE_CORR_ERR_STATUS                                             0xa000058
#define ixD3F4_PCIE_CORR_ERR_MASK                                               0xa000059
#define ixD3F4_PCIE_ADV_ERR_CAP_CNTL                                            0xa00005a
#define ixD3F4_PCIE_HDR_LOG0                                                    0xa00005b
#define ixD3F4_PCIE_HDR_LOG1                                                    0xa00005c
#define ixD3F4_PCIE_HDR_LOG2                                                    0xa00005d
#define ixD3F4_PCIE_HDR_LOG3                                                    0xa00005e
#define ixD3F4_PCIE_ROOT_ERR_CMD                                                0xa00005f
#define ixD3F4_PCIE_ROOT_ERR_STATUS                                             0xa000060
#define ixD3F4_PCIE_ERR_SRC_ID                                                  0xa000061
#define ixD3F4_PCIE_TLP_PREFIX_LOG0                                             0xa000062
#define ixD3F4_PCIE_TLP_PREFIX_LOG1                                             0xa000063
#define ixD3F4_PCIE_TLP_PREFIX_LOG2                                             0xa000064
#define ixD3F4_PCIE_TLP_PREFIX_LOG3                                             0xa000065
#define ixD3F4_PCIE_SECONDARY_ENH_CAP_LIST                                      0xa00009c
#define ixD3F4_PCIE_LINK_CNTL3                                                  0xa00009d
#define ixD3F4_PCIE_LANE_ERROR_STATUS                                           0xa00009e
#define ixD3F4_PCIE_LANE_0_EQUALIZATION_CNTL                                    0xa00009f
#define ixD3F4_PCIE_LANE_1_EQUALIZATION_CNTL                                    0xa00009f
#define ixD3F4_PCIE_LANE_2_EQUALIZATION_CNTL                                    0xa0000a0
#define ixD3F4_PCIE_LANE_3_EQUALIZATION_CNTL                                    0xa0000a0
#define ixD3F4_PCIE_LANE_4_EQUALIZATION_CNTL                                    0xa0000a1
#define ixD3F4_PCIE_LANE_5_EQUALIZATION_CNTL                                    0xa0000a1
#define ixD3F4_PCIE_LANE_6_EQUALIZATION_CNTL                                    0xa0000a2
#define ixD3F4_PCIE_LANE_7_EQUALIZATION_CNTL                                    0xa0000a2
#define ixD3F4_PCIE_LANE_8_EQUALIZATION_CNTL                                    0xa0000a3
#define ixD3F4_PCIE_LANE_9_EQUALIZATION_CNTL                                    0xa0000a3
#define ixD3F4_PCIE_LANE_10_EQUALIZATION_CNTL                                   0xa0000a4
#define ixD3F4_PCIE_LANE_11_EQUALIZATION_CNTL                                   0xa0000a4
#define ixD3F4_PCIE_LANE_12_EQUALIZATION_CNTL                                   0xa0000a5
#define ixD3F4_PCIE_LANE_13_EQUALIZATION_CNTL                                   0xa0000a5
#define ixD3F4_PCIE_LANE_14_EQUALIZATION_CNTL                                   0xa0000a6
#define ixD3F4_PCIE_LANE_15_EQUALIZATION_CNTL                                   0xa0000a6
#define ixD3F4_PCIE_ACS_ENH_CAP_LIST                                            0xa0000a8
#define ixD3F4_PCIE_ACS_CAP                                                     0xa0000a9
#define ixD3F4_PCIE_ACS_CNTL                                                    0xa0000a9
#define ixD3F4_PCIE_MC_ENH_CAP_LIST                                             0xa0000bc
#define ixD3F4_PCIE_MC_CAP                                                      0xa0000bd
#define ixD3F4_PCIE_MC_CNTL                                                     0xa0000bd
#define ixD3F4_PCIE_MC_ADDR0                                                    0xa0000be
#define ixD3F4_PCIE_MC_ADDR1                                                    0xa0000bf
#define ixD3F4_PCIE_MC_RCV0                                                     0xa0000c0
#define ixD3F4_PCIE_MC_RCV1                                                     0xa0000c1
#define ixD3F4_PCIE_MC_BLOCK_ALL0                                               0xa0000c2
#define ixD3F4_PCIE_MC_BLOCK_ALL1                                               0xa0000c3
#define ixD3F4_PCIE_MC_BLOCK_UNTRANSLATED_0                                     0xa0000c4
#define ixD3F4_PCIE_MC_BLOCK_UNTRANSLATED_1                                     0xa0000c5
#define ixD3F4_PCIE_MC_OVERLAY_BAR0                                             0xa0000c6
#define ixD3F4_PCIE_MC_OVERLAY_BAR1                                             0xa0000c7
#define ixD3F5_PCIE_PORT_INDEX                                                  0xb000038
#define ixD3F5_PCIE_PORT_DATA                                                   0xb000039
#define ixD3F5_PCIEP_RESERVED                                                   0x0
#define ixD3F5_PCIEP_SCRATCH                                                    0x1
#define ixD3F5_PCIEP_HW_DEBUG                                                   0x2
#define ixD3F5_PCIEP_PORT_CNTL                                                  0x10
#define ixD3F5_PCIE_TX_CNTL                                                     0x20
#define ixD3F5_PCIE_TX_REQUESTER_ID                                             0x21
#define ixD3F5_PCIE_TX_VENDOR_SPECIFIC                                          0x22
#define ixD3F5_PCIE_TX_REQUEST_NUM_CNTL                                         0x23
#define ixD3F5_PCIE_TX_SEQ                                                      0x24
#define ixD3F5_PCIE_TX_REPLAY                                                   0x25
#define ixD3F5_PCIE_TX_ACK_LATENCY_LIMIT                                        0x26
#define ixD3F5_PCIE_TX_CREDITS_ADVT_P                                           0x30
#define ixD3F5_PCIE_TX_CREDITS_ADVT_NP                                          0x31
#define ixD3F5_PCIE_TX_CREDITS_ADVT_CPL                                         0x32
#define ixD3F5_PCIE_TX_CREDITS_INIT_P                                           0x33
#define ixD3F5_PCIE_TX_CREDITS_INIT_NP                                          0x34
#define ixD3F5_PCIE_TX_CREDITS_INIT_CPL                                         0x35
#define ixD3F5_PCIE_TX_CREDITS_STATUS                                           0x36
#define ixD3F5_PCIE_TX_CREDITS_FCU_THRESHOLD                                    0x37
#define ixD3F5_PCIE_P_PORT_LANE_STATUS                                          0x50
#define ixD3F5_PCIE_FC_P                                                        0x60
#define ixD3F5_PCIE_FC_NP                                                       0x61
#define ixD3F5_PCIE_FC_CPL                                                      0x62
#define ixD3F5_PCIE_ERR_CNTL                                                    0x6a
#define ixD3F5_PCIE_RX_CNTL                                                     0x70
#define ixD3F5_PCIE_RX_EXPECTED_SEQNUM                                          0x71
#define ixD3F5_PCIE_RX_VENDOR_SPECIFIC                                          0x72
#define ixD3F5_PCIE_RX_CNTL3                                                    0x74
#define ixD3F5_PCIE_RX_CREDITS_ALLOCATED_P                                      0x80
#define ixD3F5_PCIE_RX_CREDITS_ALLOCATED_NP                                     0x81
#define ixD3F5_PCIE_RX_CREDITS_ALLOCATED_CPL                                    0x82
#define ixD3F5_PCIEP_ERROR_INJECT_PHYSICAL                                      0x83
#define ixD3F5_PCIEP_ERROR_INJECT_TRANSACTION                                   0x84
#define ixD3F5_PCIE_LC_CNTL                                                     0xa0
#define ixD3F5_PCIE_LC_CNTL2                                                    0xb1
#define ixD3F5_PCIE_LC_CNTL3                                                    0xb5
#define ixD3F5_PCIE_LC_CNTL4                                                    0xb6
#define ixD3F5_PCIE_LC_CNTL5                                                    0xb7
#define ixD3F5_PCIE_LC_CNTL6                                                    0xbb
#define ixD3F5_PCIE_LC_BW_CHANGE_CNTL                                           0xb2
#define ixD3F5_PCIE_LC_TRAINING_CNTL                                            0xa1
#define ixD3F5_PCIE_LC_LINK_WIDTH_CNTL                                          0xa2
#define ixD3F5_PCIE_LC_N_FTS_CNTL                                               0xa3
#define ixD3F5_PCIE_LC_SPEED_CNTL                                               0xa4
#define ixD3F5_PCIE_LC_CDR_CNTL                                                 0xb3
#define ixD3F5_PCIE_LC_LANE_CNTL                                                0xb4
#define ixD3F5_PCIE_LC_FORCE_COEFF                                              0xb8
#define ixD3F5_PCIE_LC_BEST_EQ_SETTINGS                                         0xb9
#define ixD3F5_PCIE_LC_FORCE_EQ_REQ_COEFF                                       0xba
#define ixD3F5_PCIE_LC_STATE0                                                   0xa5
#define ixD3F5_PCIE_LC_STATE1                                                   0xa6
#define ixD3F5_PCIE_LC_STATE2                                                   0xa7
#define ixD3F5_PCIE_LC_STATE3                                                   0xa8
#define ixD3F5_PCIE_LC_STATE4                                                   0xa9
#define ixD3F5_PCIE_LC_STATE5                                                   0xaa
#define ixD3F5_PCIEP_STRAP_LC                                                   0xc0
#define ixD3F5_PCIEP_STRAP_MISC                                                 0xc1
#define ixD3F5_PCIEP_BCH_ECC_CNTL                                               0xd0
#define ixD3F5_PCIEP_HPGI_PRIVATE                                               0xd2
#define ixD3F5_PCIEP_HPGI                                                       0xda
#define ixD3F5_VENDOR_ID                                                        0xb000000
#define ixD3F5_DEVICE_ID                                                        0xb000000
#define ixD3F5_COMMAND                                                          0xb000001
#define ixD3F5_STATUS                                                           0xb000001
#define ixD3F5_REVISION_ID                                                      0xb000002
#define ixD3F5_PROG_INTERFACE                                                   0xb000002
#define ixD3F5_SUB_CLASS                                                        0xb000002
#define ixD3F5_BASE_CLASS                                                       0xb000002
#define ixD3F5_CACHE_LINE                                                       0xb000003
#define ixD3F5_LATENCY                                                          0xb000003
#define ixD3F5_HEADER                                                           0xb000003
#define ixD3F5_BIST                                                             0xb000003
#define ixD3F5_SUB_BUS_NUMBER_LATENCY                                           0xb000006
#define ixD3F5_IO_BASE_LIMIT                                                    0xb000007
#define ixD3F5_SECONDARY_STATUS                                                 0xb000007
#define ixD3F5_MEM_BASE_LIMIT                                                   0xb000008
#define ixD3F5_PREF_BASE_LIMIT                                                  0xb000009
#define ixD3F5_PREF_BASE_UPPER                                                  0xb00000a
#define ixD3F5_PREF_LIMIT_UPPER                                                 0xb00000b
#define ixD3F5_IO_BASE_LIMIT_HI                                                 0xb00000c
#define ixD3F5_IRQ_BRIDGE_CNTL                                                  0xb00000f
#define ixD3F5_CAP_PTR                                                          0xb00000d
#define ixD3F5_INTERRUPT_LINE                                                   0xb00000f
#define ixD3F5_INTERRUPT_PIN                                                    0xb00000f
#define ixD3F5_EXT_BRIDGE_CNTL                                                  0xb000010
#define ixD3F5_PMI_CAP_LIST                                                     0xb000014
#define ixD3F5_PMI_CAP                                                          0xb000014
#define ixD3F5_PMI_STATUS_CNTL                                                  0xb000015
#define ixD3F5_PCIE_CAP_LIST                                                    0xb000016
#define ixD3F5_PCIE_CAP                                                         0xb000016
#define ixD3F5_DEVICE_CAP                                                       0xb000017
#define ixD3F5_DEVICE_CNTL                                                      0xb000018
#define ixD3F5_DEVICE_STATUS                                                    0xb000018
#define ixD3F5_LINK_CAP                                                         0xb000019
#define ixD3F5_LINK_CNTL                                                        0xb00001a
#define ixD3F5_LINK_STATUS                                                      0xb00001a
#define ixD3F5_SLOT_CAP                                                         0xb00001b
#define ixD3F5_SLOT_CNTL                                                        0xb00001c
#define ixD3F5_SLOT_STATUS                                                      0xb00001c
#define ixD3F5_ROOT_CNTL                                                        0xb00001d
#define ixD3F5_ROOT_CAP                                                         0xb00001d
#define ixD3F5_ROOT_STATUS                                                      0xb00001e
#define ixD3F5_DEVICE_CAP2                                                      0xb00001f
#define ixD3F5_DEVICE_CNTL2                                                     0xb000020
#define ixD3F5_DEVICE_STATUS2                                                   0xb000020
#define ixD3F5_LINK_CAP2                                                        0xb000021
#define ixD3F5_LINK_CNTL2                                                       0xb000022
#define ixD3F5_LINK_STATUS2                                                     0xb000022
#define ixD3F5_SLOT_CAP2                                                        0xb000023
#define ixD3F5_SLOT_CNTL2                                                       0xb000024
#define ixD3F5_SLOT_STATUS2                                                     0xb000024
#define ixD3F5_MSI_CAP_LIST                                                     0xb000028
#define ixD3F5_MSI_MSG_CNTL                                                     0xb000028
#define ixD3F5_MSI_MSG_ADDR_LO                                                  0xb000029
#define ixD3F5_MSI_MSG_ADDR_HI                                                  0xb00002a
#define ixD3F5_MSI_MSG_DATA_64                                                  0xb00002b
#define ixD3F5_MSI_MSG_DATA                                                     0xb00002a
#define ixD3F5_SSID_CAP_LIST                                                    0xb000030
#define ixD3F5_SSID_CAP                                                         0xb000031
#define ixD3F5_MSI_MAP_CAP_LIST                                                 0xb000032
#define ixD3F5_MSI_MAP_CAP                                                      0xb000032
#define ixD3F5_MSI_MAP_ADDR_LO                                                  0xb000033
#define ixD3F5_MSI_MAP_ADDR_HI                                                  0xb000034
#define ixD3F5_PCIE_VENDOR_SPECIFIC_ENH_CAP_LIST                                0xb000040
#define ixD3F5_PCIE_VENDOR_SPECIFIC_HDR                                         0xb000041
#define ixD3F5_PCIE_VENDOR_SPECIFIC1                                            0xb000042
#define ixD3F5_PCIE_VENDOR_SPECIFIC2                                            0xb000043
#define ixD3F5_PCIE_VC_ENH_CAP_LIST                                             0xb000044
#define ixD3F5_PCIE_PORT_VC_CAP_REG1                                            0xb000045
#define ixD3F5_PCIE_PORT_VC_CAP_REG2                                            0xb000046
#define ixD3F5_PCIE_PORT_VC_CNTL                                                0xb000047
#define ixD3F5_PCIE_PORT_VC_STATUS                                              0xb000047
#define ixD3F5_PCIE_VC0_RESOURCE_CAP                                            0xb000048
#define ixD3F5_PCIE_VC0_RESOURCE_CNTL                                           0xb000049
#define ixD3F5_PCIE_VC0_RESOURCE_STATUS                                         0xb00004a
#define ixD3F5_PCIE_VC1_RESOURCE_CAP                                            0xb00004b
#define ixD3F5_PCIE_VC1_RESOURCE_CNTL                                           0xb00004c
#define ixD3F5_PCIE_VC1_RESOURCE_STATUS                                         0xb00004d
#define ixD3F5_PCIE_DEV_SERIAL_NUM_ENH_CAP_LIST                                 0xb000050
#define ixD3F5_PCIE_DEV_SERIAL_NUM_DW1                                          0xb000051
#define ixD3F5_PCIE_DEV_SERIAL_NUM_DW2                                          0xb000052
#define ixD3F5_PCIE_ADV_ERR_RPT_ENH_CAP_LIST                                    0xb000054
#define ixD3F5_PCIE_UNCORR_ERR_STATUS                                           0xb000055
#define ixD3F5_PCIE_UNCORR_ERR_MASK                                             0xb000056
#define ixD3F5_PCIE_UNCORR_ERR_SEVERITY                                         0xb000057
#define ixD3F5_PCIE_CORR_ERR_STATUS                                             0xb000058
#define ixD3F5_PCIE_CORR_ERR_MASK                                               0xb000059
#define ixD3F5_PCIE_ADV_ERR_CAP_CNTL                                            0xb00005a
#define ixD3F5_PCIE_HDR_LOG0                                                    0xb00005b
#define ixD3F5_PCIE_HDR_LOG1                                                    0xb00005c
#define ixD3F5_PCIE_HDR_LOG2                                                    0xb00005d
#define ixD3F5_PCIE_HDR_LOG3                                                    0xb00005e
#define ixD3F5_PCIE_ROOT_ERR_CMD                                                0xb00005f
#define ixD3F5_PCIE_ROOT_ERR_STATUS                                             0xb000060
#define ixD3F5_PCIE_ERR_SRC_ID                                                  0xb000061
#define ixD3F5_PCIE_TLP_PREFIX_LOG0                                             0xb000062
#define ixD3F5_PCIE_TLP_PREFIX_LOG1                                             0xb000063
#define ixD3F5_PCIE_TLP_PREFIX_LOG2                                             0xb000064
#define ixD3F5_PCIE_TLP_PREFIX_LOG3                                             0xb000065
#define ixD3F5_PCIE_SECONDARY_ENH_CAP_LIST                                      0xb00009c
#define ixD3F5_PCIE_LINK_CNTL3                                                  0xb00009d
#define ixD3F5_PCIE_LANE_ERROR_STATUS                                           0xb00009e
#define ixD3F5_PCIE_LANE_0_EQUALIZATION_CNTL                                    0xb00009f
#define ixD3F5_PCIE_LANE_1_EQUALIZATION_CNTL                                    0xb00009f
#define ixD3F5_PCIE_LANE_2_EQUALIZATION_CNTL                                    0xb0000a0
#define ixD3F5_PCIE_LANE_3_EQUALIZATION_CNTL                                    0xb0000a0
#define ixD3F5_PCIE_LANE_4_EQUALIZATION_CNTL                                    0xb0000a1
#define ixD3F5_PCIE_LANE_5_EQUALIZATION_CNTL                                    0xb0000a1
#define ixD3F5_PCIE_LANE_6_EQUALIZATION_CNTL                                    0xb0000a2
#define ixD3F5_PCIE_LANE_7_EQUALIZATION_CNTL                                    0xb0000a2
#define ixD3F5_PCIE_LANE_8_EQUALIZATION_CNTL                                    0xb0000a3
#define ixD3F5_PCIE_LANE_9_EQUALIZATION_CNTL                                    0xb0000a3
#define ixD3F5_PCIE_LANE_10_EQUALIZATION_CNTL                                   0xb0000a4
#define ixD3F5_PCIE_LANE_11_EQUALIZATION_CNTL                                   0xb0000a4
#define ixD3F5_PCIE_LANE_12_EQUALIZATION_CNTL                                   0xb0000a5
#define ixD3F5_PCIE_LANE_13_EQUALIZATION_CNTL                                   0xb0000a5
#define ixD3F5_PCIE_LANE_14_EQUALIZATION_CNTL                                   0xb0000a6
#define ixD3F5_PCIE_LANE_15_EQUALIZATION_CNTL                                   0xb0000a6
#define ixD3F5_PCIE_ACS_ENH_CAP_LIST                                            0xb0000a8
#define ixD3F5_PCIE_ACS_CAP                                                     0xb0000a9
#define ixD3F5_PCIE_ACS_CNTL                                                    0xb0000a9
#define ixD3F5_PCIE_MC_ENH_CAP_LIST                                             0xb0000bc
#define ixD3F5_PCIE_MC_CAP                                                      0xb0000bd
#define ixD3F5_PCIE_MC_CNTL                                                     0xb0000bd
#define ixD3F5_PCIE_MC_ADDR0                                                    0xb0000be
#define ixD3F5_PCIE_MC_ADDR1                                                    0xb0000bf
#define ixD3F5_PCIE_MC_RCV0                                                     0xb0000c0
#define ixD3F5_PCIE_MC_RCV1                                                     0xb0000c1
#define ixD3F5_PCIE_MC_BLOCK_ALL0                                               0xb0000c2
#define ixD3F5_PCIE_MC_BLOCK_ALL1                                               0xb0000c3
#define ixD3F5_PCIE_MC_BLOCK_UNTRANSLATED_0                                     0xb0000c4
#define ixD3F5_PCIE_MC_BLOCK_UNTRANSLATED_1                                     0xb0000c5
#define ixD3F5_PCIE_MC_OVERLAY_BAR0                                             0xb0000c6
#define ixD3F5_PCIE_MC_OVERLAY_BAR1                                             0xb0000c7
#define mmC_PCIE_INDEX                                                          0x28
#define mmPCIE_WRAPPER0_C_PCIE_INDEX                                            0x28
#define mmPCIE_WRAPPER1_C_PCIE_INDEX                                            0x38
#define mmC_PCIE_DATA                                                           0x29
#define mmPCIE_WRAPPER0_C_PCIE_DATA                                             0x29
#define mmPCIE_WRAPPER1_C_PCIE_DATA                                             0x39
#define mmRFE_SNOOP_RST                                                         0x3c
#define ixPSX80_WRP_BIF_STRAP_FEATURE_EN_1                                      0x1500000
#define ixPSX80_WRP_BIF_STRAP_PI_CNTL                                           0x1500001
#define ixPSX80_WRP_BIF_STRAP_LINK_SPEED_CORE                                   0x1500002
#define ixPSX80_WRP_BIF_STRAP_LC_MISC_CORE                                      0x1500003
#define ixPSX80_WRP_BIF_STRAP_ERROR_IGNORE                                      0x1500004
#define ixPSX80_WRP_BIF_STRAP_TEST_DFT                                          0x1500005
#define ixPSX80_WRP_BIF_STRAP_ID                                                0x1500006
#define ixPSX80_WRP_BIF_STRAP_REV_ID                                            0x1500007
#define ixPSX80_WRP_BIF_STRAP_I2C_CNTL                                          0x1500008
#define ixPSX80_WRP_BIF_INT_CNTL                                                0x1500009
#define ixPSX80_WRP_BIF_STRAP_ACS                                               0x150000a
#define ixPSX80_WRP_BIF_STRAP_PM                                                0x150000b
#define ixPSX80_WRP_BIF_STRAP_FEATURE_EN_2                                      0x150000c
#define ixPSX80_WRP_BIF_SERIAL_NUM                                              0x1500045
#define ixPSX80_WRP_BIF_SSID                                                    0x1500046
#define ixPSX80_WRP_BIF_LANE_EQUALIZATION_CNTL                                  0x1500050
#define ixPSX80_WRP_PCIE_LINK_CONFIG                                            0x1500080
#define ixPSX80_WRP_PCIE_HOLD_TRAINING_A                                        0x1500800
#define ixPSX80_WRP_BIF_STRAP_LINK_SPEED_PORT_A                                 0x1500801
#define ixPSX80_WRP_BIF_STRAP_ASPM_A                                            0x1500802
#define ixPSX80_WRP_BIF_STRAP_LC_MISC_PORT_A                                    0x1500803
#define ixPSX80_WRP_BIF_STRAP_MISC_PORT_A                                       0x1500804
#define ixPSX80_WRP_BIF_STRAP_LINK_TRAINING_A                                   0x1500805
#define ixPSX80_WRP_PCIE_PORT_IS_SB_A                                           0x1500813
#define ixPSX80_WRP_PCIE_HOLD_TRAINING_B                                        0x1500900
#define ixPSX80_WRP_BIF_STRAP_LINK_SPEED_PORT_B                                 0x1500901
#define ixPSX80_WRP_BIF_STRAP_ASPM_B                                            0x1500902
#define ixPSX80_WRP_BIF_STRAP_LC_MISC_PORT_B                                    0x1500903
#define ixPSX80_WRP_BIF_STRAP_MISC_PORT_B                                       0x1500904
#define ixPSX80_WRP_BIF_STRAP_LINK_TRAINING_B                                   0x1500905
#define ixPSX80_WRP_PCIE_PORT_IS_SB_B                                           0x1500913
#define ixPSX80_WRP_PCIE_HOLD_TRAINING_C                                        0x1500a00
#define ixPSX80_WRP_BIF_STRAP_LINK_SPEED_PORT_C                                 0x1500a01
#define ixPSX80_WRP_BIF_STRAP_ASPM_C                                            0x1500a02
#define ixPSX80_WRP_BIF_STRAP_LC_MISC_PORT_C                                    0x1500a03
#define ixPSX80_WRP_BIF_STRAP_MISC_PORT_C                                       0x1500a04
#define ixPSX80_WRP_BIF_STRAP_LINK_TRAINING_C                                   0x1500a05
#define ixPSX80_WRP_PCIE_PORT_IS_SB_C                                           0x1500a13
#define ixPSX80_WRP_PCIE_HOLD_TRAINING_D                                        0x1500b00
#define ixPSX80_WRP_BIF_STRAP_LINK_SPEED_PORT_D                                 0x1500b01
#define ixPSX80_WRP_BIF_STRAP_ASPM_D                                            0x1500b02
#define ixPSX80_WRP_BIF_STRAP_LC_MISC_PORT_D                                    0x1500b03
#define ixPSX80_WRP_BIF_STRAP_MISC_PORT_D                                       0x1500b04
#define ixPSX80_WRP_BIF_STRAP_LINK_TRAINING_D                                   0x1500b05
#define ixPSX80_WRP_PCIE_PORT_IS_SB_D                                           0x1500b13
#define ixPSX80_WRP_PCIE_HOLD_TRAINING_E                                        0x1500c00
#define ixPSX80_WRP_BIF_STRAP_LINK_SPEED_PORT_E                                 0x1500c01
#define ixPSX80_WRP_BIF_STRAP_ASPM_E                                            0x1500c02
#define ixPSX80_WRP_BIF_STRAP_LC_MISC_PORT_E                                    0x1500c03
#define ixPSX80_WRP_BIF_STRAP_MISC_PORT_E                                       0x1500c04
#define ixPSX80_WRP_BIF_STRAP_LINK_TRAINING_E                                   0x1500c05
#define ixPSX80_WRP_PCIE_PORT_IS_SB_E                                           0x1500c13
#define ixPSX80_WRP_LNCNT_CONTROL                                               0x1508030
#define ixPSX80_WRP_CFG_LNC_WINDOW                                              0x1508031
#define ixPSX80_WRP_LNCNT_QUAN_THRD                                             0x1508032
#define ixPSX80_WRP_LNCNT_WEIGHT                                                0x1508033
#define ixPSX80_WRP_LNC_TOTAL_WACC                                              0x1508034
#define ixPSX80_WRP_LNC_BW_WACC                                                 0x1508035
#define ixPSX80_WRP_LNC_CMN_WACC                                                0x1508036
#define ixPSX80_WRP_PCIE_EFUSE                                                  0x150fff0
#define ixPSX80_WRP_PCIE_EFUSE2                                                 0x150fff1
#define ixPSX80_WRP_PCIE_EFUSE3                                                 0x150fff2
#define ixPSX80_WRP_PCIE_EFUSE4                                                 0x150fff3
#define ixPSX80_WRP_PCIE_EFUSE5                                                 0x150fff4
#define ixPSX80_WRP_PCIE_EFUSE6                                                 0x150fff5
#define ixPSX80_WRP_PCIE_EFUSE7                                                 0x150fff6
#define ixPSX80_WRP_PCIE_WRAP_SCRATCH1                                          0x1308001
#define ixPSX80_WRP_PCIE_WRAP_SCRATCH2                                          0x1308002
#define ixPSX80_WRP_PCIE_WRAP_REG_TARG_MISC                                     0x1308005
#define ixPSX80_WRP_PCIE_WRAP_DTM_MISC                                          0x1308006
#define ixPSX80_WRP_PCIE_WRAP_TURNAROUND_DAISYCHAIN                             0x1308007
#define ixPSX80_WRP_PCIE_WRAP_MISC                                              0x1308008
#define ixPSX80_WRP_PCIE_WRAP_PIF_MISC                                          0x1308009
#define ixPSX80_WRP_PCIE_RXDET_OVERRIDE                                         0x130800a
#define ixPSX80_WRP_IMPCTL_CNTL_PIF0                                            0x1308070
#define ixPSX80_WRP_REG_ADAPT_pciecore0_CONTROL                                 0x1308090
#define ixPSX80_WRP_REG_ADAPT_pwregt_CONTROL                                    0x1308096
#define ixPSX80_WRP_REG_ADAPT_pwregr_CONTROL                                    0x1308097
#define ixPSX80_WRP_REG_ADAPT_pif0_CONTROL                                      0x1308098
#define ixPSX80_WRP_BIOSTIMER_CMD                                               0x13080f0
#define ixPSX80_WRP_BIOSTIMER_CNTL                                              0x13080f1
#define ixPSX80_WRP_BIOSTIMER_DEBUG                                             0x13080f2
#define ixPSX80_WRP_DTM_RX_BP_CNTL                                              0x130ffe0
#define ixPSX80_WRP_DTM_CNTL                                                    0x130ffe1
#define ixPSX80_WRP_DTM_CNTL_LEGACY                                             0x130ffe2
#define ixPSX80_WRP_DTM_STI_LCLK_CTRL                                           0x130ffe3
#define ixPSX80_WRP_DTM_DENTIST_GATE_TIMING_DI_clk10x                           0x130ffe4
#define ixPSX80_WRP_DTM_DENTIST_GATE_TIMING_DI_clkGskt                          0x130ffe5
#define ixPSX80_WRP_DTM_DENTIST_GATE_TIMING_FI_clk10x                           0x130ffe6
#define ixPSX80_WRP_DTM_DENTIST_GATE_TIMING_FI_clkGskt                          0x130ffe7
#define ixPSX80_WRP_DELAYLINE_COMMAND                                           0x130ffd0
#define ixPSX80_WRP_DELAYLINE_STATUS                                            0x130ffd1
#define ixPSX81_WRP_BIF_STRAP_FEATURE_EN_1                                      0x1510000
#define ixPSX81_WRP_BIF_STRAP_PI_CNTL                                           0x1510001
#define ixPSX81_WRP_BIF_STRAP_LINK_SPEED_CORE                                   0x1510002
#define ixPSX81_WRP_BIF_STRAP_LC_MISC_CORE                                      0x1510003
#define ixPSX81_WRP_BIF_STRAP_ERROR_IGNORE                                      0x1510004
#define ixPSX81_WRP_BIF_STRAP_TEST_DFT                                          0x1510005
#define ixPSX81_WRP_BIF_STRAP_ID                                                0x1510006
#define ixPSX81_WRP_BIF_STRAP_REV_ID                                            0x1510007
#define ixPSX81_WRP_BIF_STRAP_I2C_CNTL                                          0x1510008
#define ixPSX81_WRP_BIF_INT_CNTL                                                0x1510009
#define ixPSX81_WRP_BIF_STRAP_ACS                                               0x151000a
#define ixPSX81_WRP_BIF_STRAP_PM                                                0x151000b
#define ixPSX81_WRP_BIF_STRAP_FEATURE_EN_2                                      0x151000c
#define ixPSX81_WRP_BIF_SERIAL_NUM                                              0x1510045
#define ixPSX81_WRP_BIF_SSID                                                    0x1510046
#define ixPSX81_WRP_BIF_LANE_EQUALIZATION_CNTL                                  0x1510050
#define ixPSX81_WRP_PCIE_LINK_CONFIG                                            0x1510080
#define ixPSX81_WRP_PCIE_HOLD_TRAINING_A                                        0x1510800
#define ixPSX81_WRP_BIF_STRAP_LINK_SPEED_PORT_A                                 0x1510801
#define ixPSX81_WRP_BIF_STRAP_ASPM_A                                            0x1510802
#define ixPSX81_WRP_BIF_STRAP_LC_MISC_PORT_A                                    0x1510803
#define ixPSX81_WRP_BIF_STRAP_MISC_PORT_A                                       0x1510804
#define ixPSX81_WRP_BIF_STRAP_LINK_TRAINING_A                                   0x1510805
#define ixPSX81_WRP_PCIE_PORT_IS_SB_A                                           0x1510813
#define ixPSX81_WRP_PCIE_HOLD_TRAINING_B                                        0x1510900
#define ixPSX81_WRP_BIF_STRAP_LINK_SPEED_PORT_B                                 0x1510901
#define ixPSX81_WRP_BIF_STRAP_ASPM_B                                            0x1510902
#define ixPSX81_WRP_BIF_STRAP_LC_MISC_PORT_B                                    0x1510903
#define ixPSX81_WRP_BIF_STRAP_MISC_PORT_B                                       0x1510904
#define ixPSX81_WRP_BIF_STRAP_LINK_TRAINING_B                                   0x1510905
#define ixPSX81_WRP_PCIE_PORT_IS_SB_B                                           0x1510913
#define ixPSX81_WRP_PCIE_HOLD_TRAINING_C                                        0x1510a00
#define ixPSX81_WRP_BIF_STRAP_LINK_SPEED_PORT_C                                 0x1510a01
#define ixPSX81_WRP_BIF_STRAP_ASPM_C                                            0x1510a02
#define ixPSX81_WRP_BIF_STRAP_LC_MISC_PORT_C                                    0x1510a03
#define ixPSX81_WRP_BIF_STRAP_MISC_PORT_C                                       0x1510a04
#define ixPSX81_WRP_BIF_STRAP_LINK_TRAINING_C                                   0x1510a05
#define ixPSX81_WRP_PCIE_PORT_IS_SB_C                                           0x1510a13
#define ixPSX81_WRP_PCIE_HOLD_TRAINING_D                                        0x1510b00
#define ixPSX81_WRP_BIF_STRAP_LINK_SPEED_PORT_D                                 0x1510b01
#define ixPSX81_WRP_BIF_STRAP_ASPM_D                                            0x1510b02
#define ixPSX81_WRP_BIF_STRAP_LC_MISC_PORT_D                                    0x1510b03
#define ixPSX81_WRP_BIF_STRAP_MISC_PORT_D                                       0x1510b04
#define ixPSX81_WRP_BIF_STRAP_LINK_TRAINING_D                                   0x1510b05
#define ixPSX81_WRP_PCIE_PORT_IS_SB_D                                           0x1510b13
#define ixPSX81_WRP_PCIE_HOLD_TRAINING_E                                        0x1510c00
#define ixPSX81_WRP_BIF_STRAP_LINK_SPEED_PORT_E                                 0x1510c01
#define ixPSX81_WRP_BIF_STRAP_ASPM_E                                            0x1510c02
#define ixPSX81_WRP_BIF_STRAP_LC_MISC_PORT_E                                    0x1510c03
#define ixPSX81_WRP_BIF_STRAP_MISC_PORT_E                                       0x1510c04
#define ixPSX81_WRP_BIF_STRAP_LINK_TRAINING_E                                   0x1510c05
#define ixPSX81_WRP_PCIE_PORT_IS_SB_E                                           0x1510c13
#define ixPSX81_WRP_LNCNT_CONTROL                                               0x1518030
#define ixPSX81_WRP_CFG_LNC_WINDOW                                              0x1518031
#define ixPSX81_WRP_LNCNT_QUAN_THRD                                             0x1518032
#define ixPSX81_WRP_LNCNT_WEIGHT                                                0x1518033
#define ixPSX81_WRP_LNC_TOTAL_WACC                                              0x1518034
#define ixPSX81_WRP_LNC_BW_WACC                                                 0x1518035
#define ixPSX81_WRP_LNC_CMN_WACC                                                0x1518036
#define ixPSX81_WRP_PCIE_EFUSE                                                  0x151fff0
#define ixPSX81_WRP_PCIE_EFUSE2                                                 0x151fff1
#define ixPSX81_WRP_PCIE_EFUSE3                                                 0x151fff2
#define ixPSX81_WRP_PCIE_EFUSE4                                                 0x151fff3
#define ixPSX81_WRP_PCIE_EFUSE5                                                 0x151fff4
#define ixPSX81_WRP_PCIE_EFUSE6                                                 0x151fff5
#define ixPSX81_WRP_PCIE_EFUSE7                                                 0x151fff6
#define ixPSX81_WRP_PCIE_WRAP_SCRATCH1                                          0x1318001
#define ixPSX81_WRP_PCIE_WRAP_SCRATCH2                                          0x1318002
#define ixPSX81_WRP_PCIE_WRAP_REG_TARG_MISC                                     0x1318005
#define ixPSX81_WRP_PCIE_WRAP_DTM_MISC                                          0x1318006
#define ixPSX81_WRP_PCIE_WRAP_TURNAROUND_DAISYCHAIN                             0x1318007
#define ixPSX81_WRP_PCIE_WRAP_MISC                                              0x1318008
#define ixPSX81_WRP_PCIE_WRAP_PIF_MISC                                          0x1318009
#define ixPSX81_WRP_PCIE_RXDET_OVERRIDE                                         0x131800a
#define ixPSX81_WRP_IMPCTL_CNTL_PIF0                                            0x1318070
#define ixPSX81_WRP_REG_ADAPT_pciecore0_CONTROL                                 0x1318090
#define ixPSX81_WRP_REG_ADAPT_pwregt_CONTROL                                    0x1318096
#define ixPSX81_WRP_REG_ADAPT_pwregr_CONTROL                                    0x1318097
#define ixPSX81_WRP_REG_ADAPT_pif0_CONTROL                                      0x1318098
#define ixPSX81_WRP_BIOSTIMER_CMD                                               0x13180f0
#define ixPSX81_WRP_BIOSTIMER_CNTL                                              0x13180f1
#define ixPSX81_WRP_BIOSTIMER_DEBUG                                             0x13180f2
#define ixPSX81_WRP_DTM_RX_BP_CNTL                                              0x131ffe0
#define ixPSX81_WRP_DTM_CNTL                                                    0x131ffe1
#define ixPSX81_WRP_DTM_CNTL_LEGACY                                             0x131ffe2
#define ixPSX81_WRP_DTM_STI_LCLK_CTRL                                           0x131ffe3
#define ixPSX81_WRP_DTM_DENTIST_GATE_TIMING_DI_clk10x                           0x131ffe4
#define ixPSX81_WRP_DTM_DENTIST_GATE_TIMING_DI_clkGskt                          0x131ffe5
#define ixPSX81_WRP_DTM_DENTIST_GATE_TIMING_FI_clk10x                           0x131ffe6
#define ixPSX81_WRP_DTM_DENTIST_GATE_TIMING_FI_clkGskt                          0x131ffe7
#define ixPSX81_WRP_DELAYLINE_COMMAND                                           0x131ffd0
#define ixPSX81_WRP_DELAYLINE_STATUS                                            0x131ffd1
#define ixRFE_WARMRST_CNTL                                                      0x1085164
#define ixRFE_SOFTRST_CNTL                                                      0x1080001
#define ixRFE_IMPRST_CNTL                                                       0x1085160
#define ixRFE_CLIENT_SOFTRST_TRIGGER                                            0x1080004
#define ixRFE_MASTER_SOFTRST_TRIGGER                                            0x1080005
#define ixRFE_PWDN_COMMAND                                                      0x1080010
#define ixRFE_PWDN_STATUS                                                       0x1080011
#define ixRFE_MST_PCIEW0_CMDSTATUS                                              0x1080020
#define ixRFE_MST_PCIEW1_CMDSTATUS                                              0x1080021
#define ixRFE_MST_RWREG_RFEWRC_CMDSTATUS                                        0x1080022
#define ixRFE_MST_TMOUT_STATUS                                                  0x108003f
#define ixRFE_IMPARBH_STATUS                                                    0x1085140
#define ixRFE_IMPARBH_CONTROL                                                   0x1080083
#define ixPSX80_BIF_PCIE_RESERVED                                               0x1400000
#define ixPSX80_BIF_PCIE_SCRATCH                                                0x1400001
#define ixPSX80_BIF_PCIE_HW_DEBUG                                               0x1400002
#define ixPSX80_BIF_PCIE_RX_NUM_NAK                                             0x140000e
#define ixPSX80_BIF_PCIE_RX_NUM_NAK_GENERATED                                   0x140000f
#define ixPSX80_BIF_PCIE_CNTL                                                   0x1400010
#define ixPSX80_BIF_PCIE_CONFIG_CNTL                                            0x1400011
#define ixPSX80_BIF_PCIE_DEBUG_CNTL                                             0x1400012
#define ixPSX80_BIF_PCIE_CNTL2                                                  0x140001c
#define ixPSX80_BIF_PCIE_RX_CNTL2                                               0x140001d
#define ixPSX80_BIF_PCIE_TX_F0_ATTR_CNTL                                        0x140001e
#define ixPSX80_BIF_PCIE_CI_CNTL                                                0x1400020
#define ixPSX80_BIF_PCIE_BUS_CNTL                                               0x1400021
#define ixPSX80_BIF_PCIE_LC_STATE6                                              0x1400022
#define ixPSX80_BIF_PCIE_LC_STATE7                                              0x1400023
#define ixPSX80_BIF_PCIE_LC_STATE8                                              0x1400024
#define ixPSX80_BIF_PCIE_LC_STATE9                                              0x1400025
#define ixPSX80_BIF_PCIE_LC_STATE10                                             0x1400026
#define ixPSX80_BIF_PCIE_LC_STATE11                                             0x1400027
#define ixPSX80_BIF_PCIE_LC_STATUS1                                             0x1400028
#define ixPSX80_BIF_PCIE_LC_STATUS2                                             0x1400029
#define ixPSX80_BIF_PCIE_WPR_CNTL                                               0x1400030
#define ixPSX80_BIF_PCIE_RX_LAST_TLP0                                           0x1400031
#define ixPSX80_BIF_PCIE_RX_LAST_TLP1                                           0x1400032
#define ixPSX80_BIF_PCIE_RX_LAST_TLP2                                           0x1400033
#define ixPSX80_BIF_PCIE_RX_LAST_TLP3                                           0x1400034
#define ixPSX80_BIF_PCIE_TX_LAST_TLP0                                           0x1400035
#define ixPSX80_BIF_PCIE_TX_LAST_TLP1                                           0x1400036
#define ixPSX80_BIF_PCIE_TX_LAST_TLP2                                           0x1400037
#define ixPSX80_BIF_PCIE_TX_LAST_TLP3                                           0x1400038
#define ixPSX80_BIF_PCIE_I2C_REG_ADDR_EXPAND                                    0x140003a
#define ixPSX80_BIF_PCIE_I2C_REG_DATA                                           0x140003b
#define ixPSX80_BIF_PCIE_CFG_CNTL                                               0x140003c
#define ixPSX80_BIF_PCIE_LC_PM_CNTL                                             0x140003d
#define ixPSX80_BIF_PCIE_P_CNTL                                                 0x1400040
#define ixPSX80_BIF_PCIE_P_BUF_STATUS                                           0x1400041
#define ixPSX80_BIF_PCIE_P_DECODER_STATUS                                       0x1400042
#define ixPSX80_BIF_PCIE_P_MISC_STATUS                                          0x1400043
#define ixPSX80_BIF_PCIE_P_RCV_L0S_FTS_DET                                      0x1400050
#define ixPSX80_BIF_PCIE_PERF_COUNT_CNTL                                        0x1400080
#define ixPSX80_BIF_PCIE_PERF_CNTL_TXCLK                                        0x1400081
#define ixPSX80_BIF_PCIE_PERF_COUNT0_TXCLK                                      0x1400082
#define ixPSX80_BIF_PCIE_PERF_COUNT1_TXCLK                                      0x1400083
#define ixPSX80_BIF_PCIE_PERF_CNTL_MST_R_CLK                                    0x1400084
#define ixPSX80_BIF_PCIE_PERF_COUNT0_MST_R_CLK                                  0x1400085
#define ixPSX80_BIF_PCIE_PERF_COUNT1_MST_R_CLK                                  0x1400086
#define ixPSX80_BIF_PCIE_PERF_CNTL_MST_C_CLK                                    0x1400087
#define ixPSX80_BIF_PCIE_PERF_COUNT0_MST_C_CLK                                  0x1400088
#define ixPSX80_BIF_PCIE_PERF_COUNT1_MST_C_CLK                                  0x1400089
#define ixPSX80_BIF_PCIE_PERF_CNTL_SLV_R_CLK                                    0x140008a
#define ixPSX80_BIF_PCIE_PERF_COUNT0_SLV_R_CLK                                  0x140008b
#define ixPSX80_BIF_PCIE_PERF_COUNT1_SLV_R_CLK                                  0x140008c
#define ixPSX80_BIF_PCIE_PERF_CNTL_SLV_S_C_CLK                                  0x140008d
#define ixPSX80_BIF_PCIE_PERF_COUNT0_SLV_S_C_CLK                                0x140008e
#define ixPSX80_BIF_PCIE_PERF_COUNT1_SLV_S_C_CLK                                0x140008f
#define ixPSX80_BIF_PCIE_PERF_CNTL_SLV_NS_C_CLK                                 0x1400090
#define ixPSX80_BIF_PCIE_PERF_COUNT0_SLV_NS_C_CLK                               0x1400091
#define ixPSX80_BIF_PCIE_PERF_COUNT1_SLV_NS_C_CLK                               0x1400092
#define ixPSX80_BIF_PCIE_PERF_CNTL_EVENT0_PORT_SEL                              0x1400093
#define ixPSX80_BIF_PCIE_PERF_CNTL_EVENT1_PORT_SEL                              0x1400094
#define ixPSX80_BIF_PCIE_PERF_CNTL_TXCLK2                                       0x1400095
#define ixPSX80_BIF_PCIE_PERF_COUNT0_TXCLK2                                     0x1400096
#define ixPSX80_BIF_PCIE_PERF_COUNT1_TXCLK2                                     0x1400097
#define ixPSX80_BIF_PCIE_STRAP_F0                                               0x14000b0
#define ixPSX80_BIF_PCIE_STRAP_MISC                                             0x14000c0
#define ixPSX80_BIF_PCIE_STRAP_MISC2                                            0x14000c1
#define ixPSX80_BIF_PCIE_STRAP_PI                                               0x14000c2
#define ixPSX80_BIF_PCIE_STRAP_I2C_BD                                           0x14000c4
#define ixPSX80_BIF_PCIE_PRBS_CLR                                               0x14000c8
#define ixPSX80_BIF_PCIE_PRBS_STATUS1                                           0x14000c9
#define ixPSX80_BIF_PCIE_PRBS_STATUS2                                           0x14000ca
#define ixPSX80_BIF_PCIE_PRBS_FREERUN                                           0x14000cb
#define ixPSX80_BIF_PCIE_PRBS_MISC                                              0x14000cc
#define ixPSX80_BIF_PCIE_PRBS_USER_PATTERN                                      0x14000cd
#define ixPSX80_BIF_PCIE_PRBS_LO_BITCNT                                         0x14000ce
#define ixPSX80_BIF_PCIE_PRBS_HI_BITCNT                                         0x14000cf
#define ixPSX80_BIF_PCIE_PRBS_ERRCNT_0                                          0x14000d0
#define ixPSX80_BIF_PCIE_PRBS_ERRCNT_1                                          0x14000d1
#define ixPSX80_BIF_PCIE_PRBS_ERRCNT_2                                          0x14000d2
#define ixPSX80_BIF_PCIE_PRBS_ERRCNT_3                                          0x14000d3
#define ixPSX80_BIF_PCIE_PRBS_ERRCNT_4                                          0x14000d4
#define ixPSX80_BIF_PCIE_PRBS_ERRCNT_5                                          0x14000d5
#define ixPSX80_BIF_PCIE_PRBS_ERRCNT_6                                          0x14000d6
#define ixPSX80_BIF_PCIE_PRBS_ERRCNT_7                                          0x14000d7
#define ixPSX80_BIF_PCIE_PRBS_ERRCNT_8                                          0x14000d8
#define ixPSX80_BIF_PCIE_PRBS_ERRCNT_9                                          0x14000d9
#define ixPSX80_BIF_PCIE_PRBS_ERRCNT_10                                         0x14000da
#define ixPSX80_BIF_PCIE_PRBS_ERRCNT_11                                         0x14000db
#define ixPSX80_BIF_PCIE_PRBS_ERRCNT_12                                         0x14000dc
#define ixPSX80_BIF_PCIE_PRBS_ERRCNT_13                                         0x14000dd
#define ixPSX80_BIF_PCIE_PRBS_ERRCNT_14                                         0x14000de
#define ixPSX80_BIF_PCIE_PRBS_ERRCNT_15                                         0x14000df
#define ixPSX80_BIF_SWRST_COMMAND_STATUS                                        0x1400100
#define ixPSX80_BIF_SWRST_GENERAL_CONTROL                                       0x1400101
#define ixPSX80_BIF_SWRST_COMMAND_0                                             0x1400102
#define ixPSX80_BIF_SWRST_COMMAND_1                                             0x1400103
#define ixPSX80_BIF_SWRST_CONTROL_0                                             0x1400104
#define ixPSX80_BIF_SWRST_CONTROL_1                                             0x1400105
#define ixPSX80_BIF_SWRST_CONTROL_2                                             0x1400106
#define ixPSX80_BIF_SWRST_CONTROL_3                                             0x1400107
#define ixPSX80_BIF_SWRST_CONTROL_4                                             0x1400108
#define ixPSX80_BIF_SWRST_CONTROL_5                                             0x1400109
#define ixPSX80_BIF_SWRST_CONTROL_6                                             0x140010a
#define ixPSX80_BIF_CPM_CONTROL                                                 0x1400118
#define ixPSX80_BIF_LM_CONTROL                                                  0x1400120
#define ixPSX80_BIF_LM_PCIETXMUX0                                               0x1400121
#define ixPSX80_BIF_LM_PCIETXMUX1                                               0x1400122
#define ixPSX80_BIF_LM_PCIETXMUX2                                               0x1400123
#define ixPSX80_BIF_LM_PCIETXMUX3                                               0x1400124
#define ixPSX80_BIF_LM_PCIERXMUX0                                               0x1400125
#define ixPSX80_BIF_LM_PCIERXMUX1                                               0x1400126
#define ixPSX80_BIF_LM_PCIERXMUX2                                               0x1400127
#define ixPSX80_BIF_LM_PCIERXMUX3                                               0x1400128
#define ixPSX80_BIF_LM_LANEENABLE                                               0x1400129
#define ixPSX80_BIF_LM_PRBSCONTROL                                              0x140012a
#define ixPSX80_BIF_LM_POWERCONTROL                                             0x140012b
#define ixPSX80_BIF_LM_POWERCONTROL1                                            0x140012c
#define ixPSX80_BIF_LM_POWERCONTROL2                                            0x140012d
#define ixPSX80_BIF_LM_POWERCONTROL3                                            0x140012e
#define ixPSX80_BIF_LM_POWERCONTROL4                                            0x140012f
#define ixPSX81_BIF_PCIE_RESERVED                                               0x1410000
#define ixPSX81_BIF_PCIE_SCRATCH                                                0x1410001
#define ixPSX81_BIF_PCIE_HW_DEBUG                                               0x1410002
#define ixPSX81_BIF_PCIE_RX_NUM_NAK                                             0x141000e
#define ixPSX81_BIF_PCIE_RX_NUM_NAK_GENERATED                                   0x141000f
#define ixPSX81_BIF_PCIE_CNTL                                                   0x1410010
#define ixPSX81_BIF_PCIE_CONFIG_CNTL                                            0x1410011
#define ixPSX81_BIF_PCIE_DEBUG_CNTL                                             0x1410012
#define ixPSX81_BIF_PCIE_CNTL2                                                  0x141001c
#define ixPSX81_BIF_PCIE_RX_CNTL2                                               0x141001d
#define ixPSX81_BIF_PCIE_TX_F0_ATTR_CNTL                                        0x141001e
#define ixPSX81_BIF_PCIE_CI_CNTL                                                0x1410020
#define ixPSX81_BIF_PCIE_BUS_CNTL                                               0x1410021
#define ixPSX81_BIF_PCIE_LC_STATE6                                              0x1410022
#define ixPSX81_BIF_PCIE_LC_STATE7                                              0x1410023
#define ixPSX81_BIF_PCIE_LC_STATE8                                              0x1410024
#define ixPSX81_BIF_PCIE_LC_STATE9                                              0x1410025
#define ixPSX81_BIF_PCIE_LC_STATE10                                             0x1410026
#define ixPSX81_BIF_PCIE_LC_STATE11                                             0x1410027
#define ixPSX81_BIF_PCIE_LC_STATUS1                                             0x1410028
#define ixPSX81_BIF_PCIE_LC_STATUS2                                             0x1410029
#define ixPSX81_BIF_PCIE_WPR_CNTL                                               0x1410030
#define ixPSX81_BIF_PCIE_RX_LAST_TLP0                                           0x1410031
#define ixPSX81_BIF_PCIE_RX_LAST_TLP1                                           0x1410032
#define ixPSX81_BIF_PCIE_RX_LAST_TLP2                                           0x1410033
#define ixPSX81_BIF_PCIE_RX_LAST_TLP3                                           0x1410034
#define ixPSX81_BIF_PCIE_TX_LAST_TLP0                                           0x1410035
#define ixPSX81_BIF_PCIE_TX_LAST_TLP1                                           0x1410036
#define ixPSX81_BIF_PCIE_TX_LAST_TLP2                                           0x1410037
#define ixPSX81_BIF_PCIE_TX_LAST_TLP3                                           0x1410038
#define ixPSX81_BIF_PCIE_I2C_REG_ADDR_EXPAND                                    0x141003a
#define ixPSX81_BIF_PCIE_I2C_REG_DATA                                           0x141003b
#define ixPSX81_BIF_PCIE_CFG_CNTL                                               0x141003c
#define ixPSX81_BIF_PCIE_LC_PM_CNTL                                             0x141003d
#define ixPSX81_BIF_PCIE_P_CNTL                                                 0x1410040
#define ixPSX81_BIF_PCIE_P_BUF_STATUS                                           0x1410041
#define ixPSX81_BIF_PCIE_P_DECODER_STATUS                                       0x1410042
#define ixPSX81_BIF_PCIE_P_MISC_STATUS                                          0x1410043
#define ixPSX81_BIF_PCIE_P_RCV_L0S_FTS_DET                                      0x1410050
#define ixPSX81_BIF_PCIE_PERF_COUNT_CNTL                                        0x1410080
#define ixPSX81_BIF_PCIE_PERF_CNTL_TXCLK                                        0x1410081
#define ixPSX81_BIF_PCIE_PERF_COUNT0_TXCLK                                      0x1410082
#define ixPSX81_BIF_PCIE_PERF_COUNT1_TXCLK                                      0x1410083
#define ixPSX81_BIF_PCIE_PERF_CNTL_MST_R_CLK                                    0x1410084
#define ixPSX81_BIF_PCIE_PERF_COUNT0_MST_R_CLK                                  0x1410085
#define ixPSX81_BIF_PCIE_PERF_COUNT1_MST_R_CLK                                  0x1410086
#define ixPSX81_BIF_PCIE_PERF_CNTL_MST_C_CLK                                    0x1410087
#define ixPSX81_BIF_PCIE_PERF_COUNT0_MST_C_CLK                                  0x1410088
#define ixPSX81_BIF_PCIE_PERF_COUNT1_MST_C_CLK                                  0x1410089
#define ixPSX81_BIF_PCIE_PERF_CNTL_SLV_R_CLK                                    0x141008a
#define ixPSX81_BIF_PCIE_PERF_COUNT0_SLV_R_CLK                                  0x141008b
#define ixPSX81_BIF_PCIE_PERF_COUNT1_SLV_R_CLK                                  0x141008c
#define ixPSX81_BIF_PCIE_PERF_CNTL_SLV_S_C_CLK                                  0x141008d
#define ixPSX81_BIF_PCIE_PERF_COUNT0_SLV_S_C_CLK                                0x141008e
#define ixPSX81_BIF_PCIE_PERF_COUNT1_SLV_S_C_CLK                                0x141008f
#define ixPSX81_BIF_PCIE_PERF_CNTL_SLV_NS_C_CLK                                 0x1410090
#define ixPSX81_BIF_PCIE_PERF_COUNT0_SLV_NS_C_CLK                               0x1410091
#define ixPSX81_BIF_PCIE_PERF_COUNT1_SLV_NS_C_CLK                               0x1410092
#define ixPSX81_BIF_PCIE_PERF_CNTL_EVENT0_PORT_SEL                              0x1410093
#define ixPSX81_BIF_PCIE_PERF_CNTL_EVENT1_PORT_SEL                              0x1410094
#define ixPSX81_BIF_PCIE_PERF_CNTL_TXCLK2                                       0x1410095
#define ixPSX81_BIF_PCIE_PERF_COUNT0_TXCLK2                                     0x1410096
#define ixPSX81_BIF_PCIE_PERF_COUNT1_TXCLK2                                     0x1410097
#define ixPSX81_BIF_PCIE_STRAP_F0                                               0x14100b0
#define ixPSX81_BIF_PCIE_STRAP_MISC                                             0x14100c0
#define ixPSX81_BIF_PCIE_STRAP_MISC2                                            0x14100c1
#define ixPSX81_BIF_PCIE_STRAP_PI                                               0x14100c2
#define ixPSX81_BIF_PCIE_STRAP_I2C_BD                                           0x14100c4
#define ixPSX81_BIF_PCIE_PRBS_CLR                                               0x14100c8
#define ixPSX81_BIF_PCIE_PRBS_STATUS1                                           0x14100c9
#define ixPSX81_BIF_PCIE_PRBS_STATUS2                                           0x14100ca
#define ixPSX81_BIF_PCIE_PRBS_FREERUN                                           0x14100cb
#define ixPSX81_BIF_PCIE_PRBS_MISC                                              0x14100cc
#define ixPSX81_BIF_PCIE_PRBS_USER_PATTERN                                      0x14100cd
#define ixPSX81_BIF_PCIE_PRBS_LO_BITCNT                                         0x14100ce
#define ixPSX81_BIF_PCIE_PRBS_HI_BITCNT                                         0x14100cf
#define ixPSX81_BIF_PCIE_PRBS_ERRCNT_0                                          0x14100d0
#define ixPSX81_BIF_PCIE_PRBS_ERRCNT_1                                          0x14100d1
#define ixPSX81_BIF_PCIE_PRBS_ERRCNT_2                                          0x14100d2
#define ixPSX81_BIF_PCIE_PRBS_ERRCNT_3                                          0x14100d3
#define ixPSX81_BIF_PCIE_PRBS_ERRCNT_4                                          0x14100d4
#define ixPSX81_BIF_PCIE_PRBS_ERRCNT_5                                          0x14100d5
#define ixPSX81_BIF_PCIE_PRBS_ERRCNT_6                                          0x14100d6
#define ixPSX81_BIF_PCIE_PRBS_ERRCNT_7                                          0x14100d7
#define ixPSX81_BIF_PCIE_PRBS_ERRCNT_8                                          0x14100d8
#define ixPSX81_BIF_PCIE_PRBS_ERRCNT_9                                          0x14100d9
#define ixPSX81_BIF_PCIE_PRBS_ERRCNT_10                                         0x14100da
#define ixPSX81_BIF_PCIE_PRBS_ERRCNT_11                                         0x14100db
#define ixPSX81_BIF_PCIE_PRBS_ERRCNT_12                                         0x14100dc
#define ixPSX81_BIF_PCIE_PRBS_ERRCNT_13                                         0x14100dd
#define ixPSX81_BIF_PCIE_PRBS_ERRCNT_14                                         0x14100de
#define ixPSX81_BIF_PCIE_PRBS_ERRCNT_15                                         0x14100df
#define ixPSX81_BIF_SWRST_COMMAND_STATUS                                        0x1410100
#define ixPSX81_BIF_SWRST_GENERAL_CONTROL                                       0x1410101
#define ixPSX81_BIF_SWRST_COMMAND_0                                             0x1410102
#define ixPSX81_BIF_SWRST_COMMAND_1                                             0x1410103
#define ixPSX81_BIF_SWRST_CONTROL_0                                             0x1410104
#define ixPSX81_BIF_SWRST_CONTROL_1                                             0x1410105
#define ixPSX81_BIF_SWRST_CONTROL_2                                             0x1410106
#define ixPSX81_BIF_SWRST_CONTROL_3                                             0x1410107
#define ixPSX81_BIF_SWRST_CONTROL_4                                             0x1410108
#define ixPSX81_BIF_SWRST_CONTROL_5                                             0x1410109
#define ixPSX81_BIF_SWRST_CONTROL_6                                             0x141010a
#define ixPSX81_BIF_CPM_CONTROL                                                 0x1410118
#define ixPSX81_BIF_LM_CONTROL                                                  0x1410120
#define ixPSX81_BIF_LM_PCIETXMUX0                                               0x1410121
#define ixPSX81_BIF_LM_PCIETXMUX1                                               0x1410122
#define ixPSX81_BIF_LM_PCIETXMUX2                                               0x1410123
#define ixPSX81_BIF_LM_PCIETXMUX3                                               0x1410124
#define ixPSX81_BIF_LM_PCIERXMUX0                                               0x1410125
#define ixPSX81_BIF_LM_PCIERXMUX1                                               0x1410126
#define ixPSX81_BIF_LM_PCIERXMUX2                                               0x1410127
#define ixPSX81_BIF_LM_PCIERXMUX3                                               0x1410128
#define ixPSX81_BIF_LM_LANEENABLE                                               0x1410129
#define ixPSX81_BIF_LM_PRBSCONTROL                                              0x141012a
#define ixPSX81_BIF_LM_POWERCONTROL                                             0x141012b
#define ixPSX81_BIF_LM_POWERCONTROL1                                            0x141012c
#define ixPSX81_BIF_LM_POWERCONTROL2                                            0x141012d
#define ixPSX81_BIF_LM_POWERCONTROL3                                            0x141012e
#define ixPSX81_BIF_LM_POWERCONTROL4                                            0x141012f
#define ixPSX80_PHY0_COM_COMMON_FUSE1                                           0x1206200
#define ixPSX80_PHY0_COM_COMMON_FUSE2                                           0x1206201
#define ixPSX80_PHY0_COM_COMMON_FUSE3                                           0x1206202
#define ixPSX80_PHY0_COM_COMMON_ELECIDLE                                        0x1206204
#define ixPSX80_PHY0_COM_COMMON_DFX                                             0x1206205
#define ixPSX80_PHY0_COM_COMMON_MAR_DEEMPH_NOM                                  0x1206206
#define ixPSX80_PHY0_COM_COMMON_SELDEEMPH35                                     0x1206207
#define ixPSX80_PHY0_COM_COMMON_SELDEEMPH60                                     0x1206208
#define ixPSX80_PHY0_COM_COMMON_LANE_PWRMGMT                                    0x1206209
#define ixPSX80_PHY0_COM_COMMON_ADAPTCTL1                                       0x120620a
#define ixPSX80_PHY0_COM_COMMON_ADAPTCTL2                                       0x120620b
#define ixPSX80_PHY0_COM_COMMON_ADAPT_CFG_BYP_VAL                               0x120620c
#define ixPSX80_PHY0_COM_COMMON_ADAPT_CFG_BYP_VAL1                              0x120620d
#define ixPSX80_PHY0_COM_COMMON_ADAPT_DBG_BYP_VAL                               0x120620e
#define ixPSX80_PHY0_COM_COMMON_ADAPT_DBG_BYP_VAL1                              0x120620f
#define ixPSX80_PHY0_COM_COMMON_ADAPT_DBG1                                      0x1206210
#define ixPSX80_PHY0_COM_COMMON_LNCNTRL                                         0x1206211
#define ixPSX80_PHY0_COM_COMMON_TXTESTDEBUG                                     0x1206212
#define ixPSX80_PHY0_COM_COMMON_RXTESTDEBUG                                     0x1206213
#define ixPSX80_PHY0_COM_COMMON_CDR_PHCTL                                       0x1206214
#define ixPSX80_PHY0_COM_COMMON_CDR_FRCTL                                       0x1206215
#define ixPSX80_PHY0_RX_CMD_BUS_RX_CONTROL_BROADCAST                            0x120fe00
#define ixPSX80_PHY0_RX_CMD_BUS_RX_CONTROL_LANE0                                0x1200000
#define ixPSX80_PHY0_RX_CMD_BUS_RX_CONTROL_LANE1                                0x1200100
#define ixPSX80_PHY0_RX_CMD_BUS_RX_CONTROL_LANE2                                0x1200200
#define ixPSX80_PHY0_RX_CMD_BUS_RX_CONTROL_LANE3                                0x1200300
#define ixPSX80_PHY0_RX_CMD_BUS_RX_CONTROL_LANE4                                0x1200400
#define ixPSX80_PHY0_RX_CMD_BUS_RX_CONTROL_LANE5                                0x1200500
#define ixPSX80_PHY0_RX_CMD_BUS_RX_CONTROL_LANE6                                0x1200600
#define ixPSX80_PHY0_RX_CMD_BUS_RX_CONTROL_LANE7                                0x1200700
#define ixPSX80_PHY0_RX_CMD_BUS_GLOBAL_FOR_RX_BROADCAST                         0x120fe01
#define ixPSX80_PHY0_RX_CMD_BUS_GLOBAL_FOR_RX_LANE0                             0x1200001
#define ixPSX80_PHY0_RX_CMD_BUS_GLOBAL_FOR_RX_LANE1                             0x1200101
#define ixPSX80_PHY0_RX_CMD_BUS_GLOBAL_FOR_RX_LANE2                             0x1200201
#define ixPSX80_PHY0_RX_CMD_BUS_GLOBAL_FOR_RX_LANE3                             0x1200301
#define ixPSX80_PHY0_RX_CMD_BUS_GLOBAL_FOR_RX_LANE4                             0x1200401
#define ixPSX80_PHY0_RX_CMD_BUS_GLOBAL_FOR_RX_LANE5                             0x1200501
#define ixPSX80_PHY0_RX_CMD_BUS_GLOBAL_FOR_RX_LANE6                             0x1200601
#define ixPSX80_PHY0_RX_CMD_BUS_GLOBAL_FOR_RX_LANE7                             0x1200701
#define ixPSX80_PHY0_RX_RX_CTL_BROADCAST                                        0x120fe02
#define ixPSX80_PHY0_RX_RX_CTL_LANE0                                            0x1200002
#define ixPSX80_PHY0_RX_RX_CTL_LANE1                                            0x1200102
#define ixPSX80_PHY0_RX_RX_CTL_LANE2                                            0x1200202
#define ixPSX80_PHY0_RX_RX_CTL_LANE3                                            0x1200302
#define ixPSX80_PHY0_RX_RX_CTL_LANE4                                            0x1200402
#define ixPSX80_PHY0_RX_RX_CTL_LANE5                                            0x1200502
#define ixPSX80_PHY0_RX_RX_CTL_LANE6                                            0x1200602
#define ixPSX80_PHY0_RX_RX_CTL_LANE7                                            0x1200702
#define ixPSX80_PHY0_RX_DLL_CTL_BROADCAST                                       0x120fe03
#define ixPSX80_PHY0_RX_DLL_CTL_LANE0                                           0x1200003
#define ixPSX80_PHY0_RX_DLL_CTL_LANE1                                           0x1200103
#define ixPSX80_PHY0_RX_DLL_CTL_LANE2                                           0x1200203
#define ixPSX80_PHY0_RX_DLL_CTL_LANE3                                           0x1200303
#define ixPSX80_PHY0_RX_DLL_CTL_LANE4                                           0x1200403
#define ixPSX80_PHY0_RX_DLL_CTL_LANE5                                           0x1200503
#define ixPSX80_PHY0_RX_DLL_CTL_LANE6                                           0x1200603
#define ixPSX80_PHY0_RX_DLL_CTL_LANE7                                           0x1200703
#define ixPSX80_PHY0_RX_RXTEST_REGS_BROADCAST                                   0x120fe04
#define ixPSX80_PHY0_RX_RXTEST_REGS_LANE0                                       0x1200004
#define ixPSX80_PHY0_RX_RXTEST_REGS_LANE1                                       0x1200104
#define ixPSX80_PHY0_RX_RXTEST_REGS_LANE2                                       0x1200204
#define ixPSX80_PHY0_RX_RXTEST_REGS_LANE3                                       0x1200304
#define ixPSX80_PHY0_RX_RXTEST_REGS_LANE4                                       0x1200404
#define ixPSX80_PHY0_RX_RXTEST_REGS_LANE5                                       0x1200504
#define ixPSX80_PHY0_RX_RXTEST_REGS_LANE6                                       0x1200604
#define ixPSX80_PHY0_RX_RXTEST_REGS_LANE7                                       0x1200704
#define ixPSX80_PHY0_RX_ELECIDLE_DEBUG_BROADCAST                                0x120fe05
#define ixPSX80_PHY0_RX_ELECIDLE_DEBUG_LANE0                                    0x1200005
#define ixPSX80_PHY0_RX_ELECIDLE_DEBUG_LANE1                                    0x1200105
#define ixPSX80_PHY0_RX_ELECIDLE_DEBUG_LANE2                                    0x1200205
#define ixPSX80_PHY0_RX_ELECIDLE_DEBUG_LANE3                                    0x1200305
#define ixPSX80_PHY0_RX_ELECIDLE_DEBUG_LANE4                                    0x1200405
#define ixPSX80_PHY0_RX_ELECIDLE_DEBUG_LANE5                                    0x1200505
#define ixPSX80_PHY0_RX_ELECIDLE_DEBUG_LANE6                                    0x1200605
#define ixPSX80_PHY0_RX_ELECIDLE_DEBUG_LANE7                                    0x1200705
#define ixPSX80_PHY0_RX_ADAPTCTL_BROADCAST                                      0x120fe0a
#define ixPSX80_PHY0_RX_ADAPTCTL_LANE0                                          0x120000a
#define ixPSX80_PHY0_RX_ADAPTCTL_LANE1                                          0x120010a
#define ixPSX80_PHY0_RX_ADAPTCTL_LANE2                                          0x120020a
#define ixPSX80_PHY0_RX_ADAPTCTL_LANE3                                          0x120030a
#define ixPSX80_PHY0_RX_ADAPTCTL_LANE4                                          0x120040a
#define ixPSX80_PHY0_RX_ADAPTCTL_LANE5                                          0x120050a
#define ixPSX80_PHY0_RX_ADAPTCTL_LANE6                                          0x120060a
#define ixPSX80_PHY0_RX_ADAPTCTL_LANE7                                          0x120070a
#define ixPSX80_PHY0_RX_FOMCALCCTL_BROADCAST                                    0x120fe0b
#define ixPSX80_PHY0_RX_FOMCALCCTL_LANE0                                        0x120000b
#define ixPSX80_PHY0_RX_FOMCALCCTL_LANE1                                        0x120010b
#define ixPSX80_PHY0_RX_FOMCALCCTL_LANE2                                        0x120020b
#define ixPSX80_PHY0_RX_FOMCALCCTL_LANE3                                        0x120030b
#define ixPSX80_PHY0_RX_FOMCALCCTL_LANE4                                        0x120040b
#define ixPSX80_PHY0_RX_FOMCALCCTL_LANE5                                        0x120050b
#define ixPSX80_PHY0_RX_FOMCALCCTL_LANE6                                        0x120060b
#define ixPSX80_PHY0_RX_FOMCALCCTL_LANE7                                        0x120070b
#define ixPSX80_PHY0_RX_ADAPT_CFG_BYP_EN_BROADCAST                              0x120fe0c
#define ixPSX80_PHY0_RX_ADAPT_CFG_BYP_EN_LANE0                                  0x120000c
#define ixPSX80_PHY0_RX_ADAPT_CFG_BYP_EN_LANE1                                  0x120010c
#define ixPSX80_PHY0_RX_ADAPT_CFG_BYP_EN_LANE2                                  0x120020c
#define ixPSX80_PHY0_RX_ADAPT_CFG_BYP_EN_LANE3                                  0x120030c
#define ixPSX80_PHY0_RX_ADAPT_CFG_BYP_EN_LANE4                                  0x120040c
#define ixPSX80_PHY0_RX_ADAPT_CFG_BYP_EN_LANE5                                  0x120050c
#define ixPSX80_PHY0_RX_ADAPT_CFG_BYP_EN_LANE6                                  0x120060c
#define ixPSX80_PHY0_RX_ADAPT_CFG_BYP_EN_LANE7                                  0x120070c
#define ixPSX80_PHY0_RX_DBG_BYP_EN_BROADCAST                                    0x120fe0d
#define ixPSX80_PHY0_RX_DBG_BYP_EN_LANE0                                        0x120000d
#define ixPSX80_PHY0_RX_DBG_BYP_EN_LANE1                                        0x120010d
#define ixPSX80_PHY0_RX_DBG_BYP_EN_LANE2                                        0x120020d
#define ixPSX80_PHY0_RX_DBG_BYP_EN_LANE3                                        0x120030d
#define ixPSX80_PHY0_RX_DBG_BYP_EN_LANE4                                        0x120040d
#define ixPSX80_PHY0_RX_DBG_BYP_EN_LANE5                                        0x120050d
#define ixPSX80_PHY0_RX_DBG_BYP_EN_LANE6                                        0x120060d
#define ixPSX80_PHY0_RX_DBG_BYP_EN_LANE7                                        0x120070d
#define ixPSX80_PHY0_RX_ADAPTDBG1_BROADCAST                                     0x120fe0e
#define ixPSX80_PHY0_RX_ADAPTDBG1_LANE0                                         0x120000e
#define ixPSX80_PHY0_RX_ADAPTDBG1_LANE1                                         0x120010e
#define ixPSX80_PHY0_RX_ADAPTDBG1_LANE2                                         0x120020e
#define ixPSX80_PHY0_RX_ADAPTDBG1_LANE3                                         0x120030e
#define ixPSX80_PHY0_RX_ADAPTDBG1_LANE4                                         0x120040e
#define ixPSX80_PHY0_RX_ADAPTDBG1_LANE5                                         0x120050e
#define ixPSX80_PHY0_RX_ADAPTDBG1_LANE6                                         0x120060e
#define ixPSX80_PHY0_RX_ADAPTDBG1_LANE7                                         0x120070e
#define ixPSX80_PHY0_TX_CMD_BUS_TX_CONTROL_BROADCAST                            0x120ff00
#define ixPSX80_PHY0_TX_CMD_BUS_TX_CONTROL_LANE0                                0x1202000
#define ixPSX80_PHY0_TX_CMD_BUS_TX_CONTROL_LANE1                                0x1202100
#define ixPSX80_PHY0_TX_CMD_BUS_TX_CONTROL_LANE2                                0x1202200
#define ixPSX80_PHY0_TX_CMD_BUS_TX_CONTROL_LANE3                                0x1202300
#define ixPSX80_PHY0_TX_CMD_BUS_TX_CONTROL_LANE4                                0x1202400
#define ixPSX80_PHY0_TX_CMD_BUS_TX_CONTROL_LANE5                                0x1202500
#define ixPSX80_PHY0_TX_CMD_BUS_TX_CONTROL_LANE6                                0x1202600
#define ixPSX80_PHY0_TX_CMD_BUS_TX_CONTROL_LANE7                                0x1202700
#define ixPSX80_PHY0_TX_DFX_BROADCAST                                           0x120ff01
#define ixPSX80_PHY0_TX_DFX_LANE0                                               0x1202001
#define ixPSX80_PHY0_TX_DFX_LANE1                                               0x1202101
#define ixPSX80_PHY0_TX_DFX_LANE2                                               0x1202201
#define ixPSX80_PHY0_TX_DFX_LANE3                                               0x1202301
#define ixPSX80_PHY0_TX_DFX_LANE4                                               0x1202401
#define ixPSX80_PHY0_TX_DFX_LANE5                                               0x1202501
#define ixPSX80_PHY0_TX_DFX_LANE6                                               0x1202601
#define ixPSX80_PHY0_TX_DFX_LANE7                                               0x1202701
#define ixPSX80_PHY0_TX_DEEMPH_BROADCAST                                        0x120ff02
#define ixPSX80_PHY0_TX_DEEMPH_LANE0                                            0x1202002
#define ixPSX80_PHY0_TX_DEEMPH_LANE1                                            0x1202102
#define ixPSX80_PHY0_TX_DEEMPH_LANE2                                            0x1202202
#define ixPSX80_PHY0_TX_DEEMPH_LANE3                                            0x1202302
#define ixPSX80_PHY0_TX_DEEMPH_LANE4                                            0x1202402
#define ixPSX80_PHY0_TX_DEEMPH_LANE5                                            0x1202502
#define ixPSX80_PHY0_TX_DEEMPH_LANE6                                            0x1202602
#define ixPSX80_PHY0_TX_DEEMPH_LANE7                                            0x1202702
#define ixPSX80_PHY0_TX_TSTMARGDEEMPH_BROADCAST                                 0x120ff03
#define ixPSX80_PHY0_TX_TSTMARGDEEMPH_LANE0                                     0x1202003
#define ixPSX80_PHY0_TX_TSTMARGDEEMPH_LANE1                                     0x1202103
#define ixPSX80_PHY0_TX_TSTMARGDEEMPH_LANE2                                     0x1202203
#define ixPSX80_PHY0_TX_TSTMARGDEEMPH_LANE3                                     0x1202303
#define ixPSX80_PHY0_TX_TSTMARGDEEMPH_LANE4                                     0x1202403
#define ixPSX80_PHY0_TX_TSTMARGDEEMPH_LANE5                                     0x1202503
#define ixPSX80_PHY0_TX_TSTMARGDEEMPH_LANE6                                     0x1202603
#define ixPSX80_PHY0_TX_TSTMARGDEEMPH_LANE7                                     0x1202703
#define ixPSX80_PHY0_TX_MARGDEEMPHSTATUS_BROADCAST                              0x120ff04
#define ixPSX80_PHY0_TX_MARGDEEMPHSTATUS_LANE0                                  0x1202004
#define ixPSX80_PHY0_TX_MARGDEEMPHSTATUS_LANE1                                  0x1202104
#define ixPSX80_PHY0_TX_MARGDEEMPHSTATUS_LANE2                                  0x1202204
#define ixPSX80_PHY0_TX_MARGDEEMPHSTATUS_LANE3                                  0x1202304
#define ixPSX80_PHY0_TX_MARGDEEMPHSTATUS_LANE4                                  0x1202404
#define ixPSX80_PHY0_TX_MARGDEEMPHSTATUS_LANE5                                  0x1202504
#define ixPSX80_PHY0_TX_MARGDEEMPHSTATUS_LANE6                                  0x1202604
#define ixPSX80_PHY0_TX_MARGDEEMPHSTATUS_LANE7                                  0x1202704
#define ixPSX80_PHY0_TX_TXCNTRL_BROADCAST                                       0x120ff06
#define ixPSX80_PHY0_TX_TXCNTRL_LANE0                                           0x1202006
#define ixPSX80_PHY0_TX_TXCNTRL_LANE1                                           0x1202106
#define ixPSX80_PHY0_TX_TXCNTRL_LANE2                                           0x1202206
#define ixPSX80_PHY0_TX_TXCNTRL_LANE3                                           0x1202306
#define ixPSX80_PHY0_TX_TXCNTRL_LANE4                                           0x1202406
#define ixPSX80_PHY0_TX_TXCNTRL_LANE5                                           0x1202506
#define ixPSX80_PHY0_TX_TXCNTRL_LANE6                                           0x1202606
#define ixPSX80_PHY0_TX_TXCNTRL_LANE7                                           0x1202706
#define ixPSX80_PHY0_TX_CMD_BUS_GLOBAL_FOR_TX_BROADCAST                         0x120ff07
#define ixPSX80_PHY0_TX_CMD_BUS_GLOBAL_FOR_TX_LANE0                             0x1202007
#define ixPSX80_PHY0_TX_CMD_BUS_GLOBAL_FOR_TX_LANE1                             0x1202107
#define ixPSX80_PHY0_TX_CMD_BUS_GLOBAL_FOR_TX_LANE2                             0x1202207
#define ixPSX80_PHY0_TX_CMD_BUS_GLOBAL_FOR_TX_LANE3                             0x1202307
#define ixPSX80_PHY0_TX_CMD_BUS_GLOBAL_FOR_TX_LANE4                             0x1202407
#define ixPSX80_PHY0_TX_CMD_BUS_GLOBAL_FOR_TX_LANE5                             0x1202507
#define ixPSX80_PHY0_TX_CMD_BUS_GLOBAL_FOR_TX_LANE6                             0x1202607
#define ixPSX80_PHY0_TX_CMD_BUS_GLOBAL_FOR_TX_LANE7                             0x1202707
#define ixPSX80_PHY0_HTPLL_ROPLL_PowerDownEn                                    0x1204180
#define ixPSX80_PHY0_HTPLL_ROPLL_PciPllControlExt                               0x1204101
#define ixPSX80_PHY0_HTPLL_ROPLL_PciPllControl                                  0x1204102
#define ixPSX80_PHY0_HTPLL_ROPLL_PciPllTestDebug1                               0x1204103
#define ixPSX80_PHY0_HTPLL_ROPLL_PciPllTestDebug2                               0x1204104
#define ixPSX80_PHY0_HTPLL_ROPLL_PciPllFreqMode                                 0x1204105
#define ixPSX80_PHY0_HTPLL_ROPLL_PciPllUpdateCtrl                               0x1204108
#define ixPSX80_PHY0_HTPLL_ROPLL_PciPllTestDebug3                               0x1204109
#define ixPSX80_PHY0_HTPLL_ROPLL_PciFuseProcess                                 0x120410a
#define ixPSX80_PHY0_HTPLL_ROPLL_PciPllTestDebug4                               0x120410b
#define ixPSX80_PHY0_HTPLL_ROPLL_PciPllTestDebug5                               0x120410c
#define ixPSX80_PHY0_LCPLL_LCPLL_PowerDownEn                                    0x1204080
#define ixPSX80_PHY0_LCPLL_LCPLL_PciPllControlExt                               0x1204001
#define ixPSX80_PHY0_LCPLL_LCPLL_PciPllControl                                  0x1204002
#define ixPSX80_PHY0_LCPLL_LCPLL_PciPllTestDebug1                               0x1204003
#define ixPSX80_PHY0_LCPLL_LCPLL_PciPllTestDebug2                               0x1204004
#define ixPSX80_PHY0_LCPLL_LCPLL_PciPllFreqMode                                 0x1204005
#define ixPSX80_PHY0_LCPLL_LCPLL_PciLcVcoCtrl                                   0x1204007
#define ixPSX80_PHY0_LCPLL_LCPLL_PciPllUpdateCtrl                               0x1204008
#define ixPSX80_PHY0_LCPLL_LCPLL_PciPllTestDebug3                               0x1204009
#define ixPSX80_PHY0_LCPLL_LCPLL_PciPllTestDebug4                               0x120400b
#define ixPSX80_PHY0_LCPLL_LCPLL_PciPllTestDebug5                               0x120400c
#define ixPSX81_PHY0_COM_COMMON_FUSE1                                           0x1216200
#define ixPSX81_PHY0_COM_COMMON_FUSE2                                           0x1216201
#define ixPSX81_PHY0_COM_COMMON_FUSE3                                           0x1216202
#define ixPSX81_PHY0_COM_COMMON_ELECIDLE                                        0x1216204
#define ixPSX81_PHY0_COM_COMMON_DFX                                             0x1216205
#define ixPSX81_PHY0_COM_COMMON_MAR_DEEMPH_NOM                                  0x1216206
#define ixPSX81_PHY0_COM_COMMON_SELDEEMPH35                                     0x1216207
#define ixPSX81_PHY0_COM_COMMON_SELDEEMPH60                                     0x1216208
#define ixPSX81_PHY0_COM_COMMON_LANE_PWRMGMT                                    0x1216209
#define ixPSX81_PHY0_COM_COMMON_ADAPTCTL1                                       0x121620a
#define ixPSX81_PHY0_COM_COMMON_ADAPTCTL2                                       0x121620b
#define ixPSX81_PHY0_COM_COMMON_ADAPT_CFG_BYP_VAL                               0x121620c
#define ixPSX81_PHY0_COM_COMMON_ADAPT_CFG_BYP_VAL1                              0x121620d
#define ixPSX81_PHY0_COM_COMMON_ADAPT_DBG_BYP_VAL                               0x121620e
#define ixPSX81_PHY0_COM_COMMON_ADAPT_DBG_BYP_VAL1                              0x121620f
#define ixPSX81_PHY0_COM_COMMON_ADAPT_DBG1                                      0x1216210
#define ixPSX81_PHY0_COM_COMMON_LNCNTRL                                         0x1216211
#define ixPSX81_PHY0_COM_COMMON_TXTESTDEBUG                                     0x1216212
#define ixPSX81_PHY0_COM_COMMON_RXTESTDEBUG                                     0x1216213
#define ixPSX81_PHY0_COM_COMMON_CDR_PHCTL                                       0x1216214
#define ixPSX81_PHY0_COM_COMMON_CDR_FRCTL                                       0x1216215
#define ixPSX81_PHY0_RX_CMD_BUS_RX_CONTROL_BROADCAST                            0x121fe00
#define ixPSX81_PHY0_RX_CMD_BUS_RX_CONTROL_LANE0                                0x1210000
#define ixPSX81_PHY0_RX_CMD_BUS_RX_CONTROL_LANE1                                0x1210100
#define ixPSX81_PHY0_RX_CMD_BUS_RX_CONTROL_LANE2                                0x1210200
#define ixPSX81_PHY0_RX_CMD_BUS_RX_CONTROL_LANE3                                0x1210300
#define ixPSX81_PHY0_RX_CMD_BUS_RX_CONTROL_LANE4                                0x1210400
#define ixPSX81_PHY0_RX_CMD_BUS_RX_CONTROL_LANE5                                0x1210500
#define ixPSX81_PHY0_RX_CMD_BUS_RX_CONTROL_LANE6                                0x1210600
#define ixPSX81_PHY0_RX_CMD_BUS_RX_CONTROL_LANE7                                0x1210700
#define ixPSX81_PHY0_RX_CMD_BUS_GLOBAL_FOR_RX_BROADCAST                         0x121fe01
#define ixPSX81_PHY0_RX_CMD_BUS_GLOBAL_FOR_RX_LANE0                             0x1210001
#define ixPSX81_PHY0_RX_CMD_BUS_GLOBAL_FOR_RX_LANE1                             0x1210101
#define ixPSX81_PHY0_RX_CMD_BUS_GLOBAL_FOR_RX_LANE2                             0x1210201
#define ixPSX81_PHY0_RX_CMD_BUS_GLOBAL_FOR_RX_LANE3                             0x1210301
#define ixPSX81_PHY0_RX_CMD_BUS_GLOBAL_FOR_RX_LANE4                             0x1210401
#define ixPSX81_PHY0_RX_CMD_BUS_GLOBAL_FOR_RX_LANE5                             0x1210501
#define ixPSX81_PHY0_RX_CMD_BUS_GLOBAL_FOR_RX_LANE6                             0x1210601
#define ixPSX81_PHY0_RX_CMD_BUS_GLOBAL_FOR_RX_LANE7                             0x1210701
#define ixPSX81_PHY0_RX_RX_CTL_BROADCAST                                        0x121fe02
#define ixPSX81_PHY0_RX_RX_CTL_LANE0                                            0x1210002
#define ixPSX81_PHY0_RX_RX_CTL_LANE1                                            0x1210102
#define ixPSX81_PHY0_RX_RX_CTL_LANE2                                            0x1210202
#define ixPSX81_PHY0_RX_RX_CTL_LANE3                                            0x1210302
#define ixPSX81_PHY0_RX_RX_CTL_LANE4                                            0x1210402
#define ixPSX81_PHY0_RX_RX_CTL_LANE5                                            0x1210502
#define ixPSX81_PHY0_RX_RX_CTL_LANE6                                            0x1210602
#define ixPSX81_PHY0_RX_RX_CTL_LANE7                                            0x1210702
#define ixPSX81_PHY0_RX_DLL_CTL_BROADCAST                                       0x121fe03
#define ixPSX81_PHY0_RX_DLL_CTL_LANE0                                           0x1210003
#define ixPSX81_PHY0_RX_DLL_CTL_LANE1                                           0x1210103
#define ixPSX81_PHY0_RX_DLL_CTL_LANE2                                           0x1210203
#define ixPSX81_PHY0_RX_DLL_CTL_LANE3                                           0x1210303
#define ixPSX81_PHY0_RX_DLL_CTL_LANE4                                           0x1210403
#define ixPSX81_PHY0_RX_DLL_CTL_LANE5                                           0x1210503
#define ixPSX81_PHY0_RX_DLL_CTL_LANE6                                           0x1210603
#define ixPSX81_PHY0_RX_DLL_CTL_LANE7                                           0x1210703
#define ixPSX81_PHY0_RX_RXTEST_REGS_BROADCAST                                   0x121fe04
#define ixPSX81_PHY0_RX_RXTEST_REGS_LANE0                                       0x1210004
#define ixPSX81_PHY0_RX_RXTEST_REGS_LANE1                                       0x1210104
#define ixPSX81_PHY0_RX_RXTEST_REGS_LANE2                                       0x1210204
#define ixPSX81_PHY0_RX_RXTEST_REGS_LANE3                                       0x1210304
#define ixPSX81_PHY0_RX_RXTEST_REGS_LANE4                                       0x1210404
#define ixPSX81_PHY0_RX_RXTEST_REGS_LANE5                                       0x1210504
#define ixPSX81_PHY0_RX_RXTEST_REGS_LANE6                                       0x1210604
#define ixPSX81_PHY0_RX_RXTEST_REGS_LANE7                                       0x1210704
#define ixPSX81_PHY0_RX_ELECIDLE_DEBUG_BROADCAST                                0x121fe05
#define ixPSX81_PHY0_RX_ELECIDLE_DEBUG_LANE0                                    0x1210005
#define ixPSX81_PHY0_RX_ELECIDLE_DEBUG_LANE1                                    0x1210105
#define ixPSX81_PHY0_RX_ELECIDLE_DEBUG_LANE2                                    0x1210205
#define ixPSX81_PHY0_RX_ELECIDLE_DEBUG_LANE3                                    0x1210305
#define ixPSX81_PHY0_RX_ELECIDLE_DEBUG_LANE4                                    0x1210405
#define ixPSX81_PHY0_RX_ELECIDLE_DEBUG_LANE5                                    0x1210505
#define ixPSX81_PHY0_RX_ELECIDLE_DEBUG_LANE6                                    0x1210605
#define ixPSX81_PHY0_RX_ELECIDLE_DEBUG_LANE7                                    0x1210705
#define ixPSX81_PHY0_RX_ADAPTCTL_BROADCAST                                      0x121fe0a
#define ixPSX81_PHY0_RX_ADAPTCTL_LANE0                                          0x121000a
#define ixPSX81_PHY0_RX_ADAPTCTL_LANE1                                          0x121010a
#define ixPSX81_PHY0_RX_ADAPTCTL_LANE2                                          0x121020a
#define ixPSX81_PHY0_RX_ADAPTCTL_LANE3                                          0x121030a
#define ixPSX81_PHY0_RX_ADAPTCTL_LANE4                                          0x121040a
#define ixPSX81_PHY0_RX_ADAPTCTL_LANE5                                          0x121050a
#define ixPSX81_PHY0_RX_ADAPTCTL_LANE6                                          0x121060a
#define ixPSX81_PHY0_RX_ADAPTCTL_LANE7                                          0x121070a
#define ixPSX81_PHY0_RX_FOMCALCCTL_BROADCAST                                    0x121fe0b
#define ixPSX81_PHY0_RX_FOMCALCCTL_LANE0                                        0x121000b
#define ixPSX81_PHY0_RX_FOMCALCCTL_LANE1                                        0x121010b
#define ixPSX81_PHY0_RX_FOMCALCCTL_LANE2                                        0x121020b
#define ixPSX81_PHY0_RX_FOMCALCCTL_LANE3                                        0x121030b
#define ixPSX81_PHY0_RX_FOMCALCCTL_LANE4                                        0x121040b
#define ixPSX81_PHY0_RX_FOMCALCCTL_LANE5                                        0x121050b
#define ixPSX81_PHY0_RX_FOMCALCCTL_LANE6                                        0x121060b
#define ixPSX81_PHY0_RX_FOMCALCCTL_LANE7                                        0x121070b
#define ixPSX81_PHY0_RX_ADAPT_CFG_BYP_EN_BROADCAST                              0x121fe0c
#define ixPSX81_PHY0_RX_ADAPT_CFG_BYP_EN_LANE0                                  0x121000c
#define ixPSX81_PHY0_RX_ADAPT_CFG_BYP_EN_LANE1                                  0x121010c
#define ixPSX81_PHY0_RX_ADAPT_CFG_BYP_EN_LANE2                                  0x121020c
#define ixPSX81_PHY0_RX_ADAPT_CFG_BYP_EN_LANE3                                  0x121030c
#define ixPSX81_PHY0_RX_ADAPT_CFG_BYP_EN_LANE4                                  0x121040c
#define ixPSX81_PHY0_RX_ADAPT_CFG_BYP_EN_LANE5                                  0x121050c
#define ixPSX81_PHY0_RX_ADAPT_CFG_BYP_EN_LANE6                                  0x121060c
#define ixPSX81_PHY0_RX_ADAPT_CFG_BYP_EN_LANE7                                  0x121070c
#define ixPSX81_PHY0_RX_DBG_BYP_EN_BROADCAST                                    0x121fe0d
#define ixPSX81_PHY0_RX_DBG_BYP_EN_LANE0                                        0x121000d
#define ixPSX81_PHY0_RX_DBG_BYP_EN_LANE1                                        0x121010d
#define ixPSX81_PHY0_RX_DBG_BYP_EN_LANE2                                        0x121020d
#define ixPSX81_PHY0_RX_DBG_BYP_EN_LANE3                                        0x121030d
#define ixPSX81_PHY0_RX_DBG_BYP_EN_LANE4                                        0x121040d
#define ixPSX81_PHY0_RX_DBG_BYP_EN_LANE5                                        0x121050d
#define ixPSX81_PHY0_RX_DBG_BYP_EN_LANE6                                        0x121060d
#define ixPSX81_PHY0_RX_DBG_BYP_EN_LANE7                                        0x121070d
#define ixPSX81_PHY0_RX_ADAPTDBG1_BROADCAST                                     0x121fe0e
#define ixPSX81_PHY0_RX_ADAPTDBG1_LANE0                                         0x121000e
#define ixPSX81_PHY0_RX_ADAPTDBG1_LANE1                                         0x121010e
#define ixPSX81_PHY0_RX_ADAPTDBG1_LANE2                                         0x121020e
#define ixPSX81_PHY0_RX_ADAPTDBG1_LANE3                                         0x121030e
#define ixPSX81_PHY0_RX_ADAPTDBG1_LANE4                                         0x121040e
#define ixPSX81_PHY0_RX_ADAPTDBG1_LANE5                                         0x121050e
#define ixPSX81_PHY0_RX_ADAPTDBG1_LANE6                                         0x121060e
#define ixPSX81_PHY0_RX_ADAPTDBG1_LANE7                                         0x121070e
#define ixPSX81_PHY0_TX_CMD_BUS_TX_CONTROL_BROADCAST                            0x121ff00
#define ixPSX81_PHY0_TX_CMD_BUS_TX_CONTROL_LANE0                                0x1212000
#define ixPSX81_PHY0_TX_CMD_BUS_TX_CONTROL_LANE1                                0x1212100
#define ixPSX81_PHY0_TX_CMD_BUS_TX_CONTROL_LANE2                                0x1212200
#define ixPSX81_PHY0_TX_CMD_BUS_TX_CONTROL_LANE3                                0x1212300
#define ixPSX81_PHY0_TX_CMD_BUS_TX_CONTROL_LANE4                                0x1212400
#define ixPSX81_PHY0_TX_CMD_BUS_TX_CONTROL_LANE5                                0x1212500
#define ixPSX81_PHY0_TX_CMD_BUS_TX_CONTROL_LANE6                                0x1212600
#define ixPSX81_PHY0_TX_CMD_BUS_TX_CONTROL_LANE7                                0x1212700
#define ixPSX81_PHY0_TX_DFX_BROADCAST                                           0x121ff01
#define ixPSX81_PHY0_TX_DFX_LANE0                                               0x1212001
#define ixPSX81_PHY0_TX_DFX_LANE1                                               0x1212101
#define ixPSX81_PHY0_TX_DFX_LANE2                                               0x1212201
#define ixPSX81_PHY0_TX_DFX_LANE3                                               0x1212301
#define ixPSX81_PHY0_TX_DFX_LANE4                                               0x1212401
#define ixPSX81_PHY0_TX_DFX_LANE5                                               0x1212501
#define ixPSX81_PHY0_TX_DFX_LANE6                                               0x1212601
#define ixPSX81_PHY0_TX_DFX_LANE7                                               0x1212701
#define ixPSX81_PHY0_TX_DEEMPH_BROADCAST                                        0x121ff02
#define ixPSX81_PHY0_TX_DEEMPH_LANE0                                            0x1212002
#define ixPSX81_PHY0_TX_DEEMPH_LANE1                                            0x1212102
#define ixPSX81_PHY0_TX_DEEMPH_LANE2                                            0x1212202
#define ixPSX81_PHY0_TX_DEEMPH_LANE3                                            0x1212302
#define ixPSX81_PHY0_TX_DEEMPH_LANE4                                            0x1212402
#define ixPSX81_PHY0_TX_DEEMPH_LANE5                                            0x1212502
#define ixPSX81_PHY0_TX_DEEMPH_LANE6                                            0x1212602
#define ixPSX81_PHY0_TX_DEEMPH_LANE7                                            0x1212702
#define ixPSX81_PHY0_TX_TSTMARGDEEMPH_BROADCAST                                 0x121ff03
#define ixPSX81_PHY0_TX_TSTMARGDEEMPH_LANE0                                     0x1212003
#define ixPSX81_PHY0_TX_TSTMARGDEEMPH_LANE1                                     0x1212103
#define ixPSX81_PHY0_TX_TSTMARGDEEMPH_LANE2                                     0x1212203
#define ixPSX81_PHY0_TX_TSTMARGDEEMPH_LANE3                                     0x1212303
#define ixPSX81_PHY0_TX_TSTMARGDEEMPH_LANE4                                     0x1212403
#define ixPSX81_PHY0_TX_TSTMARGDEEMPH_LANE5                                     0x1212503
#define ixPSX81_PHY0_TX_TSTMARGDEEMPH_LANE6                                     0x1212603
#define ixPSX81_PHY0_TX_TSTMARGDEEMPH_LANE7                                     0x1212703
#define ixPSX81_PHY0_TX_MARGDEEMPHSTATUS_BROADCAST                              0x121ff04
#define ixPSX81_PHY0_TX_MARGDEEMPHSTATUS_LANE0                                  0x1212004
#define ixPSX81_PHY0_TX_MARGDEEMPHSTATUS_LANE1                                  0x1212104
#define ixPSX81_PHY0_TX_MARGDEEMPHSTATUS_LANE2                                  0x1212204
#define ixPSX81_PHY0_TX_MARGDEEMPHSTATUS_LANE3                                  0x1212304
#define ixPSX81_PHY0_TX_MARGDEEMPHSTATUS_LANE4                                  0x1212404
#define ixPSX81_PHY0_TX_MARGDEEMPHSTATUS_LANE5                                  0x1212504
#define ixPSX81_PHY0_TX_MARGDEEMPHSTATUS_LANE6                                  0x1212604
#define ixPSX81_PHY0_TX_MARGDEEMPHSTATUS_LANE7                                  0x1212704
#define ixPSX81_PHY0_TX_TXCNTRL_BROADCAST                                       0x121ff06
#define ixPSX81_PHY0_TX_TXCNTRL_LANE0                                           0x1212006
#define ixPSX81_PHY0_TX_TXCNTRL_LANE1                                           0x1212106
#define ixPSX81_PHY0_TX_TXCNTRL_LANE2                                           0x1212206
#define ixPSX81_PHY0_TX_TXCNTRL_LANE3                                           0x1212306
#define ixPSX81_PHY0_TX_TXCNTRL_LANE4                                           0x1212406
#define ixPSX81_PHY0_TX_TXCNTRL_LANE5                                           0x1212506
#define ixPSX81_PHY0_TX_TXCNTRL_LANE6                                           0x1212606
#define ixPSX81_PHY0_TX_TXCNTRL_LANE7                                           0x1212706
#define ixPSX81_PHY0_TX_CMD_BUS_GLOBAL_FOR_TX_BROADCAST                         0x121ff07
#define ixPSX81_PHY0_TX_CMD_BUS_GLOBAL_FOR_TX_LANE0                             0x1212007
#define ixPSX81_PHY0_TX_CMD_BUS_GLOBAL_FOR_TX_LANE1                             0x1212107
#define ixPSX81_PHY0_TX_CMD_BUS_GLOBAL_FOR_TX_LANE2                             0x1212207
#define ixPSX81_PHY0_TX_CMD_BUS_GLOBAL_FOR_TX_LANE3                             0x1212307
#define ixPSX81_PHY0_TX_CMD_BUS_GLOBAL_FOR_TX_LANE4                             0x1212407
#define ixPSX81_PHY0_TX_CMD_BUS_GLOBAL_FOR_TX_LANE5                             0x1212507
#define ixPSX81_PHY0_TX_CMD_BUS_GLOBAL_FOR_TX_LANE6                             0x1212607
#define ixPSX81_PHY0_TX_CMD_BUS_GLOBAL_FOR_TX_LANE7                             0x1212707
#define ixPSX81_PHY0_HTPLL_ROPLL_PowerDownEn                                    0x1214180
#define ixPSX81_PHY0_HTPLL_ROPLL_PciPllControlExt                               0x1214101
#define ixPSX81_PHY0_HTPLL_ROPLL_PciPllControl                                  0x1214102
#define ixPSX81_PHY0_HTPLL_ROPLL_PciPllTestDebug1                               0x1214103
#define ixPSX81_PHY0_HTPLL_ROPLL_PciPllTestDebug2                               0x1214104
#define ixPSX81_PHY0_HTPLL_ROPLL_PciPllFreqMode                                 0x1214105
#define ixPSX81_PHY0_HTPLL_ROPLL_PciPllUpdateCtrl                               0x1214108
#define ixPSX81_PHY0_HTPLL_ROPLL_PciPllTestDebug3                               0x1214109
#define ixPSX81_PHY0_HTPLL_ROPLL_PciFuseProcess                                 0x121410a
#define ixPSX81_PHY0_HTPLL_ROPLL_PciPllTestDebug4                               0x121410b
#define ixPSX81_PHY0_HTPLL_ROPLL_PciPllTestDebug5                               0x121410c
#define ixPSX81_PHY0_LCPLL_LCPLL_PowerDownEn                                    0x1214080
#define ixPSX81_PHY0_LCPLL_LCPLL_PciPllControlExt                               0x1214001
#define ixPSX81_PHY0_LCPLL_LCPLL_PciPllControl                                  0x1214002
#define ixPSX81_PHY0_LCPLL_LCPLL_PciPllTestDebug1                               0x1214003
#define ixPSX81_PHY0_LCPLL_LCPLL_PciPllTestDebug2                               0x1214004
#define ixPSX81_PHY0_LCPLL_LCPLL_PciPllFreqMode                                 0x1214005
#define ixPSX81_PHY0_LCPLL_LCPLL_PciLcVcoCtrl                                   0x1214007
#define ixPSX81_PHY0_LCPLL_LCPLL_PciPllUpdateCtrl                               0x1214008
#define ixPSX81_PHY0_LCPLL_LCPLL_PciPllTestDebug3                               0x1214009
#define ixPSX81_PHY0_LCPLL_LCPLL_PciPllTestDebug4                               0x121400b
#define ixPSX81_PHY0_LCPLL_LCPLL_PciPllTestDebug5                               0x121400c
#define ixPSX80_PIF0_SCRATCH                                                    0x1100001
#define ixPSX80_PIF0_HW_DEBUG                                                   0x1100002
#define ixPSX80_PIF0_STRAP_0                                                    0x1100003
#define ixPSX80_PIF0_CTRL                                                       0x1100004
#define ixPSX80_PIF0_TX_CTRL                                                    0x1100008
#define ixPSX80_PIF0_TX_CTRL2                                                   0x1100009
#define ixPSX80_PIF0_RX_CTRL                                                    0x110000a
#define ixPSX80_PIF0_RX_CTRL2                                                   0x110000b
#define ixPSX80_PIF0_GLB_OVRD                                                   0x110000c
#define ixPSX80_PIF0_GLB_OVRD2                                                  0x110000d
#define ixPSX80_PIF0_BIF_CMD_STATUS                                             0x1100010
#define ixPSX80_PIF0_CMD_BUS_CTRL                                               0x1100011
#define ixPSX80_PIF0_CMD_BUS_GLB_OVRD                                           0x1100013
#define ixPSX80_PIF0_LANE0_OVRD                                                 0x1100014
#define ixPSX80_PIF0_LANE0_OVRD2                                                0x1100015
#define ixPSX80_PIF0_LANE1_OVRD                                                 0x1100016
#define ixPSX80_PIF0_LANE1_OVRD2                                                0x1100017
#define ixPSX80_PIF0_LANE2_OVRD                                                 0x1100018
#define ixPSX80_PIF0_LANE2_OVRD2                                                0x1100019
#define ixPSX80_PIF0_LANE3_OVRD                                                 0x110001a
#define ixPSX80_PIF0_LANE3_OVRD2                                                0x110001b
#define ixPSX80_PIF0_LANE4_OVRD                                                 0x110001c
#define ixPSX80_PIF0_LANE4_OVRD2                                                0x110001d
#define ixPSX80_PIF0_LANE5_OVRD                                                 0x110001e
#define ixPSX80_PIF0_LANE5_OVRD2                                                0x110001f
#define ixPSX80_PIF0_LANE6_OVRD                                                 0x1100020
#define ixPSX80_PIF0_LANE6_OVRD2                                                0x1100021
#define ixPSX80_PIF0_LANE7_OVRD                                                 0x1100022
#define ixPSX80_PIF0_LANE7_OVRD2                                                0x1100023
#define ixPSX81_PIF0_SCRATCH                                                    0x1110001
#define ixPSX81_PIF0_HW_DEBUG                                                   0x1110002
#define ixPSX81_PIF0_STRAP_0                                                    0x1110003
#define ixPSX81_PIF0_CTRL                                                       0x1110004
#define ixPSX81_PIF0_TX_CTRL                                                    0x1110008
#define ixPSX81_PIF0_TX_CTRL2                                                   0x1110009
#define ixPSX81_PIF0_RX_CTRL                                                    0x111000a
#define ixPSX81_PIF0_RX_CTRL2                                                   0x111000b
#define ixPSX81_PIF0_GLB_OVRD                                                   0x111000c
#define ixPSX81_PIF0_GLB_OVRD2                                                  0x111000d
#define ixPSX81_PIF0_BIF_CMD_STATUS                                             0x1110010
#define ixPSX81_PIF0_CMD_BUS_CTRL                                               0x1110011
#define ixPSX81_PIF0_CMD_BUS_GLB_OVRD                                           0x1110013
#define ixPSX81_PIF0_LANE0_OVRD                                                 0x1110014
#define ixPSX81_PIF0_LANE0_OVRD2                                                0x1110015
#define ixPSX81_PIF0_LANE1_OVRD                                                 0x1110016
#define ixPSX81_PIF0_LANE1_OVRD2                                                0x1110017
#define ixPSX81_PIF0_LANE2_OVRD                                                 0x1110018
#define ixPSX81_PIF0_LANE2_OVRD2                                                0x1110019
#define ixPSX81_PIF0_LANE3_OVRD                                                 0x111001a
#define ixPSX81_PIF0_LANE3_OVRD2                                                0x111001b
#define ixPSX81_PIF0_LANE4_OVRD                                                 0x111001c
#define ixPSX81_PIF0_LANE4_OVRD2                                                0x111001d
#define ixPSX81_PIF0_LANE5_OVRD                                                 0x111001e
#define ixPSX81_PIF0_LANE5_OVRD2                                                0x111001f
#define ixPSX81_PIF0_LANE6_OVRD                                                 0x1110020
#define ixPSX81_PIF0_LANE6_OVRD2                                                0x1110021
#define ixPSX81_PIF0_LANE7_OVRD                                                 0x1110022
#define ixPSX81_PIF0_LANE7_OVRD2                                                0x1110023

#endif /* BIF_5_1_D_H */
