/*
 * Copyright (C) 2019  Advanced Micro Devices, Inc.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a
 * copy of this software and associated documentation files (the "Software"),
 * to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included
 * in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
 * THE COPYRIGHT HOLDER(S) BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN
 * AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
#ifndef _athub_2_1_0_OFFSET_HEADER
#define _athub_2_1_0_OFFSET_HEADER



// addressBlock: athub_atsdec
// base address: 0x3000
#define mmATHUB_ATS_MODE_CNTL                                                                          0x0000
#define mmATHUB_ATS_MODE_CNTL_BASE_IDX                                                                 0
#define mmATHUB_SHARED_VIRT_RESET_REQ                                                                  0x0001
#define mmATHUB_SHARED_VIRT_RESET_REQ_BASE_IDX                                                         0
#define mmATHUB_SHARED_ACTIVE_FCN_ID                                                                   0x0002
#define mmATHUB_SHARED_ACTIVE_FCN_ID_BASE_IDX                                                          0
#define mmATC_ATS_CNTL                                                                                 0x0003
#define mmATC_ATS_CNTL_BASE_IDX                                                                        0
#define mmATC_ATS_FAULT_CNTL                                                                           0x0006
#define mmATC_ATS_FAULT_CNTL_BASE_IDX                                                                  0
#define mmATC_ATS_DEFAULT_PAGE_LOW                                                                     0x0007
#define mmATC_ATS_DEFAULT_PAGE_LOW_BASE_IDX                                                            0
#define mmATC_TRANS_FAULT_RSPCNTRL                                                                     0x0008
#define mmATC_TRANS_FAULT_RSPCNTRL_BASE_IDX                                                            0
#define mmATHUB_MISC_CNTL                                                                              0x0009
#define mmATHUB_MISC_CNTL_BASE_IDX                                                                     0
#define mmATHUB_MEM_POWER_LS                                                                           0x000a
#define mmATHUB_MEM_POWER_LS_BASE_IDX                                                                  0
#define mmATC_ATS_SDPPORT_CNTL                                                                         0x000b
#define mmATC_ATS_SDPPORT_CNTL_BASE_IDX                                                                0
#define mmATC_ATS_CNTL2                                                                                0x000d
#define mmATC_ATS_CNTL2_BASE_IDX                                                                       0
#define mmATC_ATS_TR_QOS_CNTL                                                                          0x000e
#define mmATC_ATS_TR_QOS_CNTL_BASE_IDX                                                                 0
#define mmATC_ATS_MISC_CNTL                                                                            0x000f
#define mmATC_ATS_MISC_CNTL_BASE_IDX                                                                   0
#define mmATC_PERFCOUNTER0_CFG                                                                         0x0010
#define mmATC_PERFCOUNTER0_CFG_BASE_IDX                                                                0
#define mmATC_PERFCOUNTER1_CFG                                                                         0x0011
#define mmATC_PERFCOUNTER1_CFG_BASE_IDX                                                                0
#define mmATC_PERFCOUNTER2_CFG                                                                         0x0012
#define mmATC_PERFCOUNTER2_CFG_BASE_IDX                                                                0
#define mmATC_PERFCOUNTER3_CFG                                                                         0x0013
#define mmATC_PERFCOUNTER3_CFG_BASE_IDX                                                                0
#define mmATC_PERFCOUNTER_RSLT_CNTL                                                                    0x0014
#define mmATC_PERFCOUNTER_RSLT_CNTL_BASE_IDX                                                           0
#define mmATC_PERFCOUNTER_LO                                                                           0x0015
#define mmATC_PERFCOUNTER_LO_BASE_IDX                                                                  0
#define mmATC_PERFCOUNTER_HI                                                                           0x0016
#define mmATC_PERFCOUNTER_HI_BASE_IDX                                                                  0
#define mmATS_IH_CREDIT                                                                                0x0017
#define mmATS_IH_CREDIT_BASE_IDX                                                                       0
#define mmATHUB_IH_CREDIT                                                                              0x0018
#define mmATHUB_IH_CREDIT_BASE_IDX                                                                     0
#define mmATC_ATS_GFX_ATCL2_STATUS                                                                     0x0019
#define mmATC_ATS_GFX_ATCL2_STATUS_BASE_IDX                                                            0
#define mmATC_ATS_MMHUB_ATCL2_STATUS                                                                   0x001a
#define mmATC_ATS_MMHUB_ATCL2_STATUS_BASE_IDX                                                          0
#define mmATC_ATS_FAULT_STATUS_INFO                                                                    0x001b
#define mmATC_ATS_FAULT_STATUS_INFO_BASE_IDX                                                           0
#define mmATC_ATS_FAULT_STATUS_ADDR                                                                    0x001c
#define mmATC_ATS_FAULT_STATUS_ADDR_BASE_IDX                                                           0
#define mmATC_ATS_FAULT_STATUS_INFO2                                                                   0x001d
#define mmATC_ATS_FAULT_STATUS_INFO2_BASE_IDX                                                          0
#define mmATHUB_PCIE_ATS_CNTL                                                                          0x001e
#define mmATHUB_PCIE_ATS_CNTL_BASE_IDX                                                                 0
#define mmATHUB_PCIE_PASID_CNTL                                                                        0x001f
#define mmATHUB_PCIE_PASID_CNTL_BASE_IDX                                                               0
#define mmATHUB_PCIE_PAGE_REQ_CNTL                                                                     0x0020
#define mmATHUB_PCIE_PAGE_REQ_CNTL_BASE_IDX                                                            0
#define mmATHUB_PCIE_OUTSTAND_PAGE_REQ_ALLOC                                                           0x0021
#define mmATHUB_PCIE_OUTSTAND_PAGE_REQ_ALLOC_BASE_IDX                                                  0
#define mmATHUB_COMMAND                                                                                0x0022
#define mmATHUB_COMMAND_BASE_IDX                                                                       0
#define mmATHUB_PCIE_ATS_CNTL_VF_0                                                                     0x0023
#define mmATHUB_PCIE_ATS_CNTL_VF_0_BASE_IDX                                                            0
#define mmATHUB_PCIE_ATS_CNTL_VF_1                                                                     0x0024
#define mmATHUB_PCIE_ATS_CNTL_VF_1_BASE_IDX                                                            0
#define mmATHUB_PCIE_ATS_CNTL_VF_2                                                                     0x0025
#define mmATHUB_PCIE_ATS_CNTL_VF_2_BASE_IDX                                                            0
#define mmATHUB_PCIE_ATS_CNTL_VF_3                                                                     0x0026
#define mmATHUB_PCIE_ATS_CNTL_VF_3_BASE_IDX                                                            0
#define mmATHUB_PCIE_ATS_CNTL_VF_4                                                                     0x0027
#define mmATHUB_PCIE_ATS_CNTL_VF_4_BASE_IDX                                                            0
#define mmATHUB_PCIE_ATS_CNTL_VF_5                                                                     0x0028
#define mmATHUB_PCIE_ATS_CNTL_VF_5_BASE_IDX                                                            0
#define mmATHUB_PCIE_ATS_CNTL_VF_6                                                                     0x0029
#define mmATHUB_PCIE_ATS_CNTL_VF_6_BASE_IDX                                                            0
#define mmATHUB_PCIE_ATS_CNTL_VF_7                                                                     0x002a
#define mmATHUB_PCIE_ATS_CNTL_VF_7_BASE_IDX                                                            0
#define mmATHUB_PCIE_ATS_CNTL_VF_8                                                                     0x002b
#define mmATHUB_PCIE_ATS_CNTL_VF_8_BASE_IDX                                                            0
#define mmATHUB_PCIE_ATS_CNTL_VF_9                                                                     0x002c
#define mmATHUB_PCIE_ATS_CNTL_VF_9_BASE_IDX                                                            0
#define mmATHUB_PCIE_ATS_CNTL_VF_10                                                                    0x002d
#define mmATHUB_PCIE_ATS_CNTL_VF_10_BASE_IDX                                                           0
#define mmATHUB_PCIE_ATS_CNTL_VF_11                                                                    0x002e
#define mmATHUB_PCIE_ATS_CNTL_VF_11_BASE_IDX                                                           0
#define mmATHUB_PCIE_ATS_CNTL_VF_12                                                                    0x002f
#define mmATHUB_PCIE_ATS_CNTL_VF_12_BASE_IDX                                                           0
#define mmATHUB_PCIE_ATS_CNTL_VF_13                                                                    0x0030
#define mmATHUB_PCIE_ATS_CNTL_VF_13_BASE_IDX                                                           0
#define mmATHUB_PCIE_ATS_CNTL_VF_14                                                                    0x0031
#define mmATHUB_PCIE_ATS_CNTL_VF_14_BASE_IDX                                                           0
#define mmATHUB_PCIE_ATS_CNTL_VF_15                                                                    0x0032
#define mmATHUB_PCIE_ATS_CNTL_VF_15_BASE_IDX                                                           0
#define mmATHUB_PCIE_ATS_CNTL_VF_16                                                                    0x0033
#define mmATHUB_PCIE_ATS_CNTL_VF_16_BASE_IDX                                                           0
#define mmATHUB_PCIE_ATS_CNTL_VF_17                                                                    0x0034
#define mmATHUB_PCIE_ATS_CNTL_VF_17_BASE_IDX                                                           0
#define mmATHUB_PCIE_ATS_CNTL_VF_18                                                                    0x0035
#define mmATHUB_PCIE_ATS_CNTL_VF_18_BASE_IDX                                                           0
#define mmATHUB_PCIE_ATS_CNTL_VF_19                                                                    0x0036
#define mmATHUB_PCIE_ATS_CNTL_VF_19_BASE_IDX                                                           0
#define mmATHUB_PCIE_ATS_CNTL_VF_20                                                                    0x0037
#define mmATHUB_PCIE_ATS_CNTL_VF_20_BASE_IDX                                                           0
#define mmATHUB_PCIE_ATS_CNTL_VF_21                                                                    0x0038
#define mmATHUB_PCIE_ATS_CNTL_VF_21_BASE_IDX                                                           0
#define mmATHUB_PCIE_ATS_CNTL_VF_22                                                                    0x0039
#define mmATHUB_PCIE_ATS_CNTL_VF_22_BASE_IDX                                                           0
#define mmATHUB_PCIE_ATS_CNTL_VF_23                                                                    0x003a
#define mmATHUB_PCIE_ATS_CNTL_VF_23_BASE_IDX                                                           0
#define mmATHUB_PCIE_ATS_CNTL_VF_24                                                                    0x003b
#define mmATHUB_PCIE_ATS_CNTL_VF_24_BASE_IDX                                                           0
#define mmATHUB_PCIE_ATS_CNTL_VF_25                                                                    0x003c
#define mmATHUB_PCIE_ATS_CNTL_VF_25_BASE_IDX                                                           0
#define mmATHUB_PCIE_ATS_CNTL_VF_26                                                                    0x003d
#define mmATHUB_PCIE_ATS_CNTL_VF_26_BASE_IDX                                                           0
#define mmATHUB_PCIE_ATS_CNTL_VF_27                                                                    0x003e
#define mmATHUB_PCIE_ATS_CNTL_VF_27_BASE_IDX                                                           0
#define mmATHUB_PCIE_ATS_CNTL_VF_28                                                                    0x003f
#define mmATHUB_PCIE_ATS_CNTL_VF_28_BASE_IDX                                                           0
#define mmATHUB_PCIE_ATS_CNTL_VF_29                                                                    0x0040
#define mmATHUB_PCIE_ATS_CNTL_VF_29_BASE_IDX                                                           0
#define mmATHUB_PCIE_ATS_CNTL_VF_30                                                                    0x0041
#define mmATHUB_PCIE_ATS_CNTL_VF_30_BASE_IDX                                                           0
#define mmATC_VMID_PASID_MAPPING_UPDATE_STATUS                                                         0x0042
#define mmATC_VMID_PASID_MAPPING_UPDATE_STATUS_BASE_IDX                                                0
#define mmATC_ATS_VMID_STATUS                                                                          0x0043
#define mmATC_ATS_VMID_STATUS_BASE_IDX                                                                 0
#define mmATC_ATS_STATUS                                                                               0x0044
#define mmATC_ATS_STATUS_BASE_IDX                                                                      0
#define mmATC_ATS_VMID_SNAPSHOT_GFX_STAT                                                               0x0045
#define mmATC_ATS_VMID_SNAPSHOT_GFX_STAT_BASE_IDX                                                      0
#define mmATC_ATS_VMID_SNAPSHOT_MMHUB_STAT                                                             0x0046
#define mmATC_ATS_VMID_SNAPSHOT_MMHUB_STAT_BASE_IDX                                                    0
#define mmATC_VMID0_PASID_MAPPING                                                                      0x0047
#define mmATC_VMID0_PASID_MAPPING_BASE_IDX                                                             0
#define mmATC_VMID1_PASID_MAPPING                                                                      0x0048
#define mmATC_VMID1_PASID_MAPPING_BASE_IDX                                                             0
#define mmATC_VMID2_PASID_MAPPING                                                                      0x0049
#define mmATC_VMID2_PASID_MAPPING_BASE_IDX                                                             0
#define mmATC_VMID3_PASID_MAPPING                                                                      0x004a
#define mmATC_VMID3_PASID_MAPPING_BASE_IDX                                                             0
#define mmATC_VMID4_PASID_MAPPING                                                                      0x004b
#define mmATC_VMID4_PASID_MAPPING_BASE_IDX                                                             0
#define mmATC_VMID5_PASID_MAPPING                                                                      0x004c
#define mmATC_VMID5_PASID_MAPPING_BASE_IDX                                                             0
#define mmATC_VMID6_PASID_MAPPING                                                                      0x004d
#define mmATC_VMID6_PASID_MAPPING_BASE_IDX                                                             0
#define mmATC_VMID7_PASID_MAPPING                                                                      0x004e
#define mmATC_VMID7_PASID_MAPPING_BASE_IDX                                                             0
#define mmATC_VMID8_PASID_MAPPING                                                                      0x004f
#define mmATC_VMID8_PASID_MAPPING_BASE_IDX                                                             0
#define mmATC_VMID9_PASID_MAPPING                                                                      0x0050
#define mmATC_VMID9_PASID_MAPPING_BASE_IDX                                                             0
#define mmATC_VMID10_PASID_MAPPING                                                                     0x0051
#define mmATC_VMID10_PASID_MAPPING_BASE_IDX                                                            0
#define mmATC_VMID11_PASID_MAPPING                                                                     0x0052
#define mmATC_VMID11_PASID_MAPPING_BASE_IDX                                                            0
#define mmATC_VMID12_PASID_MAPPING                                                                     0x0053
#define mmATC_VMID12_PASID_MAPPING_BASE_IDX                                                            0
#define mmATC_VMID13_PASID_MAPPING                                                                     0x0054
#define mmATC_VMID13_PASID_MAPPING_BASE_IDX                                                            0
#define mmATC_VMID14_PASID_MAPPING                                                                     0x0055
#define mmATC_VMID14_PASID_MAPPING_BASE_IDX                                                            0
#define mmATC_VMID15_PASID_MAPPING                                                                     0x0056
#define mmATC_VMID15_PASID_MAPPING_BASE_IDX                                                            0
#define mmATC_VMID16_PASID_MAPPING                                                                     0x0057
#define mmATC_VMID16_PASID_MAPPING_BASE_IDX                                                            0
#define mmATC_VMID17_PASID_MAPPING                                                                     0x0058
#define mmATC_VMID17_PASID_MAPPING_BASE_IDX                                                            0
#define mmATC_VMID18_PASID_MAPPING                                                                     0x0059
#define mmATC_VMID18_PASID_MAPPING_BASE_IDX                                                            0
#define mmATC_VMID19_PASID_MAPPING                                                                     0x005a
#define mmATC_VMID19_PASID_MAPPING_BASE_IDX                                                            0
#define mmATC_VMID20_PASID_MAPPING                                                                     0x005b
#define mmATC_VMID20_PASID_MAPPING_BASE_IDX                                                            0
#define mmATC_VMID21_PASID_MAPPING                                                                     0x005c
#define mmATC_VMID21_PASID_MAPPING_BASE_IDX                                                            0
#define mmATC_VMID22_PASID_MAPPING                                                                     0x005d
#define mmATC_VMID22_PASID_MAPPING_BASE_IDX                                                            0
#define mmATC_VMID23_PASID_MAPPING                                                                     0x005e
#define mmATC_VMID23_PASID_MAPPING_BASE_IDX                                                            0
#define mmATC_VMID24_PASID_MAPPING                                                                     0x005f
#define mmATC_VMID24_PASID_MAPPING_BASE_IDX                                                            0
#define mmATC_VMID25_PASID_MAPPING                                                                     0x0060
#define mmATC_VMID25_PASID_MAPPING_BASE_IDX                                                            0
#define mmATC_VMID26_PASID_MAPPING                                                                     0x0061
#define mmATC_VMID26_PASID_MAPPING_BASE_IDX                                                            0
#define mmATC_VMID27_PASID_MAPPING                                                                     0x0062
#define mmATC_VMID27_PASID_MAPPING_BASE_IDX                                                            0
#define mmATC_VMID28_PASID_MAPPING                                                                     0x0063
#define mmATC_VMID28_PASID_MAPPING_BASE_IDX                                                            0
#define mmATC_VMID29_PASID_MAPPING                                                                     0x0064
#define mmATC_VMID29_PASID_MAPPING_BASE_IDX                                                            0
#define mmATC_VMID30_PASID_MAPPING                                                                     0x0065
#define mmATC_VMID30_PASID_MAPPING_BASE_IDX                                                            0
#define mmATC_VMID31_PASID_MAPPING                                                                     0x0066
#define mmATC_VMID31_PASID_MAPPING_BASE_IDX                                                            0


// addressBlock: athub_xpbdec
// base address: 0x31a0
#define mmXPB_RTR_SRC_APRTR0                                                                           0x0068
#define mmXPB_RTR_SRC_APRTR0_BASE_IDX                                                                  0
#define mmXPB_RTR_SRC_APRTR1                                                                           0x0069
#define mmXPB_RTR_SRC_APRTR1_BASE_IDX                                                                  0
#define mmXPB_RTR_SRC_APRTR2                                                                           0x006a
#define mmXPB_RTR_SRC_APRTR2_BASE_IDX                                                                  0
#define mmXPB_RTR_SRC_APRTR3                                                                           0x006b
#define mmXPB_RTR_SRC_APRTR3_BASE_IDX                                                                  0
#define mmXPB_RTR_SRC_APRTR4                                                                           0x006c
#define mmXPB_RTR_SRC_APRTR4_BASE_IDX                                                                  0
#define mmXPB_RTR_SRC_APRTR5                                                                           0x006d
#define mmXPB_RTR_SRC_APRTR5_BASE_IDX                                                                  0
#define mmXPB_RTR_SRC_APRTR6                                                                           0x006e
#define mmXPB_RTR_SRC_APRTR6_BASE_IDX                                                                  0
#define mmXPB_RTR_SRC_APRTR7                                                                           0x006f
#define mmXPB_RTR_SRC_APRTR7_BASE_IDX                                                                  0
#define mmXPB_RTR_SRC_APRTR8                                                                           0x0070
#define mmXPB_RTR_SRC_APRTR8_BASE_IDX                                                                  0
#define mmXPB_RTR_SRC_APRTR9                                                                           0x0071
#define mmXPB_RTR_SRC_APRTR9_BASE_IDX                                                                  0
#define mmXPB_RTR_SRC_APRTR10                                                                          0x0072
#define mmXPB_RTR_SRC_APRTR10_BASE_IDX                                                                 0
#define mmXPB_RTR_SRC_APRTR11                                                                          0x0073
#define mmXPB_RTR_SRC_APRTR11_BASE_IDX                                                                 0
#define mmXPB_RTR_SRC_APRTR12                                                                          0x0074
#define mmXPB_RTR_SRC_APRTR12_BASE_IDX                                                                 0
#define mmXPB_RTR_SRC_APRTR13                                                                          0x0075
#define mmXPB_RTR_SRC_APRTR13_BASE_IDX                                                                 0
#define mmXPB_RTR_DEST_MAP0                                                                            0x0076
#define mmXPB_RTR_DEST_MAP0_BASE_IDX                                                                   0
#define mmXPB_RTR_DEST_MAP1                                                                            0x0077
#define mmXPB_RTR_DEST_MAP1_BASE_IDX                                                                   0
#define mmXPB_RTR_DEST_MAP2                                                                            0x0078
#define mmXPB_RTR_DEST_MAP2_BASE_IDX                                                                   0
#define mmXPB_RTR_DEST_MAP3                                                                            0x0079
#define mmXPB_RTR_DEST_MAP3_BASE_IDX                                                                   0
#define mmXPB_RTR_DEST_MAP4                                                                            0x007a
#define mmXPB_RTR_DEST_MAP4_BASE_IDX                                                                   0
#define mmXPB_RTR_DEST_MAP5                                                                            0x007b
#define mmXPB_RTR_DEST_MAP5_BASE_IDX                                                                   0
#define mmXPB_RTR_DEST_MAP6                                                                            0x007c
#define mmXPB_RTR_DEST_MAP6_BASE_IDX                                                                   0
#define mmXPB_RTR_DEST_MAP7                                                                            0x007d
#define mmXPB_RTR_DEST_MAP7_BASE_IDX                                                                   0
#define mmXPB_RTR_DEST_MAP8                                                                            0x007e
#define mmXPB_RTR_DEST_MAP8_BASE_IDX                                                                   0
#define mmXPB_RTR_DEST_MAP9                                                                            0x007f
#define mmXPB_RTR_DEST_MAP9_BASE_IDX                                                                   0
#define mmXPB_RTR_DEST_MAP10                                                                           0x0080
#define mmXPB_RTR_DEST_MAP10_BASE_IDX                                                                  0
#define mmXPB_RTR_DEST_MAP11                                                                           0x0081
#define mmXPB_RTR_DEST_MAP11_BASE_IDX                                                                  0
#define mmXPB_RTR_DEST_MAP12                                                                           0x0082
#define mmXPB_RTR_DEST_MAP12_BASE_IDX                                                                  0
#define mmXPB_RTR_DEST_MAP13                                                                           0x0083
#define mmXPB_RTR_DEST_MAP13_BASE_IDX                                                                  0
#define mmXPB_CLG_CFG0                                                                                 0x0084
#define mmXPB_CLG_CFG0_BASE_IDX                                                                        0
#define mmXPB_CLG_CFG1                                                                                 0x0085
#define mmXPB_CLG_CFG1_BASE_IDX                                                                        0
#define mmXPB_CLG_CFG2                                                                                 0x0086
#define mmXPB_CLG_CFG2_BASE_IDX                                                                        0
#define mmXPB_CLG_CFG3                                                                                 0x0087
#define mmXPB_CLG_CFG3_BASE_IDX                                                                        0
#define mmXPB_CLG_CFG4                                                                                 0x0088
#define mmXPB_CLG_CFG4_BASE_IDX                                                                        0
#define mmXPB_CLG_CFG5                                                                                 0x0089
#define mmXPB_CLG_CFG5_BASE_IDX                                                                        0
#define mmXPB_CLG_CFG6                                                                                 0x008a
#define mmXPB_CLG_CFG6_BASE_IDX                                                                        0
#define mmXPB_CLG_CFG7                                                                                 0x008b
#define mmXPB_CLG_CFG7_BASE_IDX                                                                        0
#define mmXPB_CLG_EXTRA                                                                                0x008c
#define mmXPB_CLG_EXTRA_BASE_IDX                                                                       0
#define mmXPB_CLG_EXTRA_MSK                                                                            0x008d
#define mmXPB_CLG_EXTRA_MSK_BASE_IDX                                                                   0
#define mmXPB_LB_ADDR                                                                                  0x008e
#define mmXPB_LB_ADDR_BASE_IDX                                                                         0
#define mmXPB_WCB_STS                                                                                  0x008f
#define mmXPB_WCB_STS_BASE_IDX                                                                         0
#define mmXPB_HST_CFG                                                                                  0x0090
#define mmXPB_HST_CFG_BASE_IDX                                                                         0
#define mmXPB_P2P_BAR_CFG                                                                              0x0091
#define mmXPB_P2P_BAR_CFG_BASE_IDX                                                                     0
#define mmXPB_P2P_BAR0                                                                                 0x0092
#define mmXPB_P2P_BAR0_BASE_IDX                                                                        0
#define mmXPB_P2P_BAR1                                                                                 0x0093
#define mmXPB_P2P_BAR1_BASE_IDX                                                                        0
#define mmXPB_P2P_BAR2                                                                                 0x0094
#define mmXPB_P2P_BAR2_BASE_IDX                                                                        0
#define mmXPB_P2P_BAR3                                                                                 0x0095
#define mmXPB_P2P_BAR3_BASE_IDX                                                                        0
#define mmXPB_P2P_BAR4                                                                                 0x0096
#define mmXPB_P2P_BAR4_BASE_IDX                                                                        0
#define mmXPB_P2P_BAR5                                                                                 0x0097
#define mmXPB_P2P_BAR5_BASE_IDX                                                                        0
#define mmXPB_P2P_BAR6                                                                                 0x0098
#define mmXPB_P2P_BAR6_BASE_IDX                                                                        0
#define mmXPB_P2P_BAR7                                                                                 0x0099
#define mmXPB_P2P_BAR7_BASE_IDX                                                                        0
#define mmXPB_P2P_BAR_SETUP                                                                            0x009a
#define mmXPB_P2P_BAR_SETUP_BASE_IDX                                                                   0
#define mmXPB_P2P_BAR_DELTA_ABOVE                                                                      0x009c
#define mmXPB_P2P_BAR_DELTA_ABOVE_BASE_IDX                                                             0
#define mmXPB_P2P_BAR_DELTA_BELOW                                                                      0x009d
#define mmXPB_P2P_BAR_DELTA_BELOW_BASE_IDX                                                             0
#define mmXPB_PEER_SYS_BAR0                                                                            0x009e
#define mmXPB_PEER_SYS_BAR0_BASE_IDX                                                                   0
#define mmXPB_PEER_SYS_BAR1                                                                            0x009f
#define mmXPB_PEER_SYS_BAR1_BASE_IDX                                                                   0
#define mmXPB_PEER_SYS_BAR2                                                                            0x00a0
#define mmXPB_PEER_SYS_BAR2_BASE_IDX                                                                   0
#define mmXPB_PEER_SYS_BAR3                                                                            0x00a1
#define mmXPB_PEER_SYS_BAR3_BASE_IDX                                                                   0
#define mmXPB_PEER_SYS_BAR4                                                                            0x00a2
#define mmXPB_PEER_SYS_BAR4_BASE_IDX                                                                   0
#define mmXPB_PEER_SYS_BAR5                                                                            0x00a3
#define mmXPB_PEER_SYS_BAR5_BASE_IDX                                                                   0
#define mmXPB_PEER_SYS_BAR6                                                                            0x00a4
#define mmXPB_PEER_SYS_BAR6_BASE_IDX                                                                   0
#define mmXPB_PEER_SYS_BAR7                                                                            0x00a5
#define mmXPB_PEER_SYS_BAR7_BASE_IDX                                                                   0
#define mmXPB_PEER_SYS_BAR8                                                                            0x00a6
#define mmXPB_PEER_SYS_BAR8_BASE_IDX                                                                   0
#define mmXPB_PEER_SYS_BAR9                                                                            0x00a7
#define mmXPB_PEER_SYS_BAR9_BASE_IDX                                                                   0
#define mmXPB_PEER_SYS_BAR10                                                                           0x00a8
#define mmXPB_PEER_SYS_BAR10_BASE_IDX                                                                  0
#define mmXPB_PEER_SYS_BAR11                                                                           0x00a9
#define mmXPB_PEER_SYS_BAR11_BASE_IDX                                                                  0
#define mmXPB_PEER_SYS_BAR12                                                                           0x00aa
#define mmXPB_PEER_SYS_BAR12_BASE_IDX                                                                  0
#define mmXPB_PEER_SYS_BAR13                                                                           0x00ab
#define mmXPB_PEER_SYS_BAR13_BASE_IDX                                                                  0
#define mmXPB_CLK_GAT                                                                                  0x00ac
#define mmXPB_CLK_GAT_BASE_IDX                                                                         0
#define mmXPB_INTF_CFG                                                                                 0x00ad
#define mmXPB_INTF_CFG_BASE_IDX                                                                        0
#define mmXPB_INTF_STS                                                                                 0x00ae
#define mmXPB_INTF_STS_BASE_IDX                                                                        0
#define mmXPB_PIPE_STS                                                                                 0x00af
#define mmXPB_PIPE_STS_BASE_IDX                                                                        0
#define mmXPB_SUB_CTRL                                                                                 0x00b0
#define mmXPB_SUB_CTRL_BASE_IDX                                                                        0
#define mmXPB_MAP_INVERT_FLUSH_NUM_LSB                                                                 0x00b1
#define mmXPB_MAP_INVERT_FLUSH_NUM_LSB_BASE_IDX                                                        0
#define mmXPB_PERF_KNOBS                                                                               0x00b2
#define mmXPB_PERF_KNOBS_BASE_IDX                                                                      0
#define mmXPB_STICKY                                                                                   0x00b3
#define mmXPB_STICKY_BASE_IDX                                                                          0
#define mmXPB_STICKY_W1C                                                                               0x00b4
#define mmXPB_STICKY_W1C_BASE_IDX                                                                      0
#define mmXPB_MISC_CFG                                                                                 0x00b5
#define mmXPB_MISC_CFG_BASE_IDX                                                                        0
#define mmXPB_INTF_CFG2                                                                                0x00b6
#define mmXPB_INTF_CFG2_BASE_IDX                                                                       0
#define mmXPB_CLG_EXTRA_RD                                                                             0x00b7
#define mmXPB_CLG_EXTRA_RD_BASE_IDX                                                                    0
#define mmXPB_CLG_EXTRA_MSK_RD                                                                         0x00b8
#define mmXPB_CLG_EXTRA_MSK_RD_BASE_IDX                                                                0
#define mmXPB_CLG_GFX_MATCH                                                                            0x00b9
#define mmXPB_CLG_GFX_MATCH_BASE_IDX                                                                   0
#define mmXPB_CLG_GFX_MATCH_MSK                                                                        0x00ba
#define mmXPB_CLG_GFX_MATCH_MSK_BASE_IDX                                                               0
#define mmXPB_CLG_MM_MATCH                                                                             0x00bb
#define mmXPB_CLG_MM_MATCH_BASE_IDX                                                                    0
#define mmXPB_CLG_MM_MATCH_MSK                                                                         0x00bc
#define mmXPB_CLG_MM_MATCH_MSK_BASE_IDX                                                                0
#define mmXPB_CLG_GUS_MATCH                                                                            0x00bd
#define mmXPB_CLG_GUS_MATCH_BASE_IDX                                                                   0
#define mmXPB_CLG_GUS_MATCH_MSK                                                                        0x00be
#define mmXPB_CLG_GUS_MATCH_MSK_BASE_IDX                                                               0
#define mmXPB_CLG_GFX_UNITID_MAPPING0                                                                  0x00bf
#define mmXPB_CLG_GFX_UNITID_MAPPING0_BASE_IDX                                                         0
#define mmXPB_CLG_GFX_UNITID_MAPPING1                                                                  0x00c0
#define mmXPB_CLG_GFX_UNITID_MAPPING1_BASE_IDX                                                         0
#define mmXPB_CLG_GFX_UNITID_MAPPING2                                                                  0x00c1
#define mmXPB_CLG_GFX_UNITID_MAPPING2_BASE_IDX                                                         0
#define mmXPB_CLG_GFX_UNITID_MAPPING3                                                                  0x00c2
#define mmXPB_CLG_GFX_UNITID_MAPPING3_BASE_IDX                                                         0
#define mmXPB_CLG_GFX_UNITID_MAPPING4                                                                  0x00c3
#define mmXPB_CLG_GFX_UNITID_MAPPING4_BASE_IDX                                                         0
#define mmXPB_CLG_GFX_UNITID_MAPPING5                                                                  0x00c4
#define mmXPB_CLG_GFX_UNITID_MAPPING5_BASE_IDX                                                         0
#define mmXPB_CLG_GFX_UNITID_MAPPING6                                                                  0x00c5
#define mmXPB_CLG_GFX_UNITID_MAPPING6_BASE_IDX                                                         0
#define mmXPB_CLG_GFX_UNITID_MAPPING7                                                                  0x00c6
#define mmXPB_CLG_GFX_UNITID_MAPPING7_BASE_IDX                                                         0
#define mmXPB_CLG_MM_UNITID_MAPPING0                                                                   0x00c7
#define mmXPB_CLG_MM_UNITID_MAPPING0_BASE_IDX                                                          0
#define mmXPB_CLG_MM_UNITID_MAPPING1                                                                   0x00c8
#define mmXPB_CLG_MM_UNITID_MAPPING1_BASE_IDX                                                          0
#define mmXPB_CLG_MM_UNITID_MAPPING2                                                                   0x00c9
#define mmXPB_CLG_MM_UNITID_MAPPING2_BASE_IDX                                                          0
#define mmXPB_CLG_MM_UNITID_MAPPING3                                                                   0x00ca
#define mmXPB_CLG_MM_UNITID_MAPPING3_BASE_IDX                                                          0
#define mmXPB_CLG_GUS_UNITID_MAPPING0                                                                  0x00cb
#define mmXPB_CLG_GUS_UNITID_MAPPING0_BASE_IDX                                                         0
#define mmXPB_CLG_GUS_UNITID_MAPPING1                                                                  0x00cc
#define mmXPB_CLG_GUS_UNITID_MAPPING1_BASE_IDX                                                         0
#define mmXPB_CLG_GUS_UNITID_MAPPING2                                                                  0x00cd
#define mmXPB_CLG_GUS_UNITID_MAPPING2_BASE_IDX                                                         0
#define mmXPB_CLG_GUS_UNITID_MAPPING3                                                                  0x00ce
#define mmXPB_CLG_GUS_UNITID_MAPPING3_BASE_IDX                                                         0
#define mmXPB_CLG_GUS_UNITID_MAPPING4                                                                  0x00cf
#define mmXPB_CLG_GUS_UNITID_MAPPING4_BASE_IDX                                                         0
#define mmXPB_CLG_GUS_UNITID_MAPPING5                                                                  0x00d0
#define mmXPB_CLG_GUS_UNITID_MAPPING5_BASE_IDX                                                         0
#define mmXPB_CLG_GUS_UNITID_MAPPING6                                                                  0x00d1
#define mmXPB_CLG_GUS_UNITID_MAPPING6_BASE_IDX                                                         0
#define mmXPB_CLG_GUS_UNITID_MAPPING7                                                                  0x00d2
#define mmXPB_CLG_GUS_UNITID_MAPPING7_BASE_IDX                                                         0


// addressBlock: athub_rpbdec
// base address: 0x3350
#define mmRPB_PASSPW_CONF                                                                              0x00d4
#define mmRPB_PASSPW_CONF_BASE_IDX                                                                     0
#define mmRPB_BLOCKLEVEL_CONF                                                                          0x00d5
#define mmRPB_BLOCKLEVEL_CONF_BASE_IDX                                                                 0
#define mmRPB_TAG_CONF                                                                                 0x00d6
#define mmRPB_TAG_CONF_BASE_IDX                                                                        0
#define mmRPB_EFF_CNTL                                                                                 0x00d8
#define mmRPB_EFF_CNTL_BASE_IDX                                                                        0
#define mmRPB_ARB_CNTL                                                                                 0x00d9
#define mmRPB_ARB_CNTL_BASE_IDX                                                                        0
#define mmRPB_ARB_CNTL2                                                                                0x00da
#define mmRPB_ARB_CNTL2_BASE_IDX                                                                       0
#define mmRPB_BIF_CNTL                                                                                 0x00db
#define mmRPB_BIF_CNTL_BASE_IDX                                                                        0
#define mmRPB_BIF_CNTL2                                                                                0x00dc
#define mmRPB_BIF_CNTL2_BASE_IDX                                                                       0
#define mmRPB_WR_SWITCH_CNTL                                                                           0x00dd
#define mmRPB_WR_SWITCH_CNTL_BASE_IDX                                                                  0
#define mmRPB_RD_SWITCH_CNTL                                                                           0x00de
#define mmRPB_RD_SWITCH_CNTL_BASE_IDX                                                                  0
#define mmRPB_SWITCH_CNTL2                                                                             0x00df
#define mmRPB_SWITCH_CNTL2_BASE_IDX                                                                    0
#define mmRPB_CID_QUEUE_WR                                                                             0x00e0
#define mmRPB_CID_QUEUE_WR_BASE_IDX                                                                    0
#define mmRPB_EA_QUEUE_WR                                                                              0x00e1
#define mmRPB_EA_QUEUE_WR_BASE_IDX                                                                     0
#define mmRPB_CID_QUEUE_RD                                                                             0x00e2
#define mmRPB_CID_QUEUE_RD_BASE_IDX                                                                    0
#define mmRPB_CID_QUEUE_EX                                                                             0x00e3
#define mmRPB_CID_QUEUE_EX_BASE_IDX                                                                    0
#define mmRPB_CID_QUEUE_EX_DATA                                                                        0x00e4
#define mmRPB_CID_QUEUE_EX_DATA_BASE_IDX                                                               0
#define mmRPB_DEINTRLV_COMBINE_CNTL                                                                    0x00e5
#define mmRPB_DEINTRLV_COMBINE_CNTL_BASE_IDX                                                           0
#define mmRPB_VC_SWITCH_RDWR                                                                           0x00e6
#define mmRPB_VC_SWITCH_RDWR_BASE_IDX                                                                  0
#define mmRPB_PERF_COUNTER_CNTL                                                                        0x00e7
#define mmRPB_PERF_COUNTER_CNTL_BASE_IDX                                                               0
#define mmRPB_PERF_COUNTER_STATUS                                                                      0x00e8
#define mmRPB_PERF_COUNTER_STATUS_BASE_IDX                                                             0
#define mmRPB_PERFCOUNTER_LO                                                                           0x00e9
#define mmRPB_PERFCOUNTER_LO_BASE_IDX                                                                  0
#define mmRPB_PERFCOUNTER_HI                                                                           0x00ea
#define mmRPB_PERFCOUNTER_HI_BASE_IDX                                                                  0
#define mmRPB_PERFCOUNTER0_CFG                                                                         0x00eb
#define mmRPB_PERFCOUNTER0_CFG_BASE_IDX                                                                0
#define mmRPB_PERFCOUNTER1_CFG                                                                         0x00ec
#define mmRPB_PERFCOUNTER1_CFG_BASE_IDX                                                                0
#define mmRPB_PERFCOUNTER2_CFG                                                                         0x00ed
#define mmRPB_PERFCOUNTER2_CFG_BASE_IDX                                                                0
#define mmRPB_PERFCOUNTER3_CFG                                                                         0x00ee
#define mmRPB_PERFCOUNTER3_CFG_BASE_IDX                                                                0
#define mmRPB_PERFCOUNTER_RSLT_CNTL                                                                    0x00ef
#define mmRPB_PERFCOUNTER_RSLT_CNTL_BASE_IDX                                                           0
#define mmRPB_RD_QUEUE_CNTL                                                                            0x00f0
#define mmRPB_RD_QUEUE_CNTL_BASE_IDX                                                                   0
#define mmRPB_RD_QUEUE_CNTL2                                                                           0x00f1
#define mmRPB_RD_QUEUE_CNTL2_BASE_IDX                                                                  0
#define mmRPB_WR_QUEUE_CNTL                                                                            0x00f2
#define mmRPB_WR_QUEUE_CNTL_BASE_IDX                                                                   0
#define mmRPB_WR_QUEUE_CNTL2                                                                           0x00f3
#define mmRPB_WR_QUEUE_CNTL2_BASE_IDX                                                                  0
#define mmRPB_ATS_CNTL                                                                                 0x00f4
#define mmRPB_ATS_CNTL_BASE_IDX                                                                        0
#define mmRPB_ATS_CNTL2                                                                                0x00f5
#define mmRPB_ATS_CNTL2_BASE_IDX                                                                       0
#define mmRPB_ATS_CNTL3                                                                                0x00f6
#define mmRPB_ATS_CNTL3_BASE_IDX                                                                       0
#define mmRPB_DF_SDPPORT_CNTL                                                                          0x00f7
#define mmRPB_DF_SDPPORT_CNTL_BASE_IDX                                                                 0
#define mmRPB_SDPPORT_CNTL                                                                             0x00f8
#define mmRPB_SDPPORT_CNTL_BASE_IDX                                                                    0
#define mmRPB_NBIF_SDPPORT_CNTL                                                                        0x00f9
#define mmRPB_NBIF_SDPPORT_CNTL_BASE_IDX                                                               0

#endif
