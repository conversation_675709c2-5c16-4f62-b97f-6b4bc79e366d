// <experimental/unordered_set> -*- C++ -*-

// Copyright (C) 2015-2017 Free Software Foundation, Inc.
//
// This file is part of the GNU ISO C++ Library.  This library is free
// software; you can redistribute it and/or modify it under the
// terms of the GNU General Public License as published by the
// Free Software Foundation; either version 3, or (at your option)
// any later version.

// This library is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.

// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.

// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

/** @file experimental/unordered_set
 *  This is a TS C++ Library header.
 */

#ifndef _GLIBCXX_EXPERIMENTAL_UNORDERED_SET
#define _GLIBCXX_EXPERIMENTAL_UNORDERED_SET 1

#pragma GCC system_header

#if __cplusplus >= 201402L

#include <unordered_set>
#include <experimental/bits/erase_if.h>
#include <experimental/memory_resource>

namespace std _GLIBCXX_VISIBILITY(default)
{
namespace experimental
{
inline namespace fundamentals_v2
{
_GLIBCXX_BEGIN_NAMESPACE_VERSION

  template<typename _Key, typename _Hash, typename _CPred, typename _Alloc,
	   typename _Predicate>
    inline void
    erase_if(unordered_set<_Key, _Hash, _CPred, _Alloc>& __cont,
	     _Predicate __pred)
    { __detail::__erase_nodes_if(__cont, __pred); }

  template<typename _Key, typename _Hash, typename _CPred, typename _Alloc,
	   typename _Predicate>
    inline void
    erase_if(unordered_multiset<_Key, _Hash, _CPred, _Alloc>& __cont,
	     _Predicate __pred)
    { __detail::__erase_nodes_if(__cont, __pred); }

_GLIBCXX_END_NAMESPACE_VERSION

namespace pmr {
_GLIBCXX_BEGIN_NAMESPACE_VERSION

  template<typename _Key, typename _Hash = hash<_Key>,
	   typename _Pred = equal_to<_Key>>
    using unordered_set
      = std::unordered_set<_Key, _Hash, _Pred, polymorphic_allocator<_Key>>;

  template<typename _Key, typename _Hash = hash<_Key>,
	   typename _Pred = equal_to<_Key>>
    using unordered_multiset
      = std::unordered_multiset<_Key, _Hash, _Pred,
				polymorphic_allocator<_Key>>;

_GLIBCXX_END_NAMESPACE_VERSION
} // namespace pmr

} // namespace fundamentals_v2
} // namespace experimental
} // namespace std

#endif // C++14

#endif // _GLIBCXX_EXPERIMENTAL_UNORDERED_SET
