/* Copyright 2020 Advanced Micro Devices, Inc.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a
 * copy of this software and associated documentation files (the "Software"),
 * to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
 * THE COPYRIGHT HOLDER(S) OR AUTHOR(S) BE LIABLE FOR ANY CLAIM, DAMAGES OR
 * OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 * OTHER DEALINGS IN THE SOFTWARE.
 *
 * Authors: <AUTHORS>
 *
 */
#ifndef __DC_DWBC_DCN30_H__
#define __DC_DWBC_DCN30_H__

#define TO_DCN30_DWBC(dwbc_base) \
	container_of(dwbc_base, struct dcn30_dwbc, base)

/* DCN */
#define BASE_INNER(seg) \
	DCE_BASE__INST0_SEG ## seg

#define BASE(seg) \
	BASE_INNER(seg)

#define SF_DWB(reg_name, block, id, field_name, post_fix)\
	.field_name = block ## id ## _ ## reg_name ## __ ## field_name ## post_fix

 /* set field name */
#define SF_DWB2(reg_name, block, id, field_name, post_fix)\
	.field_name = reg_name ## __ ## field_name ## post_fix


#define DWBC_COMMON_REG_LIST_DCN30(inst) \
	SR(DWB_ENABLE_CLK_CTRL),\
	SR(DWB_MEM_PWR_CTRL),\
	SR(FC_MODE_CTRL),\
	SR(FC_FLOW_CTRL),\
	SR(FC_WINDOW_START),\
	SR(FC_WINDOW_SIZE),\
	SR(FC_SOURCE_SIZE),\
	SR(DWB_UPDATE_CTRL),\
	SR(DWB_CRC_CTRL),\
	SR(DWB_CRC_MASK_R_G),\
	SR(DWB_CRC_MASK_B_A),\
	SR(DWB_CRC_VAL_R_G),\
	SR(DWB_CRC_VAL_B_A),\
	SR(DWB_OUT_CTRL),\
	SR(DWB_MMHUBBUB_BACKPRESSURE_CNT_EN),\
	SR(DWB_MMHUBBUB_BACKPRESSURE_CNT),\
	SR(DWB_HOST_READ_CONTROL),\
	SR(DWB_SOFT_RESET),\
	SR(DWB_HDR_MULT_COEF),\
	SR(DWB_GAMUT_REMAP_MODE),\
	SR(DWB_GAMUT_REMAP_COEF_FORMAT),\
	SR(DWB_GAMUT_REMAPA_C11_C12),\
	SR(DWB_GAMUT_REMAPA_C13_C14),\
	SR(DWB_GAMUT_REMAPA_C21_C22),\
	SR(DWB_GAMUT_REMAPA_C23_C24),\
	SR(DWB_GAMUT_REMAPA_C31_C32),\
	SR(DWB_GAMUT_REMAPA_C33_C34),\
	SR(DWB_GAMUT_REMAPB_C11_C12),\
	SR(DWB_GAMUT_REMAPB_C13_C14),\
	SR(DWB_GAMUT_REMAPB_C21_C22),\
	SR(DWB_GAMUT_REMAPB_C23_C24),\
	SR(DWB_GAMUT_REMAPB_C31_C32),\
	SR(DWB_GAMUT_REMAPB_C33_C34),\
	SR(DWB_OGAM_CONTROL),\
	SR(DWB_OGAM_LUT_INDEX),\
	SR(DWB_OGAM_LUT_DATA),\
	SR(DWB_OGAM_LUT_CONTROL),\
	SR(DWB_OGAM_RAMA_START_CNTL_B),\
	SR(DWB_OGAM_RAMA_START_CNTL_G),\
	SR(DWB_OGAM_RAMA_START_CNTL_R),\
	SR(DWB_OGAM_RAMA_START_BASE_CNTL_B),\
	SR(DWB_OGAM_RAMA_START_SLOPE_CNTL_B),\
	SR(DWB_OGAM_RAMA_START_BASE_CNTL_G),\
	SR(DWB_OGAM_RAMA_START_SLOPE_CNTL_G),\
	SR(DWB_OGAM_RAMA_START_BASE_CNTL_R),\
	SR(DWB_OGAM_RAMA_START_SLOPE_CNTL_R),\
	SR(DWB_OGAM_RAMA_END_CNTL1_B),\
	SR(DWB_OGAM_RAMA_END_CNTL2_B),\
	SR(DWB_OGAM_RAMA_END_CNTL1_G),\
	SR(DWB_OGAM_RAMA_END_CNTL2_G),\
	SR(DWB_OGAM_RAMA_END_CNTL1_R),\
	SR(DWB_OGAM_RAMA_END_CNTL2_R),\
	SR(DWB_OGAM_RAMA_OFFSET_B),\
	SR(DWB_OGAM_RAMA_OFFSET_G),\
	SR(DWB_OGAM_RAMA_OFFSET_R),\
	SR(DWB_OGAM_RAMA_REGION_0_1),\
	SR(DWB_OGAM_RAMA_REGION_2_3),\
	SR(DWB_OGAM_RAMA_REGION_4_5),\
	SR(DWB_OGAM_RAMA_REGION_6_7),\
	SR(DWB_OGAM_RAMA_REGION_8_9),\
	SR(DWB_OGAM_RAMA_REGION_10_11),\
	SR(DWB_OGAM_RAMA_REGION_12_13),\
	SR(DWB_OGAM_RAMA_REGION_14_15),\
	SR(DWB_OGAM_RAMA_REGION_16_17),\
	SR(DWB_OGAM_RAMA_REGION_18_19),\
	SR(DWB_OGAM_RAMA_REGION_20_21),\
	SR(DWB_OGAM_RAMA_REGION_22_23),\
	SR(DWB_OGAM_RAMA_REGION_24_25),\
	SR(DWB_OGAM_RAMA_REGION_26_27),\
	SR(DWB_OGAM_RAMA_REGION_28_29),\
	SR(DWB_OGAM_RAMA_REGION_30_31),\
	SR(DWB_OGAM_RAMA_REGION_32_33),\
	SR(DWB_OGAM_RAMB_START_CNTL_B),\
	SR(DWB_OGAM_RAMB_START_CNTL_G),\
	SR(DWB_OGAM_RAMB_START_CNTL_R),\
	SR(DWB_OGAM_RAMB_START_BASE_CNTL_B),\
	SR(DWB_OGAM_RAMB_START_SLOPE_CNTL_B),\
	SR(DWB_OGAM_RAMB_START_BASE_CNTL_G),\
	SR(DWB_OGAM_RAMB_START_SLOPE_CNTL_G),\
	SR(DWB_OGAM_RAMB_START_BASE_CNTL_R),\
	SR(DWB_OGAM_RAMB_START_SLOPE_CNTL_R),\
	SR(DWB_OGAM_RAMB_END_CNTL1_B),\
	SR(DWB_OGAM_RAMB_END_CNTL2_B),\
	SR(DWB_OGAM_RAMB_END_CNTL1_G),\
	SR(DWB_OGAM_RAMB_END_CNTL2_G),\
	SR(DWB_OGAM_RAMB_END_CNTL1_R),\
	SR(DWB_OGAM_RAMB_END_CNTL2_R),\
	SR(DWB_OGAM_RAMB_OFFSET_B),\
	SR(DWB_OGAM_RAMB_OFFSET_G),\
	SR(DWB_OGAM_RAMB_OFFSET_R),\
	SR(DWB_OGAM_RAMB_REGION_0_1),\
	SR(DWB_OGAM_RAMB_REGION_2_3),\
	SR(DWB_OGAM_RAMB_REGION_4_5),\
	SR(DWB_OGAM_RAMB_REGION_6_7),\
	SR(DWB_OGAM_RAMB_REGION_8_9),\
	SR(DWB_OGAM_RAMB_REGION_10_11),\
	SR(DWB_OGAM_RAMB_REGION_12_13),\
	SR(DWB_OGAM_RAMB_REGION_14_15),\
	SR(DWB_OGAM_RAMB_REGION_16_17),\
	SR(DWB_OGAM_RAMB_REGION_18_19),\
	SR(DWB_OGAM_RAMB_REGION_20_21),\
	SR(DWB_OGAM_RAMB_REGION_22_23),\
	SR(DWB_OGAM_RAMB_REGION_24_25),\
	SR(DWB_OGAM_RAMB_REGION_26_27),\
	SR(DWB_OGAM_RAMB_REGION_28_29),\
	SR(DWB_OGAM_RAMB_REGION_30_31),\
	SR(DWB_OGAM_RAMB_REGION_32_33)


#define DWBC_COMMON_MASK_SH_LIST_DCN30(mask_sh) \
	SF_DWB2(DWB_ENABLE_CLK_CTRL, DWB_TOP, 0, DWB_ENABLE, mask_sh),\
	SF_DWB2(DWB_ENABLE_CLK_CTRL, DWB_TOP, 0, DISPCLK_R_DWB_GATE_DIS, mask_sh),\
	SF_DWB2(DWB_ENABLE_CLK_CTRL, DWB_TOP, 0, DISPCLK_G_DWB_GATE_DIS, mask_sh),\
	SF_DWB2(DWB_ENABLE_CLK_CTRL, DWB_TOP, 0, DWB_TEST_CLK_SEL, mask_sh),\
	SF_DWB2(DWB_MEM_PWR_CTRL, DWB_TOP, 0, DWB_OGAM_LUT_MEM_PWR_FORCE, mask_sh),\
	SF_DWB2(DWB_MEM_PWR_CTRL, DWB_TOP, 0, DWB_OGAM_LUT_MEM_PWR_DIS, mask_sh),\
	SF_DWB2(DWB_MEM_PWR_CTRL, DWB_TOP, 0, DWB_OGAM_LUT_MEM_PWR_STATE, mask_sh),\
	SF_DWB2(FC_MODE_CTRL, DWB_TOP, 0, FC_FRAME_CAPTURE_EN, mask_sh),\
	SF_DWB2(FC_MODE_CTRL, DWB_TOP, 0, FC_FRAME_CAPTURE_RATE, mask_sh),\
	SF_DWB2(FC_MODE_CTRL, DWB_TOP, 0, FC_WINDOW_CROP_EN, mask_sh),\
	SF_DWB2(FC_MODE_CTRL, DWB_TOP, 0, FC_EYE_SELECTION, mask_sh),\
	SF_DWB2(FC_MODE_CTRL, DWB_TOP, 0, FC_STEREO_EYE_POLARITY, mask_sh),\
	SF_DWB2(FC_MODE_CTRL, DWB_TOP, 0, FC_NEW_CONTENT, mask_sh),\
	SF_DWB2(FC_MODE_CTRL, DWB_TOP, 0, FC_FRAME_CAPTURE_EN_CURRENT, mask_sh),\
	SF_DWB2(FC_FLOW_CTRL, DWB_TOP, 0, FC_FIRST_PIXEL_DELAY_COUNT, mask_sh),\
	SF_DWB2(FC_WINDOW_START, DWB_TOP, 0, FC_WINDOW_START_X, mask_sh),\
	SF_DWB2(FC_WINDOW_START, DWB_TOP, 0, FC_WINDOW_START_Y, mask_sh),\
	SF_DWB2(FC_WINDOW_SIZE, DWB_TOP, 0, FC_WINDOW_WIDTH, mask_sh),\
	SF_DWB2(FC_WINDOW_SIZE, DWB_TOP, 0, FC_WINDOW_HEIGHT, mask_sh),\
	SF_DWB2(FC_SOURCE_SIZE, DWB_TOP, 0, FC_SOURCE_WIDTH, mask_sh),\
	SF_DWB2(FC_SOURCE_SIZE, DWB_TOP, 0, FC_SOURCE_HEIGHT, mask_sh),\
	SF_DWB2(DWB_UPDATE_CTRL, DWB_TOP, 0, DWB_UPDATE_LOCK, mask_sh),\
	SF_DWB2(DWB_UPDATE_CTRL, DWB_TOP, 0, DWB_UPDATE_PENDING, mask_sh),\
	SF_DWB2(DWB_CRC_CTRL, DWB_TOP, 0, DWB_CRC_EN, mask_sh),\
	SF_DWB2(DWB_CRC_CTRL, DWB_TOP, 0, DWB_CRC_CONT_EN, mask_sh),\
	SF_DWB2(DWB_CRC_CTRL, DWB_TOP, 0, DWB_CRC_SRC_SEL, mask_sh),\
	SF_DWB2(DWB_CRC_MASK_R_G, DWB_TOP, 0, DWB_CRC_RED_MASK, mask_sh),\
	SF_DWB2(DWB_CRC_MASK_R_G, DWB_TOP, 0, DWB_CRC_GREEN_MASK, mask_sh),\
	SF_DWB2(DWB_CRC_MASK_B_A, DWB_TOP, 0, DWB_CRC_BLUE_MASK, mask_sh),\
	SF_DWB2(DWB_CRC_MASK_B_A, DWB_TOP, 0, DWB_CRC_A_MASK, mask_sh),\
	SF_DWB2(DWB_CRC_VAL_R_G, DWB_TOP, 0, DWB_CRC_SIG_RED, mask_sh),\
	SF_DWB2(DWB_CRC_VAL_R_G, DWB_TOP, 0, DWB_CRC_SIG_GREEN, mask_sh),\
	SF_DWB2(DWB_CRC_VAL_B_A, DWB_TOP, 0, DWB_CRC_SIG_BLUE, mask_sh),\
	SF_DWB2(DWB_CRC_VAL_B_A, DWB_TOP, 0, DWB_CRC_SIG_A, mask_sh),\
	SF_DWB2(DWB_OUT_CTRL, DWB_TOP, 0, OUT_FORMAT, mask_sh),\
	SF_DWB2(DWB_OUT_CTRL, DWB_TOP, 0, OUT_DENORM, mask_sh),\
	SF_DWB2(DWB_OUT_CTRL, DWB_TOP, 0, OUT_MAX, mask_sh),\
	SF_DWB2(DWB_OUT_CTRL, DWB_TOP, 0, OUT_MIN, mask_sh),\
	SF_DWB2(DWB_MMHUBBUB_BACKPRESSURE_CNT_EN, DWB_TOP, 0, DWB_MMHUBBUB_BACKPRESSURE_CNT_EN, mask_sh),\
	SF_DWB2(DWB_MMHUBBUB_BACKPRESSURE_CNT, DWB_TOP, 0, DWB_MMHUBBUB_MAX_BACKPRESSURE, mask_sh),\
	SF_DWB2(DWB_HOST_READ_CONTROL, DWB_TOP, 0, DWB_HOST_READ_RATE_CONTROL, mask_sh),\
	SF_DWB2(DWB_SOFT_RESET, DWB_TOP, 0, DWB_SOFT_RESET, mask_sh),\
	SF_DWB2(DWB_HDR_MULT_COEF, DWBCP, 0, DWB_HDR_MULT_COEF, mask_sh),\
	SF_DWB2(DWB_GAMUT_REMAP_MODE, DWBCP, 0, DWB_GAMUT_REMAP_MODE, mask_sh),\
	SF_DWB2(DWB_GAMUT_REMAP_MODE, DWBCP, 0, DWB_GAMUT_REMAP_MODE_CURRENT, mask_sh),\
	SF_DWB2(DWB_GAMUT_REMAP_COEF_FORMAT, DWBCP, 0, DWB_GAMUT_REMAP_COEF_FORMAT, mask_sh),\
	SF_DWB2(DWB_GAMUT_REMAPA_C11_C12, DWBCP, 0, DWB_GAMUT_REMAPA_C11, mask_sh),\
	SF_DWB2(DWB_GAMUT_REMAPA_C11_C12, DWBCP, 0, DWB_GAMUT_REMAPA_C12, mask_sh),\
	SF_DWB2(DWB_GAMUT_REMAPA_C13_C14, DWBCP, 0, DWB_GAMUT_REMAPA_C13, mask_sh),\
	SF_DWB2(DWB_GAMUT_REMAPA_C13_C14, DWBCP, 0, DWB_GAMUT_REMAPA_C14, mask_sh),\
	SF_DWB2(DWB_GAMUT_REMAPA_C21_C22, DWBCP, 0, DWB_GAMUT_REMAPA_C21, mask_sh),\
	SF_DWB2(DWB_GAMUT_REMAPA_C21_C22, DWBCP, 0, DWB_GAMUT_REMAPA_C22, mask_sh),\
	SF_DWB2(DWB_GAMUT_REMAPA_C23_C24, DWBCP, 0, DWB_GAMUT_REMAPA_C23, mask_sh),\
	SF_DWB2(DWB_GAMUT_REMAPA_C23_C24, DWBCP, 0, DWB_GAMUT_REMAPA_C24, mask_sh),\
	SF_DWB2(DWB_GAMUT_REMAPA_C31_C32, DWBCP, 0, DWB_GAMUT_REMAPA_C31, mask_sh),\
	SF_DWB2(DWB_GAMUT_REMAPA_C31_C32, DWBCP, 0, DWB_GAMUT_REMAPA_C32, mask_sh),\
	SF_DWB2(DWB_GAMUT_REMAPA_C33_C34, DWBCP, 0, DWB_GAMUT_REMAPA_C33, mask_sh),\
	SF_DWB2(DWB_GAMUT_REMAPA_C33_C34, DWBCP, 0, DWB_GAMUT_REMAPA_C34, mask_sh),\
	SF_DWB2(DWB_GAMUT_REMAPB_C11_C12, DWBCP, 0, DWB_GAMUT_REMAPB_C11, mask_sh),\
	SF_DWB2(DWB_GAMUT_REMAPB_C11_C12, DWBCP, 0, DWB_GAMUT_REMAPB_C12, mask_sh),\
	SF_DWB2(DWB_GAMUT_REMAPB_C13_C14, DWBCP, 0, DWB_GAMUT_REMAPB_C13, mask_sh),\
	SF_DWB2(DWB_GAMUT_REMAPB_C13_C14, DWBCP, 0, DWB_GAMUT_REMAPB_C14, mask_sh),\
	SF_DWB2(DWB_GAMUT_REMAPB_C21_C22, DWBCP, 0, DWB_GAMUT_REMAPB_C21, mask_sh),\
	SF_DWB2(DWB_GAMUT_REMAPB_C21_C22, DWBCP, 0, DWB_GAMUT_REMAPB_C22, mask_sh),\
	SF_DWB2(DWB_GAMUT_REMAPB_C23_C24, DWBCP, 0, DWB_GAMUT_REMAPB_C23, mask_sh),\
	SF_DWB2(DWB_GAMUT_REMAPB_C23_C24, DWBCP, 0, DWB_GAMUT_REMAPB_C24, mask_sh),\
	SF_DWB2(DWB_GAMUT_REMAPB_C31_C32, DWBCP, 0, DWB_GAMUT_REMAPB_C31, mask_sh),\
	SF_DWB2(DWB_GAMUT_REMAPB_C31_C32, DWBCP, 0, DWB_GAMUT_REMAPB_C32, mask_sh),\
	SF_DWB2(DWB_GAMUT_REMAPB_C33_C34, DWBCP, 0, DWB_GAMUT_REMAPB_C33, mask_sh),\
	SF_DWB2(DWB_GAMUT_REMAPB_C33_C34, DWBCP, 0, DWB_GAMUT_REMAPB_C34, mask_sh),\
	SF_DWB2(DWB_OGAM_CONTROL, DWBCP, 0, DWB_OGAM_MODE, mask_sh),\
	SF_DWB2(DWB_OGAM_CONTROL, DWBCP, 0, DWB_OGAM_SELECT, mask_sh),\
	SF_DWB2(DWB_OGAM_CONTROL, DWBCP, 0, DWB_OGAM_PWL_DISABLE, mask_sh),\
	SF_DWB2(DWB_OGAM_CONTROL, DWBCP, 0, DWB_OGAM_MODE_CURRENT, mask_sh),\
	SF_DWB2(DWB_OGAM_CONTROL, DWBCP, 0, DWB_OGAM_SELECT_CURRENT, mask_sh),\
	SF_DWB2(DWB_OGAM_LUT_INDEX, DWBCP, 0, DWB_OGAM_LUT_INDEX, mask_sh),\
	SF_DWB2(DWB_OGAM_LUT_DATA, DWBCP, 0, DWB_OGAM_LUT_DATA, mask_sh),\
	SF_DWB2(DWB_OGAM_LUT_CONTROL, DWBCP, 0, DWB_OGAM_LUT_WRITE_COLOR_MASK, mask_sh),\
	SF_DWB2(DWB_OGAM_LUT_CONTROL, DWBCP, 0, DWB_OGAM_LUT_READ_COLOR_SEL, mask_sh),\
	SF_DWB2(DWB_OGAM_LUT_CONTROL, DWBCP, 0, DWB_OGAM_LUT_READ_DBG, mask_sh),\
	SF_DWB2(DWB_OGAM_LUT_CONTROL, DWBCP, 0, DWB_OGAM_LUT_HOST_SEL, mask_sh),\
	SF_DWB2(DWB_OGAM_LUT_CONTROL, DWBCP, 0, DWB_OGAM_LUT_CONFIG_MODE, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_START_CNTL_B, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION_START_B, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_START_CNTL_B, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION_START_SEGMENT_B, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_START_CNTL_G, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION_START_G, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_START_CNTL_G, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION_START_SEGMENT_G, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_START_CNTL_R, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION_START_R, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_START_CNTL_R, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION_START_SEGMENT_R, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_START_BASE_CNTL_B, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION_START_BASE_B, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_START_SLOPE_CNTL_B, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION_START_SLOPE_B, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_START_BASE_CNTL_G, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION_START_BASE_G, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_START_SLOPE_CNTL_G, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION_START_SLOPE_G, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_START_BASE_CNTL_R, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION_START_BASE_R, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_START_SLOPE_CNTL_R, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION_START_SLOPE_R, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_END_CNTL1_B, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION_END_BASE_B, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_END_CNTL2_B, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION_END_B, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_END_CNTL2_B, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION_END_SLOPE_B, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_END_CNTL1_G, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION_END_BASE_G, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_END_CNTL2_G, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION_END_G, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_END_CNTL2_G, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION_END_SLOPE_G, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_END_CNTL1_R, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION_END_BASE_R, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_END_CNTL2_R, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION_END_R, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_END_CNTL2_R, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION_END_SLOPE_R, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_OFFSET_B, DWBCP, 0, DWB_OGAM_RAMA_OFFSET_B, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_OFFSET_G, DWBCP, 0, DWB_OGAM_RAMA_OFFSET_G, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_OFFSET_R, DWBCP, 0, DWB_OGAM_RAMA_OFFSET_R, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_0_1, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION0_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_0_1, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION0_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_0_1, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION1_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_0_1, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION1_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_2_3, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION2_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_2_3, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION2_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_2_3, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION3_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_2_3, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION3_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_4_5, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION4_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_4_5, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION4_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_4_5, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION5_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_4_5, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION5_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_6_7, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION6_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_6_7, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION6_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_6_7, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION7_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_6_7, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION7_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_8_9, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION8_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_8_9, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION8_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_8_9, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION9_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_8_9, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION9_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_10_11, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION10_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_10_11, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION10_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_10_11, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION11_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_10_11, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION11_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_12_13, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION12_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_12_13, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION12_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_12_13, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION13_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_12_13, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION13_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_14_15, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION14_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_14_15, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION14_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_14_15, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION15_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_14_15, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION15_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_16_17, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION16_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_16_17, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION16_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_16_17, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION17_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_16_17, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION17_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_18_19, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION18_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_18_19, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION18_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_18_19, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION19_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_18_19, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION19_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_20_21, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION20_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_20_21, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION20_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_20_21, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION21_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_20_21, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION21_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_22_23, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION22_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_22_23, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION22_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_22_23, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION23_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_22_23, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION23_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_24_25, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION24_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_24_25, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION24_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_24_25, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION25_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_24_25, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION25_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_26_27, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION26_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_26_27, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION26_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_26_27, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION27_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_26_27, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION27_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_28_29, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION28_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_28_29, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION28_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_28_29, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION29_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_28_29, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION29_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_30_31, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION30_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_30_31, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION30_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_30_31, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION31_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_30_31, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION31_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_32_33, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION32_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_32_33, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION32_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_32_33, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION33_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMA_REGION_32_33, DWBCP, 0, DWB_OGAM_RAMA_EXP_REGION33_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_START_CNTL_B, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION_START_B, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_START_CNTL_B, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION_START_SEGMENT_B, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_START_CNTL_G, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION_START_G, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_START_CNTL_G, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION_START_SEGMENT_G, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_START_CNTL_R, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION_START_R, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_START_CNTL_R, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION_START_SEGMENT_R, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_START_BASE_CNTL_B, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION_START_BASE_B, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_START_SLOPE_CNTL_B, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION_START_SLOPE_B, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_START_BASE_CNTL_G, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION_START_BASE_G, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_START_SLOPE_CNTL_G, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION_START_SLOPE_G, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_START_BASE_CNTL_R, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION_START_BASE_R, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_START_SLOPE_CNTL_R, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION_START_SLOPE_R, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_END_CNTL1_B, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION_END_BASE_B, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_END_CNTL2_B, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION_END_B, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_END_CNTL2_B, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION_END_SLOPE_B, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_END_CNTL1_G, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION_END_BASE_G, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_END_CNTL2_G, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION_END_G, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_END_CNTL2_G, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION_END_SLOPE_G, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_END_CNTL1_R, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION_END_BASE_R, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_END_CNTL2_R, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION_END_R, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_END_CNTL2_R, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION_END_SLOPE_R, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_OFFSET_B, DWBCP, 0, DWB_OGAM_RAMB_OFFSET_B, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_OFFSET_G, DWBCP, 0, DWB_OGAM_RAMB_OFFSET_G, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_OFFSET_R, DWBCP, 0, DWB_OGAM_RAMB_OFFSET_R, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_0_1, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION0_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_0_1, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION0_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_0_1, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION1_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_0_1, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION1_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_2_3, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION2_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_2_3, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION2_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_2_3, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION3_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_2_3, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION3_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_4_5, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION4_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_4_5, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION4_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_4_5, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION5_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_4_5, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION5_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_6_7, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION6_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_6_7, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION6_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_6_7, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION7_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_6_7, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION7_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_8_9, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION8_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_8_9, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION8_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_8_9, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION9_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_8_9, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION9_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_10_11, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION10_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_10_11, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION10_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_10_11, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION11_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_10_11, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION11_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_12_13, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION12_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_12_13, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION12_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_12_13, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION13_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_12_13, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION13_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_14_15, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION14_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_14_15, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION14_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_14_15, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION15_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_14_15, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION15_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_16_17, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION16_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_16_17, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION16_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_16_17, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION17_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_16_17, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION17_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_18_19, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION18_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_18_19, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION18_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_18_19, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION19_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_18_19, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION19_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_20_21, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION20_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_20_21, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION20_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_20_21, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION21_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_20_21, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION21_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_22_23, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION22_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_22_23, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION22_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_22_23, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION23_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_22_23, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION23_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_24_25, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION24_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_24_25, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION24_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_24_25, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION25_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_24_25, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION25_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_26_27, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION26_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_26_27, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION26_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_26_27, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION27_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_26_27, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION27_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_28_29, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION28_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_28_29, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION28_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_28_29, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION29_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_28_29, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION29_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_30_31, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION30_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_30_31, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION30_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_30_31, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION31_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_30_31, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION31_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_32_33, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION32_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_32_33, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION32_NUM_SEGMENTS, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_32_33, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION33_LUT_OFFSET, mask_sh),\
	SF_DWB2(DWB_OGAM_RAMB_REGION_32_33, DWBCP, 0, DWB_OGAM_RAMB_EXP_REGION33_NUM_SEGMENTS, mask_sh)


#define DWBC_REG_FIELD_LIST_DCN3_0(type) \
	type DWB_ENABLE;\
	type DISPCLK_R_DWB_GATE_DIS;\
	type DISPCLK_G_DWB_GATE_DIS;\
	type DWB_TEST_CLK_SEL;\
	type DWBSCL_LUT_MEM_PWR_FORCE;\
	type DWBSCL_LUT_MEM_PWR_DIS;\
	type DWBSCL_LUT_MEM_PWR_STATE;\
	type DWBSCL_LB_MEM_PWR_FORCE;\
	type DWBSCL_LB_MEM_PWR_DIS;\
	type DWBSCL_LB_MEM_PWR_STATE;\
	type DWB_OGAM_LUT_MEM_PWR_FORCE;\
	type DWB_OGAM_LUT_MEM_PWR_DIS;\
	type DWB_OGAM_LUT_MEM_PWR_STATE;\
	type FC_FRAME_CAPTURE_EN;\
	type FC_FRAME_CAPTURE_RATE;\
	type FC_WINDOW_CROP_EN;\
	type FC_EYE_SELECTION;\
	type FC_STEREO_EYE_POLARITY;\
	type FC_NEW_CONTENT;\
	type FC_FI_EN;\
	type FC_FI_PHASE;\
	type FC_FRAME_CAPTURE_EN_CURRENT;\
	type FC_FIRST_PIXEL_DELAY_COUNT;\
	type FC_WINDOW_START_X;\
	type FC_WINDOW_START_Y;\
	type FC_WINDOW_WIDTH;\
	type FC_WINDOW_HEIGHT;\
	type FC_SOURCE_WIDTH;\
	type FC_SOURCE_HEIGHT;\
	type DWB_UPDATE_LOCK;\
	type DWB_UPDATE_PENDING;\
	type DWB_CRC_EN;\
	type DWB_CRC_CONT_EN;\
	type DWB_CRC_SRC_SEL;\
	type DWB_CRC_RED_MASK;\
	type DWB_CRC_GREEN_MASK;\
	type DWB_CRC_BLUE_MASK;\
	type DWB_CRC_A_MASK;\
	type DWB_CRC_SIG_RED;\
	type DWB_CRC_SIG_GREEN;\
	type DWB_CRC_SIG_BLUE;\
	type DWB_CRC_SIG_A;\
	type OUT_FORMAT;\
	type OUT_DENORM;\
	type OUT_MAX;\
	type OUT_MIN;\
	type DWB_MMHUBBUB_BACKPRESSURE_CNT_EN;\
	type DWB_MMHUBBUB_MAX_BACKPRESSURE;\
	type DWB_HOST_READ_RATE_CONTROL;\
	type DWBSCL_DATA_OVERFLOW_FLAG;\
	type DWBSCL_DATA_OVERFLOW_ACK;\
	type DWBSCL_DATA_OVERFLOW_MASK;\
	type DWBSCL_DATA_OVERFLOW_INT_STATUS;\
	type DWBSCL_DATA_OVERFLOW_INT_TYPE;\
	type DWBSCL_DATA_OVERFLOW_TYPE;\
	type DWBSCL_DATA_OVERFLOW_OUT_X_CNT;\
	type DWBSCL_DATA_OVERFLOW_OUT_Y_CNT;\
	type DWB_SOFT_RESET;\
	type DWBSCL_COEF_RAM_TAP_PAIR_IDX;\
	type DWBSCL_COEF_RAM_PHASE;\
	type DWBSCL_COEF_RAM_FILTER_TYPE;\
	type DWBSCL_COEF_RAM_SELECT_RD;\
	type DWBSCL_COEF_RAM_EVEN_TAP_COEF;\
	type DWBSCL_COEF_RAM_EVEN_TAP_COEF_EN;\
	type DWBSCL_COEF_RAM_ODD_TAP_COEF;\
	type DWBSCL_COEF_RAM_ODD_TAP_COEF_EN;\
	type DWBSCL_MODE;\
	type DWBSCL_COEF_RAM_SELECT;\
	type DWBSCL_COEF_RAM_SELECT_CURRENT;\
	type DWBSCL_H_NUM_OF_TAPS;\
	type DWBSCL_V_NUM_OF_TAPS;\
	type DWBSCL_H_SCALE_RATIO;\
	type DWBSCL_H_INIT_FRAC;\
	type DWBSCL_H_INIT_INT;\
	type DWBSCL_V_SCALE_RATIO;\
	type DWBSCL_V_INIT_FRAC;\
	type DWBSCL_V_INIT_INT;\
	type DWBSCL_BOUNDARY_MODE;\
	type DWBSCL_BLACK_COLOR_RGB;\
	type DWBSCL_DEST_WIDTH;\
	type DWBSCL_DEST_HEIGHT;\
	type DWB_HDR_MULT_COEF;\
	type DWB_GAMUT_REMAP_MODE;\
	type DWB_GAMUT_REMAP_MODE_CURRENT;\
	type DWB_GAMUT_REMAP_COEF_FORMAT;\
	type DWB_GAMUT_REMAPA_C11;\
	type DWB_GAMUT_REMAPA_C12;\
	type DWB_GAMUT_REMAPA_C13;\
	type DWB_GAMUT_REMAPA_C14;\
	type DWB_GAMUT_REMAPA_C21;\
	type DWB_GAMUT_REMAPA_C22;\
	type DWB_GAMUT_REMAPA_C23;\
	type DWB_GAMUT_REMAPA_C24;\
	type DWB_GAMUT_REMAPA_C31;\
	type DWB_GAMUT_REMAPA_C32;\
	type DWB_GAMUT_REMAPA_C33;\
	type DWB_GAMUT_REMAPA_C34;\
	type DWB_GAMUT_REMAPB_C11;\
	type DWB_GAMUT_REMAPB_C12;\
	type DWB_GAMUT_REMAPB_C13;\
	type DWB_GAMUT_REMAPB_C14;\
	type DWB_GAMUT_REMAPB_C21;\
	type DWB_GAMUT_REMAPB_C22;\
	type DWB_GAMUT_REMAPB_C23;\
	type DWB_GAMUT_REMAPB_C24;\
	type DWB_GAMUT_REMAPB_C31;\
	type DWB_GAMUT_REMAPB_C32;\
	type DWB_GAMUT_REMAPB_C33;\
	type DWB_GAMUT_REMAPB_C34;\
	type DWB_OGAM_MODE;\
	type DWB_OGAM_SELECT;\
	type DWB_OGAM_PWL_DISABLE;\
	type DWB_OGAM_MODE_CURRENT;\
	type DWB_OGAM_SELECT_CURRENT;\
	type DWB_OGAM_LUT_INDEX;\
	type DWB_OGAM_LUT_DATA;\
	type DWB_OGAM_LUT_WRITE_COLOR_MASK;\
	type DWB_OGAM_LUT_READ_COLOR_SEL;\
	type DWB_OGAM_LUT_READ_DBG;\
	type DWB_OGAM_LUT_HOST_SEL;\
	type DWB_OGAM_LUT_CONFIG_MODE;\
	type DWB_OGAM_LUT_STATUS;\
	type DWB_OGAM_RAMA_EXP_REGION_START_B;\
	type DWB_OGAM_RAMA_EXP_REGION_START_SEGMENT_B;\
	type DWB_OGAM_RAMA_EXP_REGION_START_G;\
	type DWB_OGAM_RAMA_EXP_REGION_START_SEGMENT_G;\
	type DWB_OGAM_RAMA_EXP_REGION_START_R;\
	type DWB_OGAM_RAMA_EXP_REGION_START_SEGMENT_R;\
	type DWB_OGAM_RAMA_EXP_REGION_START_BASE_B;\
	type DWB_OGAM_RAMA_EXP_REGION_START_SLOPE_B;\
	type DWB_OGAM_RAMA_EXP_REGION_START_BASE_G;\
	type DWB_OGAM_RAMA_EXP_REGION_START_SLOPE_G;\
	type DWB_OGAM_RAMA_EXP_REGION_START_BASE_R;\
	type DWB_OGAM_RAMA_EXP_REGION_START_SLOPE_R;\
	type DWB_OGAM_RAMA_EXP_REGION_END_BASE_B;\
	type DWB_OGAM_RAMA_EXP_REGION_END_B;\
	type DWB_OGAM_RAMA_EXP_REGION_END_SLOPE_B;\
	type DWB_OGAM_RAMA_EXP_REGION_END_BASE_G;\
	type DWB_OGAM_RAMA_EXP_REGION_END_G;\
	type DWB_OGAM_RAMA_EXP_REGION_END_SLOPE_G;\
	type DWB_OGAM_RAMA_EXP_REGION_END_BASE_R;\
	type DWB_OGAM_RAMA_EXP_REGION_END_R;\
	type DWB_OGAM_RAMA_EXP_REGION_END_SLOPE_R;\
	type DWB_OGAM_RAMA_OFFSET_B;\
	type DWB_OGAM_RAMA_OFFSET_G;\
	type DWB_OGAM_RAMA_OFFSET_R;\
	type DWB_OGAM_RAMA_EXP_REGION0_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION0_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION1_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION1_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION2_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION2_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION3_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION3_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION4_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION4_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION5_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION5_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION6_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION6_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION7_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION7_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION8_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION8_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION9_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION9_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION10_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION10_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION11_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION11_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION12_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION12_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION13_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION13_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION14_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION14_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION15_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION15_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION16_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION16_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION17_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION17_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION18_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION18_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION19_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION19_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION20_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION20_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION21_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION21_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION22_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION22_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION23_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION23_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION24_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION24_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION25_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION25_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION26_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION26_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION27_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION27_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION28_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION28_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION29_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION29_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION30_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION30_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION31_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION31_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION32_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION32_NUM_SEGMENTS;\
	type DWB_OGAM_RAMA_EXP_REGION33_LUT_OFFSET;\
	type DWB_OGAM_RAMA_EXP_REGION33_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION_START_B;\
	type DWB_OGAM_RAMB_EXP_REGION_START_SEGMENT_B;\
	type DWB_OGAM_RAMB_EXP_REGION_START_G;\
	type DWB_OGAM_RAMB_EXP_REGION_START_SEGMENT_G;\
	type DWB_OGAM_RAMB_EXP_REGION_START_R;\
	type DWB_OGAM_RAMB_EXP_REGION_START_SEGMENT_R;\
	type DWB_OGAM_RAMB_EXP_REGION_START_BASE_B;\
	type DWB_OGAM_RAMB_EXP_REGION_START_SLOPE_B;\
	type DWB_OGAM_RAMB_EXP_REGION_START_BASE_G;\
	type DWB_OGAM_RAMB_EXP_REGION_START_SLOPE_G;\
	type DWB_OGAM_RAMB_EXP_REGION_START_BASE_R;\
	type DWB_OGAM_RAMB_EXP_REGION_START_SLOPE_R;\
	type DWB_OGAM_RAMB_EXP_REGION_END_BASE_B;\
	type DWB_OGAM_RAMB_EXP_REGION_END_B;\
	type DWB_OGAM_RAMB_EXP_REGION_END_SLOPE_B;\
	type DWB_OGAM_RAMB_EXP_REGION_END_BASE_G;\
	type DWB_OGAM_RAMB_EXP_REGION_END_G;\
	type DWB_OGAM_RAMB_EXP_REGION_END_SLOPE_G;\
	type DWB_OGAM_RAMB_EXP_REGION_END_BASE_R;\
	type DWB_OGAM_RAMB_EXP_REGION_END_R;\
	type DWB_OGAM_RAMB_EXP_REGION_END_SLOPE_R;\
	type DWB_OGAM_RAMB_OFFSET_B;\
	type DWB_OGAM_RAMB_OFFSET_G;\
	type DWB_OGAM_RAMB_OFFSET_R;\
	type DWB_OGAM_RAMB_EXP_REGION0_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION0_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION1_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION1_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION2_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION2_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION3_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION3_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION4_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION4_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION5_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION5_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION6_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION6_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION7_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION7_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION8_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION8_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION9_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION9_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION10_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION10_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION11_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION11_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION12_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION12_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION13_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION13_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION14_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION14_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION15_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION15_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION16_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION16_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION17_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION17_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION18_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION18_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION19_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION19_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION20_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION20_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION21_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION21_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION22_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION22_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION23_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION23_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION24_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION24_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION25_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION25_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION26_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION26_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION27_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION27_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION28_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION28_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION29_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION29_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION30_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION30_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION31_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION31_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION32_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION32_NUM_SEGMENTS;\
	type DWB_OGAM_RAMB_EXP_REGION33_LUT_OFFSET;\
	type DWB_OGAM_RAMB_EXP_REGION33_NUM_SEGMENTS;

struct dcn30_dwbc_registers {
	/* DCN3AG */
	/* DWB_TOP */
	uint32_t DWB_ENABLE_CLK_CTRL;
	uint32_t DWB_MEM_PWR_CTRL;
	uint32_t FC_MODE_CTRL;
	uint32_t FC_FLOW_CTRL;
	uint32_t FC_WINDOW_START;
	uint32_t FC_WINDOW_SIZE;
	uint32_t FC_SOURCE_SIZE;
	uint32_t DWB_UPDATE_CTRL;
	uint32_t DWB_CRC_CTRL;
	uint32_t DWB_CRC_MASK_R_G;
	uint32_t DWB_CRC_MASK_B_A;
	uint32_t DWB_CRC_VAL_R_G;
	uint32_t DWB_CRC_VAL_B_A;
	uint32_t DWB_OUT_CTRL;
	uint32_t DWB_MMHUBBUB_BACKPRESSURE_CNT_EN;
	uint32_t DWB_MMHUBBUB_BACKPRESSURE_CNT;
	uint32_t DWB_HOST_READ_CONTROL;
	uint32_t DWB_SOFT_RESET;

	/* DWBSCL */
	uint32_t DWBSCL_COEF_RAM_TAP_SELECT;
	uint32_t DWBSCL_COEF_RAM_TAP_DATA;
	uint32_t DWBSCL_MODE;
	uint32_t DWBSCL_TAP_CONTROL;
	uint32_t DWBSCL_HORZ_FILTER_SCALE_RATIO;
	uint32_t DWBSCL_HORZ_FILTER_INIT;
	uint32_t DWBSCL_VERT_FILTER_SCALE_RATIO;
	uint32_t DWBSCL_VERT_FILTER_INIT;
	uint32_t DWBSCL_BOUNDARY_CTRL;
	uint32_t DWBSCL_DEST_SIZE;
	uint32_t DWBSCL_OVERFLOW_STATUS;
	uint32_t DWBSCL_OVERFLOW_COUNTER;

	/* DWBCP */
	uint32_t DWB_HDR_MULT_COEF;
	uint32_t DWB_GAMUT_REMAP_MODE;
	uint32_t DWB_GAMUT_REMAP_COEF_FORMAT;
	uint32_t DWB_GAMUT_REMAPA_C11_C12;
	uint32_t DWB_GAMUT_REMAPA_C13_C14;
	uint32_t DWB_GAMUT_REMAPA_C21_C22;
	uint32_t DWB_GAMUT_REMAPA_C23_C24;
	uint32_t DWB_GAMUT_REMAPA_C31_C32;
	uint32_t DWB_GAMUT_REMAPA_C33_C34;
	uint32_t DWB_GAMUT_REMAPB_C11_C12;
	uint32_t DWB_GAMUT_REMAPB_C13_C14;
	uint32_t DWB_GAMUT_REMAPB_C21_C22;
	uint32_t DWB_GAMUT_REMAPB_C23_C24;
	uint32_t DWB_GAMUT_REMAPB_C31_C32;
	uint32_t DWB_GAMUT_REMAPB_C33_C34;
	uint32_t DWB_OGAM_CONTROL;
	uint32_t DWB_OGAM_LUT_INDEX;
	uint32_t DWB_OGAM_LUT_DATA;
	uint32_t DWB_OGAM_LUT_CONTROL;
	uint32_t DWB_OGAM_RAMA_START_CNTL_B;
	uint32_t DWB_OGAM_RAMA_START_CNTL_G;
	uint32_t DWB_OGAM_RAMA_START_CNTL_R;
	uint32_t DWB_OGAM_RAMA_START_BASE_CNTL_B;
	uint32_t DWB_OGAM_RAMA_START_SLOPE_CNTL_B;
	uint32_t DWB_OGAM_RAMA_START_BASE_CNTL_G;
	uint32_t DWB_OGAM_RAMA_START_SLOPE_CNTL_G;
	uint32_t DWB_OGAM_RAMA_START_BASE_CNTL_R;
	uint32_t DWB_OGAM_RAMA_START_SLOPE_CNTL_R;
	uint32_t DWB_OGAM_RAMA_END_CNTL1_B;
	uint32_t DWB_OGAM_RAMA_END_CNTL2_B;
	uint32_t DWB_OGAM_RAMA_END_CNTL1_G;
	uint32_t DWB_OGAM_RAMA_END_CNTL2_G;
	uint32_t DWB_OGAM_RAMA_END_CNTL1_R;
	uint32_t DWB_OGAM_RAMA_END_CNTL2_R;
	uint32_t DWB_OGAM_RAMA_OFFSET_B;
	uint32_t DWB_OGAM_RAMA_OFFSET_G;
	uint32_t DWB_OGAM_RAMA_OFFSET_R;
	uint32_t DWB_OGAM_RAMA_REGION_0_1;
	uint32_t DWB_OGAM_RAMA_REGION_2_3;
	uint32_t DWB_OGAM_RAMA_REGION_4_5;
	uint32_t DWB_OGAM_RAMA_REGION_6_7;
	uint32_t DWB_OGAM_RAMA_REGION_8_9;
	uint32_t DWB_OGAM_RAMA_REGION_10_11;
	uint32_t DWB_OGAM_RAMA_REGION_12_13;
	uint32_t DWB_OGAM_RAMA_REGION_14_15;
	uint32_t DWB_OGAM_RAMA_REGION_16_17;
	uint32_t DWB_OGAM_RAMA_REGION_18_19;
	uint32_t DWB_OGAM_RAMA_REGION_20_21;
	uint32_t DWB_OGAM_RAMA_REGION_22_23;
	uint32_t DWB_OGAM_RAMA_REGION_24_25;
	uint32_t DWB_OGAM_RAMA_REGION_26_27;
	uint32_t DWB_OGAM_RAMA_REGION_28_29;
	uint32_t DWB_OGAM_RAMA_REGION_30_31;
	uint32_t DWB_OGAM_RAMA_REGION_32_33;
	uint32_t DWB_OGAM_RAMB_START_CNTL_B;
	uint32_t DWB_OGAM_RAMB_START_CNTL_G;
	uint32_t DWB_OGAM_RAMB_START_CNTL_R;
	uint32_t DWB_OGAM_RAMB_START_BASE_CNTL_B;
	uint32_t DWB_OGAM_RAMB_START_SLOPE_CNTL_B;
	uint32_t DWB_OGAM_RAMB_START_BASE_CNTL_G;
	uint32_t DWB_OGAM_RAMB_START_SLOPE_CNTL_G;
	uint32_t DWB_OGAM_RAMB_START_BASE_CNTL_R;
	uint32_t DWB_OGAM_RAMB_START_SLOPE_CNTL_R;
	uint32_t DWB_OGAM_RAMB_END_CNTL1_B;
	uint32_t DWB_OGAM_RAMB_END_CNTL2_B;
	uint32_t DWB_OGAM_RAMB_END_CNTL1_G;
	uint32_t DWB_OGAM_RAMB_END_CNTL2_G;
	uint32_t DWB_OGAM_RAMB_END_CNTL1_R;
	uint32_t DWB_OGAM_RAMB_END_CNTL2_R;
	uint32_t DWB_OGAM_RAMB_OFFSET_B;
	uint32_t DWB_OGAM_RAMB_OFFSET_G;
	uint32_t DWB_OGAM_RAMB_OFFSET_R;
	uint32_t DWB_OGAM_RAMB_REGION_0_1;
	uint32_t DWB_OGAM_RAMB_REGION_2_3;
	uint32_t DWB_OGAM_RAMB_REGION_4_5;
	uint32_t DWB_OGAM_RAMB_REGION_6_7;
	uint32_t DWB_OGAM_RAMB_REGION_8_9;
	uint32_t DWB_OGAM_RAMB_REGION_10_11;
	uint32_t DWB_OGAM_RAMB_REGION_12_13;
	uint32_t DWB_OGAM_RAMB_REGION_14_15;
	uint32_t DWB_OGAM_RAMB_REGION_16_17;
	uint32_t DWB_OGAM_RAMB_REGION_18_19;
	uint32_t DWB_OGAM_RAMB_REGION_20_21;
	uint32_t DWB_OGAM_RAMB_REGION_22_23;
	uint32_t DWB_OGAM_RAMB_REGION_24_25;
	uint32_t DWB_OGAM_RAMB_REGION_26_27;
	uint32_t DWB_OGAM_RAMB_REGION_28_29;
	uint32_t DWB_OGAM_RAMB_REGION_30_31;
	uint32_t DWB_OGAM_RAMB_REGION_32_33;
};

/* Internal enums / structs */
enum dwbscl_coef_filter_type_sel {
	DWBSCL_COEF_RAM_FILTER_TYPE_VERT_RGB = 0,
	DWBSCL_COEF_RAM_FILTER_TYPE_HORZ_RGB = 1
};


struct dcn30_dwbc_mask {
	DWBC_REG_FIELD_LIST_DCN3_0(uint32_t);
};

struct dcn30_dwbc_shift {
	DWBC_REG_FIELD_LIST_DCN3_0(uint8_t);
};

struct dcn30_dwbc {
	struct dwbc base;
	const struct dcn30_dwbc_registers *dwbc_regs;
	const struct dcn30_dwbc_shift *dwbc_shift;
	const struct dcn30_dwbc_mask *dwbc_mask;
};

void dcn30_dwbc_construct(struct dcn30_dwbc *dwbc30,
	struct dc_context *ctx,
	const struct dcn30_dwbc_registers *dwbc_regs,
	const struct dcn30_dwbc_shift *dwbc_shift,
	const struct dcn30_dwbc_mask *dwbc_mask,
	int inst);

bool dwb3_enable(struct dwbc *dwbc, struct dc_dwb_params *params);

bool dwb3_disable(struct dwbc *dwbc);

bool dwb3_update(struct dwbc *dwbc, struct dc_dwb_params *params);

bool dwb3_is_enabled(struct dwbc *dwbc);

void dwb3_set_stereo(struct dwbc *dwbc,
	struct dwb_stereo_params *stereo_params);

void dwb3_set_new_content(struct dwbc *dwbc,
	bool is_new_content);

void dwb3_config_fc(struct dwbc *dwbc,
	struct dc_dwb_params *params);

void dwb3_set_denorm(struct dwbc *dwbc, struct dc_dwb_params *params);

void dwb3_program_hdr_mult(
	struct dwbc *dwbc,
	const struct dc_dwb_params *params);

void dwb3_set_gamut_remap(
	struct dwbc *dwbc,
	const struct dc_dwb_params *params);

bool dwb3_ogam_set_input_transfer_func(
	struct dwbc *dwbc,
	const struct dc_transfer_func *in_transfer_func_dwb_ogam);

void dwb3_set_host_read_rate_control(struct dwbc *dwbc, bool host_read_delay);
#endif


