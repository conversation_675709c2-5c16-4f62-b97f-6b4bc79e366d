#指定编译器为gcc
CC=gcc
#CC=arm-none-linux-gnueabi-gcc
#指定编译时的参数
AFLAGS= -c -g -m32
#指定手动链接的库
LDFLAGS= -lpthread -m32 -lpaho-mqtt3c -lrt -lm
#指定依赖文件
OBJS=main.o  mqtt.o parse_config.o cJSON.o  

#基于依赖编译生成目标demo
demo:$(OBJS)
	$(CC) -o $@ $^ $(LDFLAGS)
#指定依赖文件生成的规则
$(OBJS):%.o:%.c
	$(CC) $(AFLAGS) $< -o $@

#指定伪目标
.PHONY:clean lib
clean:
	rm *.o demo

lib:
	ar crs libmylib.a 
#gcc main.c -o server -L.. -lmylib -lpthread
