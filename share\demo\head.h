#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <signal.h>

#include <semaphore.h>
#include <arpa/inet.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/ipc.h>
#include <sys/msg.h>
#include <sys/shm.h>
#include <sys/sem.h>
#include <sys/wait.h>
#include <sys/socket.h>
#include <netinet/in.h>

#include <errno.h>
#include <time.h>
#include <fcntl.h>
#include <unistd.h>
#include <dirent.h>
#include <pthread.h>

#include "mqtt.h"
#include "parse_config.h"
#include "cJSON.h"

// 数据包类型
typedef enum {
    USER_PASS,
    TEMP_PASS,
    OPEN_DOOR,
} DataType;

typedef struct
{
    DataType type;
    int len;
    int data;
}message_data;

