/*
 * BIF_4_1 Register documentation
 *
 * Copyright (C) 2014  Advanced Micro Devices, Inc.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a
 * copy of this software and associated documentation files (the "Software"),
 * to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included
 * in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
 * THE COPYRIGHT HOLDER(S) BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN
 * AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

#ifndef BIF_4_1_D_H
#define BIF_4_1_D_H

#define mmMM_INDEX                                                              0x0
#define mmMM_INDEX_HI                                                           0x6
#define mmMM_DATA                                                               0x1
#define mmCC_BIF_BX_FUSESTRAP0							0x14D7
#define mmBUS_CNTL                                                              0x1508
#define mmCONFIG_CNTL                                                           0x1509
#define mmCONFIG_MEMSIZE                                                        0x150a
#define mmCONFIG_F0_BASE                                                        0x150b
#define mmCONFIG_APER_SIZE                                                      0x150c
#define mmCONFIG_REG_APER_SIZE                                                  0x150d
#define mmBIF_SCRATCH0                                                          0x150e
#define mmBIF_SCRATCH1                                                          0x150f
#define mmBX_RESET_EN                                                           0x1514
#define mmMM_CFGREGS_CNTL                                                       0x1513
#define mmHW_DEBUG                                                              0x1515
#define mmMASTER_CREDIT_CNTL                                                    0x1516
#define mmSLAVE_REQ_CREDIT_CNTL                                                 0x1517
#define mmBX_RESET_CNTL                                                         0x1518
#define mmINTERRUPT_CNTL                                                        0x151a
#define mmINTERRUPT_CNTL2                                                       0x151b
#define mmBIF_DEBUG_CNTL                                                        0x151c
#define mmBIF_DEBUG_MUX                                                         0x151d
#define mmBIF_DEBUG_OUT                                                         0x151e
#define mmHDP_REG_COHERENCY_FLUSH_CNTL                                          0x1528
#define mmHDP_MEM_COHERENCY_FLUSH_CNTL                                          0x1520
#define mmCLKREQB_PAD_CNTL                                                      0x1521
#define mmSMBUS_SLV_CNTL                                                        0x14fd
#define mmSMBUS_SLV_CNTL1                                                       0x14fe
#define mmSMBDAT_PAD_CNTL                                                       0x1522
#define mmSMBCLK_PAD_CNTL                                                       0x1523
#define mmBIF_XDMA_LO                                                           0x14c0
#define mmBIF_XDMA_HI                                                           0x14c1
#define mmBIF_FEATURES_CONTROL_MISC                                             0x14c2
#define mmBIF_DOORBELL_CNTL                                                     0x14c3
#define mmBIF_SLVARB_MODE                                                       0x14c4
#define mmBIF_FB_EN                                                             0x1524
#define mmBIF_BUSNUM_CNTL1                                                      0x1525
#define mmBIF_BUSNUM_LIST0                                                      0x1526
#define mmBIF_BUSNUM_LIST1                                                      0x1527
#define mmBIF_BUSNUM_CNTL2                                                      0x152b
#define mmBIF_BUSY_DELAY_CNTR                                                   0x1529
#define mmBIF_PERFMON_CNTL                                                      0x152c
#define mmBIF_PERFCOUNTER0_RESULT                                               0x152d
#define mmBIF_PERFCOUNTER1_RESULT                                               0x152e
#define mmSLAVE_HANG_PROTECTION_CNTL                                            0x1536
#define mmGPU_HDP_FLUSH_REQ                                                     0x1537
#define mmGPU_HDP_FLUSH_DONE                                                    0x1538
#define mmSLAVE_HANG_ERROR                                                      0x153b
#define mmCAPTURE_HOST_BUSNUM                                                   0x153c
#define mmHOST_BUSNUM                                                           0x153d
#define mmPEER_REG_RANGE0                                                       0x153e
#define mmPEER_REG_RANGE1                                                       0x153f
#define mmPEER0_FB_OFFSET_HI                                                    0x14f3
#define mmPEER0_FB_OFFSET_LO                                                    0x14f2
#define mmPEER1_FB_OFFSET_HI                                                    0x14f1
#define mmPEER1_FB_OFFSET_LO                                                    0x14f0
#define mmPEER2_FB_OFFSET_HI                                                    0x14ef
#define mmPEER2_FB_OFFSET_LO                                                    0x14ee
#define mmPEER3_FB_OFFSET_HI                                                    0x14ed
#define mmPEER3_FB_OFFSET_LO                                                    0x14ec
#define mmDBG_BYPASS_SRBM_ACCESS                                                0x14eb
#define mmSMBUS_BACO_DUMMY                                                      0x14c6
#define mmBIF_DEVFUNCNUM_LIST0                                                  0x14e8
#define mmBIF_DEVFUNCNUM_LIST1                                                  0x14e7
#define mmBACO_CNTL                                                             0x14e5
#define mmBF_ANA_ISO_CNTL                                                       0x14c7
#define mmMEM_TYPE_CNTL                                                         0x14e4
#define mmBIF_BACO_DEBUG                                                        0x14df
#define mmBIF_BACO_DEBUG_LATCH                                                  0x14dc
#define mmBACO_CNTL_MISC                                                        0x14db
#define mmBIF_SSA_PWR_STATUS                                                    0x14c8
#define mmBIF_SSA_GFX0_LOWER                                                    0x14ca
#define mmBIF_SSA_GFX0_UPPER                                                    0x14cb
#define mmBIF_SSA_GFX1_LOWER                                                    0x14cc
#define mmBIF_SSA_GFX1_UPPER                                                    0x14cd
#define mmBIF_SSA_GFX2_LOWER                                                    0x14ce
#define mmBIF_SSA_GFX2_UPPER                                                    0x14cf
#define mmBIF_SSA_GFX3_LOWER                                                    0x14d0
#define mmBIF_SSA_GFX3_UPPER                                                    0x14d1
#define mmBIF_SSA_DISP_LOWER                                                    0x14d2
#define mmBIF_SSA_DISP_UPPER                                                    0x14d3
#define mmBIF_SSA_MC_LOWER                                                      0x14d4
#define mmBIF_SSA_MC_UPPER                                                      0x14d5
#define mmIMPCTL_RESET                                                          0x14f5
#define mmGARLIC_FLUSH_CNTL                                                     0x1401
#define mmGARLIC_FLUSH_ADDR_START_0                                             0x1402
#define mmGARLIC_FLUSH_ADDR_START_1                                             0x1404
#define mmGARLIC_FLUSH_ADDR_START_2                                             0x1406
#define mmGARLIC_FLUSH_ADDR_START_3                                             0x1408
#define mmGARLIC_FLUSH_ADDR_START_4                                             0x140a
#define mmGARLIC_FLUSH_ADDR_START_5                                             0x140c
#define mmGARLIC_FLUSH_ADDR_START_6                                             0x140e
#define mmGARLIC_FLUSH_ADDR_START_7                                             0x1410
#define mmGARLIC_FLUSH_ADDR_END_0                                               0x1403
#define mmGARLIC_FLUSH_ADDR_END_1                                               0x1405
#define mmGARLIC_FLUSH_ADDR_END_2                                               0x1407
#define mmGARLIC_FLUSH_ADDR_END_3                                               0x1409
#define mmGARLIC_FLUSH_ADDR_END_4                                               0x140b
#define mmGARLIC_FLUSH_ADDR_END_5                                               0x140d
#define mmGARLIC_FLUSH_ADDR_END_6                                               0x140f
#define mmGARLIC_FLUSH_ADDR_END_7                                               0x1411
#define mmGARLIC_FLUSH_REQ                                                      0x1412
#define mmGPU_GARLIC_FLUSH_REQ                                                  0x1413
#define mmGPU_GARLIC_FLUSH_DONE                                                 0x1414
#define mmGARLIC_COHE_CP_RB0_WPTR                                               0x1415
#define mmGARLIC_COHE_CP_RB1_WPTR                                               0x1416
#define mmGARLIC_COHE_CP_RB2_WPTR                                               0x1417
#define mmGARLIC_COHE_UVD_RBC_RB_WPTR                                           0x1418
#define mmGARLIC_COHE_SDMA0_GFX_RB_WPTR                                         0x1419
#define mmGARLIC_COHE_SDMA1_GFX_RB_WPTR                                         0x141a
#define mmGARLIC_COHE_CP_DMA_ME_COMMAND                                         0x141b
#define mmGARLIC_COHE_CP_DMA_PFP_COMMAND                                        0x141c
#define mmGARLIC_COHE_SAM_SAB_RBI_WPTR                                          0x141d
#define mmGARLIC_COHE_SAM_SAB_RBO_WPTR                                          0x141e
#define mmGARLIC_COHE_VCE_OUT_RB_WPTR                                           0x141f
#define mmGARLIC_COHE_VCE_RB_WPTR2                                              0x1420
#define mmGARLIC_COHE_VCE_RB_WPTR                                               0x1421
#define mmBIOS_SCRATCH_0                                                        0x5c9
#define mmBIOS_SCRATCH_1                                                        0x5ca
#define mmBIOS_SCRATCH_2                                                        0x5cb
#define mmBIOS_SCRATCH_3                                                        0x5cc
#define mmBIOS_SCRATCH_4                                                        0x5cd
#define mmBIOS_SCRATCH_5                                                        0x5ce
#define mmBIOS_SCRATCH_6                                                        0x5cf
#define mmBIOS_SCRATCH_7                                                        0x5d0
#define mmBIOS_SCRATCH_8                                                        0x5d1
#define mmBIOS_SCRATCH_9                                                        0x5d2
#define mmBIOS_SCRATCH_10                                                       0x5d3
#define mmBIOS_SCRATCH_11                                                       0x5d4
#define mmBIOS_SCRATCH_12                                                       0x5d5
#define mmBIOS_SCRATCH_13                                                       0x5d6
#define mmBIOS_SCRATCH_14                                                       0x5d7
#define mmBIOS_SCRATCH_15                                                       0x5d8
#define mmVENDOR_ID                                                             0x0
#define mmDEVICE_ID                                                             0x0
#define mmCOMMAND                                                               0x1
#define mmSTATUS                                                                0x1
#define mmREVISION_ID                                                           0x2
#define mmPROG_INTERFACE                                                        0x2
#define mmSUB_CLASS                                                             0x2
#define mmBASE_CLASS                                                            0x2
#define mmCACHE_LINE                                                            0x3
#define mmLATENCY                                                               0x3
#define mmHEADER                                                                0x3
#define mmBIST                                                                  0x3
#define mmBASE_ADDR_1                                                           0x4
#define mmBASE_ADDR_2                                                           0x5
#define mmBASE_ADDR_3                                                           0x6
#define mmBASE_ADDR_4                                                           0x7
#define mmBASE_ADDR_5                                                           0x8
#define mmBASE_ADDR_6                                                           0x9
#define mmROM_BASE_ADDR                                                         0xc
#define mmCAP_PTR                                                               0xd
#define mmINTERRUPT_LINE                                                        0xf
#define mmINTERRUPT_PIN                                                         0xf
#define mmADAPTER_ID                                                            0xb
#define mmMIN_GRANT                                                             0xf
#define mmMAX_LATENCY                                                           0xf
#define mmVENDOR_CAP_LIST                                                       0x12
#define mmADAPTER_ID_W                                                          0x13
#define mmPMI_CAP_LIST                                                          0x14
#define mmPMI_CAP                                                               0x14
#define mmPMI_STATUS_CNTL                                                       0x15
#define mmPCIE_CAP_LIST                                                         0x16
#define mmPCIE_CAP                                                              0x16
#define mmDEVICE_CAP                                                            0x17
#define mmDEVICE_CNTL                                                           0x18
#define mmDEVICE_STATUS                                                         0x18
#define mmLINK_CAP                                                              0x19
#define mmLINK_CNTL                                                             0x1a
#define mmLINK_STATUS                                                           0x1a
#define mmDEVICE_CAP2                                                           0x1f
#define mmDEVICE_CNTL2                                                          0x20
#define mmDEVICE_STATUS2                                                        0x20
#define mmLINK_CAP2                                                             0x21
#define mmLINK_CNTL2                                                            0x22
#define mmLINK_STATUS2                                                          0x22
#define mmMSI_CAP_LIST                                                          0x28
#define mmMSI_MSG_CNTL                                                          0x28
#define mmMSI_MSG_ADDR_LO                                                       0x29
#define mmMSI_MSG_ADDR_HI                                                       0x2a
#define mmMSI_MSG_DATA_64                                                       0x2b
#define mmMSI_MSG_DATA                                                          0x2a
#define mmPCIE_VENDOR_SPECIFIC_ENH_CAP_LIST                                     0x40
#define mmPCIE_VENDOR_SPECIFIC_HDR                                              0x41
#define mmPCIE_VENDOR_SPECIFIC1                                                 0x42
#define mmPCIE_VENDOR_SPECIFIC2                                                 0x43
#define mmPCIE_VC_ENH_CAP_LIST                                                  0x44
#define mmPCIE_PORT_VC_CAP_REG1                                                 0x45
#define mmPCIE_PORT_VC_CAP_REG2                                                 0x46
#define mmPCIE_PORT_VC_CNTL                                                     0x47
#define mmPCIE_PORT_VC_STATUS                                                   0x47
#define mmPCIE_VC0_RESOURCE_CAP                                                 0x48
#define mmPCIE_VC0_RESOURCE_CNTL                                                0x49
#define mmPCIE_VC0_RESOURCE_STATUS                                              0x4a
#define mmPCIE_VC1_RESOURCE_CAP                                                 0x4b
#define mmPCIE_VC1_RESOURCE_CNTL                                                0x4c
#define mmPCIE_VC1_RESOURCE_STATUS                                              0x4d
#define mmPCIE_DEV_SERIAL_NUM_ENH_CAP_LIST                                      0x50
#define mmPCIE_DEV_SERIAL_NUM_DW1                                               0x51
#define mmPCIE_DEV_SERIAL_NUM_DW2                                               0x52
#define mmPCIE_ADV_ERR_RPT_ENH_CAP_LIST                                         0x54
#define mmPCIE_UNCORR_ERR_STATUS                                                0x55
#define mmPCIE_UNCORR_ERR_MASK                                                  0x56
#define mmPCIE_UNCORR_ERR_SEVERITY                                              0x57
#define mmPCIE_CORR_ERR_STATUS                                                  0x58
#define mmPCIE_CORR_ERR_MASK                                                    0x59
#define mmPCIE_ADV_ERR_CAP_CNTL                                                 0x5a
#define mmPCIE_HDR_LOG0                                                         0x5b
#define mmPCIE_HDR_LOG1                                                         0x5c
#define mmPCIE_HDR_LOG2                                                         0x5d
#define mmPCIE_HDR_LOG3                                                         0x5e
#define mmPCIE_TLP_PREFIX_LOG0                                                  0x62
#define mmPCIE_TLP_PREFIX_LOG1                                                  0x63
#define mmPCIE_TLP_PREFIX_LOG2                                                  0x64
#define mmPCIE_TLP_PREFIX_LOG3                                                  0x65
#define mmPCIE_BAR_ENH_CAP_LIST                                                 0x80
#define mmPCIE_BAR1_CAP                                                         0x81
#define mmPCIE_BAR1_CNTL                                                        0x82
#define mmPCIE_BAR2_CAP                                                         0x83
#define mmPCIE_BAR2_CNTL                                                        0x84
#define mmPCIE_BAR3_CAP                                                         0x85
#define mmPCIE_BAR3_CNTL                                                        0x86
#define mmPCIE_BAR4_CAP                                                         0x87
#define mmPCIE_BAR4_CNTL                                                        0x88
#define mmPCIE_BAR5_CAP                                                         0x89
#define mmPCIE_BAR5_CNTL                                                        0x8a
#define mmPCIE_BAR6_CAP                                                         0x8b
#define mmPCIE_BAR6_CNTL                                                        0x8c
#define mmPCIE_PWR_BUDGET_ENH_CAP_LIST                                          0x90
#define mmPCIE_PWR_BUDGET_DATA_SELECT                                           0x91
#define mmPCIE_PWR_BUDGET_DATA                                                  0x92
#define mmPCIE_PWR_BUDGET_CAP                                                   0x93
#define mmPCIE_DPA_ENH_CAP_LIST                                                 0x94
#define mmPCIE_DPA_CAP                                                          0x95
#define mmPCIE_DPA_LATENCY_INDICATOR                                            0x96
#define mmPCIE_DPA_STATUS                                                       0x97
#define mmPCIE_DPA_CNTL                                                         0x97
#define mmPCIE_DPA_SUBSTATE_PWR_ALLOC_0                                         0x98
#define mmPCIE_DPA_SUBSTATE_PWR_ALLOC_1                                         0x98
#define mmPCIE_DPA_SUBSTATE_PWR_ALLOC_2                                         0x98
#define mmPCIE_DPA_SUBSTATE_PWR_ALLOC_3                                         0x98
#define mmPCIE_DPA_SUBSTATE_PWR_ALLOC_4                                         0x99
#define mmPCIE_DPA_SUBSTATE_PWR_ALLOC_5                                         0x99
#define mmPCIE_DPA_SUBSTATE_PWR_ALLOC_6                                         0x99
#define mmPCIE_DPA_SUBSTATE_PWR_ALLOC_7                                         0x99
#define mmPCIE_SECONDARY_ENH_CAP_LIST                                           0x9c
#define mmPCIE_LINK_CNTL3                                                       0x9d
#define mmPCIE_LANE_ERROR_STATUS                                                0x9e
#define mmPCIE_LANE_0_EQUALIZATION_CNTL                                         0x9f
#define mmPCIE_LANE_1_EQUALIZATION_CNTL                                         0x9f
#define mmPCIE_LANE_2_EQUALIZATION_CNTL                                         0xa0
#define mmPCIE_LANE_3_EQUALIZATION_CNTL                                         0xa0
#define mmPCIE_LANE_4_EQUALIZATION_CNTL                                         0xa1
#define mmPCIE_LANE_5_EQUALIZATION_CNTL                                         0xa1
#define mmPCIE_LANE_6_EQUALIZATION_CNTL                                         0xa2
#define mmPCIE_LANE_7_EQUALIZATION_CNTL                                         0xa2
#define mmPCIE_LANE_8_EQUALIZATION_CNTL                                         0xa3
#define mmPCIE_LANE_9_EQUALIZATION_CNTL                                         0xa3
#define mmPCIE_LANE_10_EQUALIZATION_CNTL                                        0xa4
#define mmPCIE_LANE_11_EQUALIZATION_CNTL                                        0xa4
#define mmPCIE_LANE_12_EQUALIZATION_CNTL                                        0xa5
#define mmPCIE_LANE_13_EQUALIZATION_CNTL                                        0xa5
#define mmPCIE_LANE_14_EQUALIZATION_CNTL                                        0xa6
#define mmPCIE_LANE_15_EQUALIZATION_CNTL                                        0xa6
#define mmPCIE_ACS_ENH_CAP_LIST                                                 0xa8
#define mmPCIE_ACS_CAP                                                          0xa9
#define mmPCIE_ACS_CNTL                                                         0xa9
#define mmPCIE_ATS_ENH_CAP_LIST                                                 0xac
#define mmPCIE_ATS_CAP                                                          0xad
#define mmPCIE_ATS_CNTL                                                         0xad
#define mmPCIE_PAGE_REQ_ENH_CAP_LIST                                            0xb0
#define mmPCIE_PAGE_REQ_CNTL                                                    0xb1
#define mmPCIE_PAGE_REQ_STATUS                                                  0xb1
#define mmPCIE_OUTSTAND_PAGE_REQ_CAPACITY                                       0xb2
#define mmPCIE_OUTSTAND_PAGE_REQ_ALLOC                                          0xb3
#define mmPCIE_PASID_ENH_CAP_LIST                                               0xb4
#define mmPCIE_PASID_CAP                                                        0xb5
#define mmPCIE_PASID_CNTL                                                       0xb5
#define mmPCIE_TPH_REQR_ENH_CAP_LIST                                            0xb8
#define mmPCIE_TPH_REQR_CAP                                                     0xb9
#define mmPCIE_TPH_REQR_CNTL                                                    0xba
#define mmPCIE_MC_ENH_CAP_LIST                                                  0xbc
#define mmPCIE_MC_CAP                                                           0xbd
#define mmPCIE_MC_CNTL                                                          0xbd
#define mmPCIE_MC_ADDR0                                                         0xbe
#define mmPCIE_MC_ADDR1                                                         0xbf
#define mmPCIE_MC_RCV0                                                          0xc0
#define mmPCIE_MC_RCV1                                                          0xc1
#define mmPCIE_MC_BLOCK_ALL0                                                    0xc2
#define mmPCIE_MC_BLOCK_ALL1                                                    0xc3
#define mmPCIE_MC_BLOCK_UNTRANSLATED_0                                          0xc4
#define mmPCIE_MC_BLOCK_UNTRANSLATED_1                                          0xc5
#define mmPCIE_LTR_ENH_CAP_LIST                                                 0xc8
#define mmPCIE_LTR_CAP                                                          0xc9
#define mmPCIE_INDEX                                                            0xe
#define mmPCIE_DATA                                                             0xf
#define mmPCIE_INDEX_2                                                          0xc
#define mmPCIE_DATA_2                                                           0xd
#define ixPCIE_RESERVED                                                         0x1400000
#define ixPCIE_SCRATCH                                                          0x1400001
#define ixPCIE_HW_DEBUG                                                         0x1400002
#define ixPCIE_RX_NUM_NAK                                                       0x140000e
#define ixPCIE_RX_NUM_NAK_GENERATED                                             0x140000f
#define ixPCIE_CNTL                                                             0x1400010
#define ixPCIE_CONFIG_CNTL                                                      0x1400011
#define ixPCIE_DEBUG_CNTL                                                       0x1400012
#define ixPCIE_INT_CNTL                                                         0x140001a
#define ixPCIE_INT_STATUS                                                       0x140001b
#define ixPCIE_CNTL2                                                            0x140001c
#define ixPCIE_RX_CNTL2                                                         0x140001d
#define ixPCIE_TX_F0_ATTR_CNTL                                                  0x140001e
#define ixPCIE_TX_F1_F2_ATTR_CNTL                                               0x140001f
#define ixPCIE_CI_CNTL                                                          0x1400020
#define ixPCIE_BUS_CNTL                                                         0x1400021
#define ixPCIE_LC_STATE6                                                        0x1400022
#define ixPCIE_LC_STATE7                                                        0x1400023
#define ixPCIE_LC_STATE8                                                        0x1400024
#define ixPCIE_LC_STATE9                                                        0x1400025
#define ixPCIE_LC_STATE10                                                       0x1400026
#define ixPCIE_LC_STATE11                                                       0x1400027
#define ixPCIE_LC_STATUS1                                                       0x1400028
#define ixPCIE_LC_STATUS2                                                       0x1400029
#define ixPCIE_WPR_CNTL                                                         0x1400030
#define ixPCIE_RX_LAST_TLP0                                                     0x1400031
#define ixPCIE_RX_LAST_TLP1                                                     0x1400032
#define ixPCIE_RX_LAST_TLP2                                                     0x1400033
#define ixPCIE_RX_LAST_TLP3                                                     0x1400034
#define ixPCIE_TX_LAST_TLP0                                                     0x1400035
#define ixPCIE_TX_LAST_TLP1                                                     0x1400036
#define ixPCIE_TX_LAST_TLP2                                                     0x1400037
#define ixPCIE_TX_LAST_TLP3                                                     0x1400038
#define ixPCIE_I2C_REG_ADDR_EXPAND                                              0x140003a
#define ixPCIE_I2C_REG_DATA                                                     0x140003b
#define ixPCIE_CFG_CNTL                                                         0x140003c
#define ixPCIE_P_CNTL                                                           0x1400040
#define ixPCIE_P_BUF_STATUS                                                     0x1400041
#define ixPCIE_P_DECODER_STATUS                                                 0x1400042
#define ixPCIE_P_MISC_STATUS                                                    0x1400043
#define ixPCIE_P_RCV_L0S_FTS_DET                                                0x1400050
#define ixPCIE_OBFF_CNTL                                                        0x1400061
#define ixPCIE_TX_LTR_CNTL                                                      0x1400060
#define ixPCIE_PERF_COUNT_CNTL                                                  0x1400080
#define ixPCIE_PERF_CNTL_TXCLK                                                  0x1400081
#define ixPCIE_PERF_COUNT0_TXCLK                                                0x1400082
#define ixPCIE_PERF_COUNT1_TXCLK                                                0x1400083
#define ixPCIE_PERF_CNTL_MST_R_CLK                                              0x1400084
#define ixPCIE_PERF_COUNT0_MST_R_CLK                                            0x1400085
#define ixPCIE_PERF_COUNT1_MST_R_CLK                                            0x1400086
#define ixPCIE_PERF_CNTL_MST_C_CLK                                              0x1400087
#define ixPCIE_PERF_COUNT0_MST_C_CLK                                            0x1400088
#define ixPCIE_PERF_COUNT1_MST_C_CLK                                            0x1400089
#define ixPCIE_PERF_CNTL_SLV_R_CLK                                              0x140008a
#define ixPCIE_PERF_COUNT0_SLV_R_CLK                                            0x140008b
#define ixPCIE_PERF_COUNT1_SLV_R_CLK                                            0x140008c
#define ixPCIE_PERF_CNTL_SLV_S_C_CLK                                            0x140008d
#define ixPCIE_PERF_COUNT0_SLV_S_C_CLK                                          0x140008e
#define ixPCIE_PERF_COUNT1_SLV_S_C_CLK                                          0x140008f
#define ixPCIE_PERF_CNTL_SLV_NS_C_CLK                                           0x1400090
#define ixPCIE_PERF_COUNT0_SLV_NS_C_CLK                                         0x1400091
#define ixPCIE_PERF_COUNT1_SLV_NS_C_CLK                                         0x1400092
#define ixPCIE_PERF_CNTL_EVENT0_PORT_SEL                                        0x1400093
#define ixPCIE_PERF_CNTL_EVENT1_PORT_SEL                                        0x1400094
#define ixPCIE_PERF_CNTL_TXCLK2                                                 0x1400095
#define ixPCIE_PERF_COUNT0_TXCLK2                                               0x1400096
#define ixPCIE_PERF_COUNT1_TXCLK2                                               0x1400097
#define ixPCIE_STRAP_F0                                                         0x14000b0
#define ixPCIE_STRAP_F1                                                         0x14000b1
#define ixPCIE_STRAP_F2                                                         0x14000b2
#define ixPCIE_STRAP_F3                                                         0x14000b3
#define ixPCIE_STRAP_F4                                                         0x14000b4
#define ixPCIE_STRAP_F5                                                         0x14000b5
#define ixPCIE_STRAP_F6                                                         0x14000b6
#define ixPCIE_STRAP_F7                                                         0x14000b7
#define ixPCIE_STRAP_MISC                                                       0x14000c0
#define ixPCIE_STRAP_MISC2                                                      0x14000c1
#define ixPCIE_STRAP_PI                                                         0x14000c2
#define ixPCIE_STRAP_I2C_BD                                                     0x14000c4
#define ixPCIE_PRBS_CLR                                                         0x14000c8
#define ixPCIE_PRBS_STATUS1                                                     0x14000c9
#define ixPCIE_PRBS_STATUS2                                                     0x14000ca
#define ixPCIE_PRBS_FREERUN                                                     0x14000cb
#define ixPCIE_PRBS_MISC                                                        0x14000cc
#define ixPCIE_PRBS_USER_PATTERN                                                0x14000cd
#define ixPCIE_PRBS_LO_BITCNT                                                   0x14000ce
#define ixPCIE_PRBS_HI_BITCNT                                                   0x14000cf
#define ixPCIE_PRBS_ERRCNT_0                                                    0x14000d0
#define ixPCIE_PRBS_ERRCNT_1                                                    0x14000d1
#define ixPCIE_PRBS_ERRCNT_2                                                    0x14000d2
#define ixPCIE_PRBS_ERRCNT_3                                                    0x14000d3
#define ixPCIE_PRBS_ERRCNT_4                                                    0x14000d4
#define ixPCIE_PRBS_ERRCNT_5                                                    0x14000d5
#define ixPCIE_PRBS_ERRCNT_6                                                    0x14000d6
#define ixPCIE_PRBS_ERRCNT_7                                                    0x14000d7
#define ixPCIE_PRBS_ERRCNT_8                                                    0x14000d8
#define ixPCIE_PRBS_ERRCNT_9                                                    0x14000d9
#define ixPCIE_PRBS_ERRCNT_10                                                   0x14000da
#define ixPCIE_PRBS_ERRCNT_11                                                   0x14000db
#define ixPCIE_PRBS_ERRCNT_12                                                   0x14000dc
#define ixPCIE_PRBS_ERRCNT_13                                                   0x14000dd
#define ixPCIE_PRBS_ERRCNT_14                                                   0x14000de
#define ixPCIE_PRBS_ERRCNT_15                                                   0x14000df
#define ixPCIE_F0_DPA_CAP                                                       0x14000e0
#define ixPCIE_F0_DPA_LATENCY_INDICATOR                                         0x14000e4
#define ixPCIE_F0_DPA_CNTL                                                      0x14000e5
#define ixPCIE_F0_DPA_SUBSTATE_PWR_ALLOC_0                                      0x14000e7
#define ixPCIE_F0_DPA_SUBSTATE_PWR_ALLOC_1                                      0x14000e8
#define ixPCIE_F0_DPA_SUBSTATE_PWR_ALLOC_2                                      0x14000e9
#define ixPCIE_F0_DPA_SUBSTATE_PWR_ALLOC_3                                      0x14000ea
#define ixPCIE_F0_DPA_SUBSTATE_PWR_ALLOC_4                                      0x14000eb
#define ixPCIE_F0_DPA_SUBSTATE_PWR_ALLOC_5                                      0x14000ec
#define ixPCIE_F0_DPA_SUBSTATE_PWR_ALLOC_6                                      0x14000ed
#define ixPCIE_F0_DPA_SUBSTATE_PWR_ALLOC_7                                      0x14000ee
#define ixPCIEP_RESERVED                                                        0x10010000
#define ixPCIEP_SCRATCH                                                         0x10010001
#define ixPCIEP_HW_DEBUG                                                        0x10010002
#define ixPCIEP_PORT_CNTL                                                       0x10010010
#define ixPCIE_TX_CNTL                                                          0x10010020
#define ixPCIE_TX_REQUESTER_ID                                                  0x10010021
#define ixPCIE_TX_VENDOR_SPECIFIC                                               0x10010022
#define ixPCIE_TX_REQUEST_NUM_CNTL                                              0x10010023
#define ixPCIE_TX_SEQ                                                           0x10010024
#define ixPCIE_TX_REPLAY                                                        0x10010025
#define ixPCIE_TX_ACK_LATENCY_LIMIT                                             0x10010026
#define ixPCIE_TX_CREDITS_ADVT_P                                                0x10010030
#define ixPCIE_TX_CREDITS_ADVT_NP                                               0x10010031
#define ixPCIE_TX_CREDITS_ADVT_CPL                                              0x10010032
#define ixPCIE_TX_CREDITS_INIT_P                                                0x10010033
#define ixPCIE_TX_CREDITS_INIT_NP                                               0x10010034
#define ixPCIE_TX_CREDITS_INIT_CPL                                              0x10010035
#define ixPCIE_TX_CREDITS_STATUS                                                0x10010036
#define ixPCIE_TX_CREDITS_FCU_THRESHOLD                                         0x10010037
#define ixPCIE_P_PORT_LANE_STATUS                                               0x10010050
#define ixPCIE_FC_P                                                             0x10010060
#define ixPCIE_FC_NP                                                            0x10010061
#define ixPCIE_FC_CPL                                                           0x10010062
#define ixPCIE_ERR_CNTL                                                         0x1001006a
#define ixPCIE_RX_CNTL                                                          0x10010070
#define ixPCIE_RX_EXPECTED_SEQNUM                                               0x10010071
#define ixPCIE_RX_VENDOR_SPECIFIC                                               0x10010072
#define ixPCIE_RX_CNTL3                                                         0x10010074
#define ixPCIE_RX_CREDITS_ALLOCATED_P                                           0x10010080
#define ixPCIE_RX_CREDITS_ALLOCATED_NP                                          0x10010081
#define ixPCIE_RX_CREDITS_ALLOCATED_CPL                                         0x10010082
#define ixPCIE_LC_CNTL                                                          0x100100a0
#define ixPCIE_LC_CNTL2                                                         0x100100b1
#define ixPCIE_LC_CNTL3                                                         0x100100b5
#define ixPCIE_LC_CNTL4                                                         0x100100b6
#define ixPCIE_LC_CNTL5                                                         0x100100b7
#define ixPCIE_LC_BW_CHANGE_CNTL                                                0x100100b2
#define ixPCIE_LC_TRAINING_CNTL                                                 0x100100a1
#define ixPCIE_LC_LINK_WIDTH_CNTL                                               0x100100a2
#define ixPCIE_LC_N_FTS_CNTL                                                    0x100100a3
#define ixPCIE_LC_SPEED_CNTL                                                    0x100100a4
#define ixPCIE_LC_CDR_CNTL                                                      0x100100b3
#define ixPCIE_LC_LANE_CNTL                                                     0x100100b4
#define ixPCIE_LC_FORCE_COEFF                                                   0x100100b8
#define ixPCIE_LC_BEST_EQ_SETTINGS                                              0x100100b9
#define ixPCIE_LC_FORCE_EQ_REQ_COEFF                                            0x100100ba
#define ixPCIE_LC_STATE0                                                        0x100100a5
#define ixPCIE_LC_STATE1                                                        0x100100a6
#define ixPCIE_LC_STATE2                                                        0x100100a7
#define ixPCIE_LC_STATE3                                                        0x100100a8
#define ixPCIE_LC_STATE4                                                        0x100100a9
#define ixPCIE_LC_STATE5                                                        0x100100aa
#define ixPCIEP_STRAP_LC                                                        0x100100c0
#define ixPCIEP_STRAP_MISC                                                      0x100100c1
#define ixPCIEP_BCH_ECC_CNTL                                                    0x100100d0
#define ixPB0_GLB_CTRL_REG0                                                     0x1200004
#define ixPB0_GLB_CTRL_REG1                                                     0x1200008
#define ixPB0_GLB_CTRL_REG2                                                     0x120000c
#define ixPB0_GLB_CTRL_REG3                                                     0x1200010
#define ixPB0_GLB_CTRL_REG4                                                     0x1200014
#define ixPB0_GLB_CTRL_REG5                                                     0x1200018
#define ixPB0_GLB_SCI_STAT_OVRD_REG0                                            0x120001c
#define ixPB0_GLB_SCI_STAT_OVRD_REG1                                            0x1200020
#define ixPB0_GLB_SCI_STAT_OVRD_REG2                                            0x1200024
#define ixPB0_GLB_SCI_STAT_OVRD_REG3                                            0x1200028
#define ixPB0_GLB_SCI_STAT_OVRD_REG4                                            0x120002c
#define ixPB0_GLB_OVRD_REG0                                                     0x1200030
#define ixPB0_GLB_OVRD_REG1                                                     0x1200034
#define ixPB0_GLB_OVRD_REG2                                                     0x1200038
#define ixPB0_HW_DEBUG                                                          0x1202004
#define ixPB0_STRAP_GLB_REG0                                                    0x1202020
#define ixPB0_STRAP_TX_REG0                                                     0x1202024
#define ixPB0_STRAP_RX_REG0                                                     0x1202028
#define ixPB0_STRAP_RX_REG1                                                     0x120202c
#define ixPB0_STRAP_PLL_REG0                                                    0x1202030
#define ixPB0_STRAP_PIN_REG0                                                    0x1202034
#define ixPB0_DFT_JIT_INJ_REG0                                                  0x1203000
#define ixPB0_DFT_JIT_INJ_REG1                                                  0x1203004
#define ixPB0_DFT_JIT_INJ_REG2                                                  0x1203008
#define ixPB0_DFT_DEBUG_CTRL_REG0                                               0x120300c
#define ixPB0_DFT_JIT_INJ_STAT_REG0                                             0x1203010
#define ixPB0_PLL_RO_GLB_CTRL_REG0                                              0x1204000
#define ixPB0_PLL_RO_GLB_OVRD_REG0                                              0x1204010
#define ixPB0_PLL_RO0_CTRL_REG0                                                 0x1204440
#define ixPB0_PLL_RO0_OVRD_REG0                                                 0x1204450
#define ixPB0_PLL_RO0_OVRD_REG1                                                 0x1204454
#define ixPB0_PLL_RO0_SCI_STAT_OVRD_REG0                                        0x1204460
#define ixPB0_PLL_RO1_SCI_STAT_OVRD_REG0                                        0x1204464
#define ixPB0_PLL_RO2_SCI_STAT_OVRD_REG0                                        0x1204468
#define ixPB0_PLL_RO3_SCI_STAT_OVRD_REG0                                        0x120446c
#define ixPB0_PLL_LC0_CTRL_REG0                                                 0x1204480
#define ixPB0_PLL_LC0_OVRD_REG0                                                 0x1204490
#define ixPB0_PLL_LC0_OVRD_REG1                                                 0x1204494
#define ixPB0_PLL_LC0_SCI_STAT_OVRD_REG0                                        0x1204500
#define ixPB0_PLL_LC1_SCI_STAT_OVRD_REG0                                        0x1204504
#define ixPB0_PLL_LC2_SCI_STAT_OVRD_REG0                                        0x1204508
#define ixPB0_PLL_LC3_SCI_STAT_OVRD_REG0                                        0x120450c
#define ixPB0_RX_GLB_CTRL_REG0                                                  0x1206000
#define ixPB0_RX_GLB_CTRL_REG1                                                  0x1206004
#define ixPB0_RX_GLB_CTRL_REG2                                                  0x1206008
#define ixPB0_RX_GLB_CTRL_REG3                                                  0x120600c
#define ixPB0_RX_GLB_CTRL_REG4                                                  0x1206010
#define ixPB0_RX_GLB_CTRL_REG5                                                  0x1206014
#define ixPB0_RX_GLB_CTRL_REG6                                                  0x1206018
#define ixPB0_RX_GLB_CTRL_REG7                                                  0x120601c
#define ixPB0_RX_GLB_CTRL_REG8                                                  0x1206020
#define ixPB0_RX_GLB_SCI_STAT_OVRD_REG0                                         0x1206028
#define ixPB0_RX_GLB_OVRD_REG0                                                  0x1206030
#define ixPB0_RX_GLB_OVRD_REG1                                                  0x1206034
#define ixPB0_RX_LANE0_CTRL_REG0                                                0x1206440
#define ixPB0_RX_LANE0_SCI_STAT_OVRD_REG0                                       0x1206448
#define ixPB0_RX_LANE1_CTRL_REG0                                                0x1206480
#define ixPB0_RX_LANE1_SCI_STAT_OVRD_REG0                                       0x1206488
#define ixPB0_RX_LANE2_CTRL_REG0                                                0x1206500
#define ixPB0_RX_LANE2_SCI_STAT_OVRD_REG0                                       0x1206508
#define ixPB0_RX_LANE3_CTRL_REG0                                                0x1206600
#define ixPB0_RX_LANE3_SCI_STAT_OVRD_REG0                                       0x1206608
#define ixPB0_RX_LANE4_CTRL_REG0                                                0x1206800
#define ixPB0_RX_LANE4_SCI_STAT_OVRD_REG0                                       0x1206848
#define ixPB0_RX_LANE5_CTRL_REG0                                                0x1206880
#define ixPB0_RX_LANE5_SCI_STAT_OVRD_REG0                                       0x1206888
#define ixPB0_RX_LANE6_CTRL_REG0                                                0x1206900
#define ixPB0_RX_LANE6_SCI_STAT_OVRD_REG0                                       0x1206908
#define ixPB0_RX_LANE7_CTRL_REG0                                                0x1206a00
#define ixPB0_RX_LANE7_SCI_STAT_OVRD_REG0                                       0x1206a08
#define ixPB0_RX_LANE8_CTRL_REG0                                                0x1207440
#define ixPB0_RX_LANE8_SCI_STAT_OVRD_REG0                                       0x1207448
#define ixPB0_RX_LANE9_CTRL_REG0                                                0x1207480
#define ixPB0_RX_LANE9_SCI_STAT_OVRD_REG0                                       0x1207488
#define ixPB0_RX_LANE10_CTRL_REG0                                               0x1207500
#define ixPB0_RX_LANE10_SCI_STAT_OVRD_REG0                                      0x1207508
#define ixPB0_RX_LANE11_CTRL_REG0                                               0x1207600
#define ixPB0_RX_LANE11_SCI_STAT_OVRD_REG0                                      0x1207608
#define ixPB0_RX_LANE12_CTRL_REG0                                               0x1207840
#define ixPB0_RX_LANE12_SCI_STAT_OVRD_REG0                                      0x1207848
#define ixPB0_RX_LANE13_CTRL_REG0                                               0x1207880
#define ixPB0_RX_LANE13_SCI_STAT_OVRD_REG0                                      0x1207888
#define ixPB0_RX_LANE14_CTRL_REG0                                               0x1207900
#define ixPB0_RX_LANE14_SCI_STAT_OVRD_REG0                                      0x1207908
#define ixPB0_RX_LANE15_CTRL_REG0                                               0x1207a00
#define ixPB0_RX_LANE15_SCI_STAT_OVRD_REG0                                      0x1207a08
#define ixPB0_TX_GLB_CTRL_REG0                                                  0x1208000
#define ixPB0_TX_GLB_LANE_SKEW_CTRL                                             0x1208004
#define ixPB0_TX_GLB_SCI_STAT_OVRD_REG0                                         0x1208010
#define ixPB0_TX_GLB_COEFF_ACCEPT_TABLE_REG0                                    0x1208014
#define ixPB0_TX_GLB_COEFF_ACCEPT_TABLE_REG1                                    0x1208018
#define ixPB0_TX_GLB_COEFF_ACCEPT_TABLE_REG2                                    0x120801c
#define ixPB0_TX_GLB_COEFF_ACCEPT_TABLE_REG3                                    0x1208020
#define ixPB0_TX_GLB_OVRD_REG0                                                  0x1208030
#define ixPB0_TX_GLB_OVRD_REG1                                                  0x1208034
#define ixPB0_TX_GLB_OVRD_REG2                                                  0x1208038
#define ixPB0_TX_GLB_OVRD_REG3                                                  0x120803c
#define ixPB0_TX_GLB_OVRD_REG4                                                  0x1208040
#define ixPB0_TX_LANE0_CTRL_REG0                                                0x1208440
#define ixPB0_TX_LANE0_OVRD_REG0                                                0x1208444
#define ixPB0_TX_LANE0_SCI_STAT_OVRD_REG0                                       0x1208448
#define ixPB0_TX_LANE1_CTRL_REG0                                                0x1208480
#define ixPB0_TX_LANE1_OVRD_REG0                                                0x1208484
#define ixPB0_TX_LANE1_SCI_STAT_OVRD_REG0                                       0x1208488
#define ixPB0_TX_LANE2_CTRL_REG0                                                0x1208500
#define ixPB0_TX_LANE2_OVRD_REG0                                                0x1208504
#define ixPB0_TX_LANE2_SCI_STAT_OVRD_REG0                                       0x1208508
#define ixPB0_TX_LANE3_CTRL_REG0                                                0x1208600
#define ixPB0_TX_LANE3_OVRD_REG0                                                0x1208604
#define ixPB0_TX_LANE3_SCI_STAT_OVRD_REG0                                       0x1208608
#define ixPB0_TX_LANE4_CTRL_REG0                                                0x1208840
#define ixPB0_TX_LANE4_OVRD_REG0                                                0x1208844
#define ixPB0_TX_LANE4_SCI_STAT_OVRD_REG0                                       0x1208848
#define ixPB0_TX_LANE5_CTRL_REG0                                                0x1208880
#define ixPB0_TX_LANE5_OVRD_REG0                                                0x1208884
#define ixPB0_TX_LANE5_SCI_STAT_OVRD_REG0                                       0x1208888
#define ixPB0_TX_LANE6_CTRL_REG0                                                0x1208900
#define ixPB0_TX_LANE6_OVRD_REG0                                                0x1208904
#define ixPB0_TX_LANE6_SCI_STAT_OVRD_REG0                                       0x1208908
#define ixPB0_TX_LANE7_CTRL_REG0                                                0x1208a00
#define ixPB0_TX_LANE7_OVRD_REG0                                                0x1208a04
#define ixPB0_TX_LANE7_SCI_STAT_OVRD_REG0                                       0x1208a08
#define ixPB0_TX_LANE8_CTRL_REG0                                                0x1209440
#define ixPB0_TX_LANE8_OVRD_REG0                                                0x1209444
#define ixPB0_TX_LANE8_SCI_STAT_OVRD_REG0                                       0x1209448
#define ixPB0_TX_LANE9_CTRL_REG0                                                0x1209480
#define ixPB0_TX_LANE9_OVRD_REG0                                                0x1209484
#define ixPB0_TX_LANE9_SCI_STAT_OVRD_REG0                                       0x1209488
#define ixPB0_TX_LANE10_CTRL_REG0                                               0x1209500
#define ixPB0_TX_LANE10_OVRD_REG0                                               0x1209504
#define ixPB0_TX_LANE10_SCI_STAT_OVRD_REG0                                      0x1209508
#define ixPB0_TX_LANE11_CTRL_REG0                                               0x1209600
#define ixPB0_TX_LANE11_OVRD_REG0                                               0x1209604
#define ixPB0_TX_LANE11_SCI_STAT_OVRD_REG0                                      0x1209608
#define ixPB0_TX_LANE12_CTRL_REG0                                               0x1209840
#define ixPB0_TX_LANE12_OVRD_REG0                                               0x1209844
#define ixPB0_TX_LANE12_SCI_STAT_OVRD_REG0                                      0x1209848
#define ixPB0_TX_LANE13_CTRL_REG0                                               0x1209880
#define ixPB0_TX_LANE13_OVRD_REG0                                               0x1209884
#define ixPB0_TX_LANE13_SCI_STAT_OVRD_REG0                                      0x1209888
#define ixPB0_TX_LANE14_CTRL_REG0                                               0x1209900
#define ixPB0_TX_LANE14_OVRD_REG0                                               0x1209904
#define ixPB0_TX_LANE14_SCI_STAT_OVRD_REG0                                      0x1209908
#define ixPB0_TX_LANE15_CTRL_REG0                                               0x1209a00
#define ixPB0_TX_LANE15_OVRD_REG0                                               0x1209a04
#define ixPB0_TX_LANE15_SCI_STAT_OVRD_REG0                                      0x1209a08
#define ixPB1_GLB_CTRL_REG0                                                     0x2200004
#define ixPB1_GLB_CTRL_REG1                                                     0x2200008
#define ixPB1_GLB_CTRL_REG2                                                     0x220000c
#define ixPB1_GLB_CTRL_REG3                                                     0x2200010
#define ixPB1_GLB_CTRL_REG4                                                     0x2200014
#define ixPB1_GLB_CTRL_REG5                                                     0x2200018
#define ixPB1_GLB_SCI_STAT_OVRD_REG0                                            0x220001c
#define ixPB1_GLB_SCI_STAT_OVRD_REG1                                            0x2200020
#define ixPB1_GLB_SCI_STAT_OVRD_REG2                                            0x2200024
#define ixPB1_GLB_SCI_STAT_OVRD_REG3                                            0x2200028
#define ixPB1_GLB_SCI_STAT_OVRD_REG4                                            0x220002c
#define ixPB1_GLB_OVRD_REG0                                                     0x2200030
#define ixPB1_GLB_OVRD_REG1                                                     0x2200034
#define ixPB1_GLB_OVRD_REG2                                                     0x2200038
#define ixPB1_HW_DEBUG                                                          0x2202004
#define ixPB1_STRAP_GLB_REG0                                                    0x2202020
#define ixPB1_STRAP_TX_REG0                                                     0x2202024
#define ixPB1_STRAP_RX_REG0                                                     0x2202028
#define ixPB1_STRAP_RX_REG1                                                     0x220202c
#define ixPB1_STRAP_PLL_REG0                                                    0x2202030
#define ixPB1_STRAP_PIN_REG0                                                    0x2202034
#define ixPB1_DFT_JIT_INJ_REG0                                                  0x2203000
#define ixPB1_DFT_JIT_INJ_REG1                                                  0x2203004
#define ixPB1_DFT_JIT_INJ_REG2                                                  0x2203008
#define ixPB1_DFT_DEBUG_CTRL_REG0                                               0x220300c
#define ixPB1_DFT_JIT_INJ_STAT_REG0                                             0x2203010
#define ixPB1_PLL_RO_GLB_CTRL_REG0                                              0x2204000
#define ixPB1_PLL_RO_GLB_OVRD_REG0                                              0x2204010
#define ixPB1_PLL_RO0_CTRL_REG0                                                 0x2204440
#define ixPB1_PLL_RO0_OVRD_REG0                                                 0x2204450
#define ixPB1_PLL_RO0_OVRD_REG1                                                 0x2204454
#define ixPB1_PLL_RO0_SCI_STAT_OVRD_REG0                                        0x2204460
#define ixPB1_PLL_RO1_SCI_STAT_OVRD_REG0                                        0x2204464
#define ixPB1_PLL_RO2_SCI_STAT_OVRD_REG0                                        0x2204468
#define ixPB1_PLL_RO3_SCI_STAT_OVRD_REG0                                        0x220446c
#define ixPB1_PLL_LC0_CTRL_REG0                                                 0x2204480
#define ixPB1_PLL_LC0_OVRD_REG0                                                 0x2204490
#define ixPB1_PLL_LC0_OVRD_REG1                                                 0x2204494
#define ixPB1_PLL_LC0_SCI_STAT_OVRD_REG0                                        0x2204500
#define ixPB1_PLL_LC1_SCI_STAT_OVRD_REG0                                        0x2204504
#define ixPB1_PLL_LC2_SCI_STAT_OVRD_REG0                                        0x2204508
#define ixPB1_PLL_LC3_SCI_STAT_OVRD_REG0                                        0x220450c
#define ixPB1_RX_GLB_CTRL_REG0                                                  0x2206000
#define ixPB1_RX_GLB_CTRL_REG1                                                  0x2206004
#define ixPB1_RX_GLB_CTRL_REG2                                                  0x2206008
#define ixPB1_RX_GLB_CTRL_REG3                                                  0x220600c
#define ixPB1_RX_GLB_CTRL_REG4                                                  0x2206010
#define ixPB1_RX_GLB_CTRL_REG5                                                  0x2206014
#define ixPB1_RX_GLB_CTRL_REG6                                                  0x2206018
#define ixPB1_RX_GLB_CTRL_REG7                                                  0x220601c
#define ixPB1_RX_GLB_CTRL_REG8                                                  0x2206020
#define ixPB1_RX_GLB_SCI_STAT_OVRD_REG0                                         0x2206028
#define ixPB1_RX_GLB_OVRD_REG0                                                  0x2206030
#define ixPB1_RX_GLB_OVRD_REG1                                                  0x2206034
#define ixPB1_RX_LANE0_CTRL_REG0                                                0x2206440
#define ixPB1_RX_LANE0_SCI_STAT_OVRD_REG0                                       0x2206448
#define ixPB1_RX_LANE1_CTRL_REG0                                                0x2206480
#define ixPB1_RX_LANE1_SCI_STAT_OVRD_REG0                                       0x2206488
#define ixPB1_RX_LANE2_CTRL_REG0                                                0x2206500
#define ixPB1_RX_LANE2_SCI_STAT_OVRD_REG0                                       0x2206508
#define ixPB1_RX_LANE3_CTRL_REG0                                                0x2206600
#define ixPB1_RX_LANE3_SCI_STAT_OVRD_REG0                                       0x2206608
#define ixPB1_RX_LANE4_CTRL_REG0                                                0x2206800
#define ixPB1_RX_LANE4_SCI_STAT_OVRD_REG0                                       0x2206848
#define ixPB1_RX_LANE5_CTRL_REG0                                                0x2206880
#define ixPB1_RX_LANE5_SCI_STAT_OVRD_REG0                                       0x2206888
#define ixPB1_RX_LANE6_CTRL_REG0                                                0x2206900
#define ixPB1_RX_LANE6_SCI_STAT_OVRD_REG0                                       0x2206908
#define ixPB1_RX_LANE7_CTRL_REG0                                                0x2206a00
#define ixPB1_RX_LANE7_SCI_STAT_OVRD_REG0                                       0x2206a08
#define ixPB1_RX_LANE8_CTRL_REG0                                                0x2207440
#define ixPB1_RX_LANE8_SCI_STAT_OVRD_REG0                                       0x2207448
#define ixPB1_RX_LANE9_CTRL_REG0                                                0x2207480
#define ixPB1_RX_LANE9_SCI_STAT_OVRD_REG0                                       0x2207488
#define ixPB1_RX_LANE10_CTRL_REG0                                               0x2207500
#define ixPB1_RX_LANE10_SCI_STAT_OVRD_REG0                                      0x2207508
#define ixPB1_RX_LANE11_CTRL_REG0                                               0x2207600
#define ixPB1_RX_LANE11_SCI_STAT_OVRD_REG0                                      0x2207608
#define ixPB1_RX_LANE12_CTRL_REG0                                               0x2207840
#define ixPB1_RX_LANE12_SCI_STAT_OVRD_REG0                                      0x2207848
#define ixPB1_RX_LANE13_CTRL_REG0                                               0x2207880
#define ixPB1_RX_LANE13_SCI_STAT_OVRD_REG0                                      0x2207888
#define ixPB1_RX_LANE14_CTRL_REG0                                               0x2207900
#define ixPB1_RX_LANE14_SCI_STAT_OVRD_REG0                                      0x2207908
#define ixPB1_RX_LANE15_CTRL_REG0                                               0x2207a00
#define ixPB1_RX_LANE15_SCI_STAT_OVRD_REG0                                      0x2207a08
#define ixPB1_TX_GLB_CTRL_REG0                                                  0x2208000
#define ixPB1_TX_GLB_LANE_SKEW_CTRL                                             0x2208004
#define ixPB1_TX_GLB_SCI_STAT_OVRD_REG0                                         0x2208010
#define ixPB1_TX_GLB_COEFF_ACCEPT_TABLE_REG0                                    0x2208014
#define ixPB1_TX_GLB_COEFF_ACCEPT_TABLE_REG1                                    0x2208018
#define ixPB1_TX_GLB_COEFF_ACCEPT_TABLE_REG2                                    0x220801c
#define ixPB1_TX_GLB_COEFF_ACCEPT_TABLE_REG3                                    0x2208020
#define ixPB1_TX_GLB_OVRD_REG0                                                  0x2208030
#define ixPB1_TX_GLB_OVRD_REG1                                                  0x2208034
#define ixPB1_TX_GLB_OVRD_REG2                                                  0x2208038
#define ixPB1_TX_GLB_OVRD_REG3                                                  0x220803c
#define ixPB1_TX_GLB_OVRD_REG4                                                  0x2208040
#define ixPB1_TX_LANE0_CTRL_REG0                                                0x2208440
#define ixPB1_TX_LANE0_OVRD_REG0                                                0x2208444
#define ixPB1_TX_LANE0_SCI_STAT_OVRD_REG0                                       0x2208448
#define ixPB1_TX_LANE1_CTRL_REG0                                                0x2208480
#define ixPB1_TX_LANE1_OVRD_REG0                                                0x2208484
#define ixPB1_TX_LANE1_SCI_STAT_OVRD_REG0                                       0x2208488
#define ixPB1_TX_LANE2_CTRL_REG0                                                0x2208500
#define ixPB1_TX_LANE2_OVRD_REG0                                                0x2208504
#define ixPB1_TX_LANE2_SCI_STAT_OVRD_REG0                                       0x2208508
#define ixPB1_TX_LANE3_CTRL_REG0                                                0x2208600
#define ixPB1_TX_LANE3_OVRD_REG0                                                0x2208604
#define ixPB1_TX_LANE3_SCI_STAT_OVRD_REG0                                       0x2208608
#define ixPB1_TX_LANE4_CTRL_REG0                                                0x2208840
#define ixPB1_TX_LANE4_OVRD_REG0                                                0x2208844
#define ixPB1_TX_LANE4_SCI_STAT_OVRD_REG0                                       0x2208848
#define ixPB1_TX_LANE5_CTRL_REG0                                                0x2208880
#define ixPB1_TX_LANE5_OVRD_REG0                                                0x2208884
#define ixPB1_TX_LANE5_SCI_STAT_OVRD_REG0                                       0x2208888
#define ixPB1_TX_LANE6_CTRL_REG0                                                0x2208900
#define ixPB1_TX_LANE6_OVRD_REG0                                                0x2208904
#define ixPB1_TX_LANE6_SCI_STAT_OVRD_REG0                                       0x2208908
#define ixPB1_TX_LANE7_CTRL_REG0                                                0x2208a00
#define ixPB1_TX_LANE7_OVRD_REG0                                                0x2208a04
#define ixPB1_TX_LANE7_SCI_STAT_OVRD_REG0                                       0x2208a08
#define ixPB1_TX_LANE8_CTRL_REG0                                                0x2209440
#define ixPB1_TX_LANE8_OVRD_REG0                                                0x2209444
#define ixPB1_TX_LANE8_SCI_STAT_OVRD_REG0                                       0x2209448
#define ixPB1_TX_LANE9_CTRL_REG0                                                0x2209480
#define ixPB1_TX_LANE9_OVRD_REG0                                                0x2209484
#define ixPB1_TX_LANE9_SCI_STAT_OVRD_REG0                                       0x2209488
#define ixPB1_TX_LANE10_CTRL_REG0                                               0x2209500
#define ixPB1_TX_LANE10_OVRD_REG0                                               0x2209504
#define ixPB1_TX_LANE10_SCI_STAT_OVRD_REG0                                      0x2209508
#define ixPB1_TX_LANE11_CTRL_REG0                                               0x2209600
#define ixPB1_TX_LANE11_OVRD_REG0                                               0x2209604
#define ixPB1_TX_LANE11_SCI_STAT_OVRD_REG0                                      0x2209608
#define ixPB1_TX_LANE12_CTRL_REG0                                               0x2209840
#define ixPB1_TX_LANE12_OVRD_REG0                                               0x2209844
#define ixPB1_TX_LANE12_SCI_STAT_OVRD_REG0                                      0x2209848
#define ixPB1_TX_LANE13_CTRL_REG0                                               0x2209880
#define ixPB1_TX_LANE13_OVRD_REG0                                               0x2209884
#define ixPB1_TX_LANE13_SCI_STAT_OVRD_REG0                                      0x2209888
#define ixPB1_TX_LANE14_CTRL_REG0                                               0x2209900
#define ixPB1_TX_LANE14_OVRD_REG0                                               0x2209904
#define ixPB1_TX_LANE14_SCI_STAT_OVRD_REG0                                      0x2209908
#define ixPB1_TX_LANE15_CTRL_REG0                                               0x2209a00
#define ixPB1_TX_LANE15_OVRD_REG0                                               0x2209a04
#define ixPB1_TX_LANE15_SCI_STAT_OVRD_REG0                                      0x2209a08
#define ixPB0_PIF_SCRATCH                                                       0x1100001
#define ixPB0_PIF_HW_DEBUG                                                      0x1100002
#define ixPB0_PIF_PRG6                                                          0x1100003
#define ixPB0_PIF_PRG7                                                          0x1100004
#define ixPB0_PIF_CNTL                                                          0x1100010
#define ixPB0_PIF_PAIRING                                                       0x1100011
#define ixPB0_PIF_PWRDOWN_0                                                     0x1100012
#define ixPB0_PIF_PWRDOWN_1                                                     0x1100013
#define ixPB0_PIF_CNTL2                                                         0x1100014
#define ixPB0_PIF_TXPHYSTATUS                                                   0x1100015
#define ixPB0_PIF_SC_CTL                                                        0x1100016
#define ixPB0_PIF_PWRDOWN_2                                                     0x1100017
#define ixPB0_PIF_PWRDOWN_3                                                     0x1100018
#define ixPB0_PIF_SC_CTL2                                                       0x1100019
#define ixPB0_PIF_PRG0                                                          0x110001a
#define ixPB0_PIF_PRG1                                                          0x110001b
#define ixPB0_PIF_PRG2                                                          0x110001c
#define ixPB0_PIF_PRG3                                                          0x110001d
#define ixPB0_PIF_PRG4                                                          0x110001e
#define ixPB0_PIF_PRG5                                                          0x110001f
#define ixPB0_PIF_PDNB_OVERRIDE_0                                               0x1100020
#define ixPB0_PIF_PDNB_OVERRIDE_1                                               0x1100021
#define ixPB0_PIF_PDNB_OVERRIDE_2                                               0x1100022
#define ixPB0_PIF_PDNB_OVERRIDE_3                                               0x1100023
#define ixPB0_PIF_PDNB_OVERRIDE_4                                               0x1100024
#define ixPB0_PIF_PDNB_OVERRIDE_5                                               0x1100025
#define ixPB0_PIF_PDNB_OVERRIDE_6                                               0x1100026
#define ixPB0_PIF_PDNB_OVERRIDE_7                                               0x1100027
#define ixPB0_PIF_SEQ_STATUS_0                                                  0x1100028
#define ixPB0_PIF_SEQ_STATUS_1                                                  0x1100029
#define ixPB0_PIF_SEQ_STATUS_2                                                  0x110002a
#define ixPB0_PIF_SEQ_STATUS_3                                                  0x110002b
#define ixPB0_PIF_SEQ_STATUS_4                                                  0x110002c
#define ixPB0_PIF_SEQ_STATUS_5                                                  0x110002d
#define ixPB0_PIF_SEQ_STATUS_6                                                  0x110002e
#define ixPB0_PIF_SEQ_STATUS_7                                                  0x110002f
#define ixPB0_PIF_PDNB_OVERRIDE_8                                               0x1100030
#define ixPB0_PIF_PDNB_OVERRIDE_9                                               0x1100031
#define ixPB0_PIF_PDNB_OVERRIDE_10                                              0x1100032
#define ixPB0_PIF_PDNB_OVERRIDE_11                                              0x1100033
#define ixPB0_PIF_PDNB_OVERRIDE_12                                              0x1100034
#define ixPB0_PIF_PDNB_OVERRIDE_13                                              0x1100035
#define ixPB0_PIF_PDNB_OVERRIDE_14                                              0x1100036
#define ixPB0_PIF_PDNB_OVERRIDE_15                                              0x1100037
#define ixPB0_PIF_SEQ_STATUS_8                                                  0x1100038
#define ixPB0_PIF_SEQ_STATUS_9                                                  0x1100039
#define ixPB0_PIF_SEQ_STATUS_10                                                 0x110003a
#define ixPB0_PIF_SEQ_STATUS_11                                                 0x110003b
#define ixPB0_PIF_SEQ_STATUS_12                                                 0x110003c
#define ixPB0_PIF_SEQ_STATUS_13                                                 0x110003d
#define ixPB0_PIF_SEQ_STATUS_14                                                 0x110003e
#define ixPB0_PIF_SEQ_STATUS_15                                                 0x110003f
#define ixPB1_PIF_SCRATCH                                                       0x2100001
#define ixPB1_PIF_HW_DEBUG                                                      0x2100002
#define ixPB1_PIF_PRG6                                                          0x2100003
#define ixPB1_PIF_PRG7                                                          0x2100004
#define ixPB1_PIF_CNTL                                                          0x2100010
#define ixPB1_PIF_PAIRING                                                       0x2100011
#define ixPB1_PIF_PWRDOWN_0                                                     0x2100012
#define ixPB1_PIF_PWRDOWN_1                                                     0x2100013
#define ixPB1_PIF_CNTL2                                                         0x2100014
#define ixPB1_PIF_TXPHYSTATUS                                                   0x2100015
#define ixPB1_PIF_SC_CTL                                                        0x2100016
#define ixPB1_PIF_PWRDOWN_2                                                     0x2100017
#define ixPB1_PIF_PWRDOWN_3                                                     0x2100018
#define ixPB1_PIF_SC_CTL2                                                       0x2100019
#define ixPB1_PIF_PRG0                                                          0x210001a
#define ixPB1_PIF_PRG1                                                          0x210001b
#define ixPB1_PIF_PRG2                                                          0x210001c
#define ixPB1_PIF_PRG3                                                          0x210001d
#define ixPB1_PIF_PRG4                                                          0x210001e
#define ixPB1_PIF_PRG5                                                          0x210001f
#define ixPB1_PIF_PDNB_OVERRIDE_0                                               0x2100020
#define ixPB1_PIF_PDNB_OVERRIDE_1                                               0x2100021
#define ixPB1_PIF_PDNB_OVERRIDE_2                                               0x2100022
#define ixPB1_PIF_PDNB_OVERRIDE_3                                               0x2100023
#define ixPB1_PIF_PDNB_OVERRIDE_4                                               0x2100024
#define ixPB1_PIF_PDNB_OVERRIDE_5                                               0x2100025
#define ixPB1_PIF_PDNB_OVERRIDE_6                                               0x2100026
#define ixPB1_PIF_PDNB_OVERRIDE_7                                               0x2100027
#define ixPB1_PIF_SEQ_STATUS_0                                                  0x2100028
#define ixPB1_PIF_SEQ_STATUS_1                                                  0x2100029
#define ixPB1_PIF_SEQ_STATUS_2                                                  0x210002a
#define ixPB1_PIF_SEQ_STATUS_3                                                  0x210002b
#define ixPB1_PIF_SEQ_STATUS_4                                                  0x210002c
#define ixPB1_PIF_SEQ_STATUS_5                                                  0x210002d
#define ixPB1_PIF_SEQ_STATUS_6                                                  0x210002e
#define ixPB1_PIF_SEQ_STATUS_7                                                  0x210002f
#define ixPB1_PIF_PDNB_OVERRIDE_8                                               0x2100030
#define ixPB1_PIF_PDNB_OVERRIDE_9                                               0x2100031
#define ixPB1_PIF_PDNB_OVERRIDE_10                                              0x2100032
#define ixPB1_PIF_PDNB_OVERRIDE_11                                              0x2100033
#define ixPB1_PIF_PDNB_OVERRIDE_12                                              0x2100034
#define ixPB1_PIF_PDNB_OVERRIDE_13                                              0x2100035
#define ixPB1_PIF_PDNB_OVERRIDE_14                                              0x2100036
#define ixPB1_PIF_PDNB_OVERRIDE_15                                              0x2100037
#define ixPB1_PIF_SEQ_STATUS_8                                                  0x2100038
#define ixPB1_PIF_SEQ_STATUS_9                                                  0x2100039
#define ixPB1_PIF_SEQ_STATUS_10                                                 0x210003a
#define ixPB1_PIF_SEQ_STATUS_11                                                 0x210003b
#define ixPB1_PIF_SEQ_STATUS_12                                                 0x210003c
#define ixPB1_PIF_SEQ_STATUS_13                                                 0x210003d
#define ixPB1_PIF_SEQ_STATUS_14                                                 0x210003e
#define ixPB1_PIF_SEQ_STATUS_15                                                 0x210003f
#define mmBIF_RFE_SNOOP_REG                                                     0x27
#define mmBIF_RFE_WARMRST_CNTL                                                  0x1459
#define mmBIF_RFE_SOFTRST_CNTL                                                  0x1441
#define mmBIF_RFE_IMPRST_CNTL                                                   0x1458
#define mmBIF_RFE_CLIENT_SOFTRST_TRIGGER                                        0x1442
#define mmBIF_RFE_MASTER_SOFTRST_TRIGGER                                        0x1443
#define mmBIF_PWDN_COMMAND                                                      0x1444
#define mmBIF_PWDN_STATUS                                                       0x1445
#define mmBIF_RFE_MST_BU_CMDSTATUS                                              0x1446
#define mmBIF_RFE_MST_RWREG_RFEWDBIF_CMDSTATUS                                  0x1447
#define mmBIF_RFE_MST_BX_CMDSTATUS                                              0x1448
#define mmBIF_RFE_MST_TMOUT_STATUS                                              0x144b
#define mmBIF_RFE_MMCFG_CNTL                                                    0x144c
#define mmBIF_CC_RFE_IMP_OVERRIDECNTL                                           0x1455
#define mmBIF_IMPCTL_SMPLCNTL                                                   0x1450
#define mmBIF_IMPCTL_RXCNTL                                                     0x1451
#define mmBIF_IMPCTL_TXCNTL_pd                                                  0x1452
#define mmBIF_IMPCTL_TXCNTL_pu                                                  0x1453
#define mmBIF_IMPCTL_CONTINUOUS_CALIBRATION_PERIOD                              0x1454
#define mmBIF_CLOCKS_BITS                                                       0x1489
#define mmBIF_LNCNT_RESET                                                       0x1488
#define mmLNCNT_CONTROL                                                         0x1487
#define mmNEW_REFCLKB_TIMER                                                     0x1485
#define mmNEW_REFCLKB_TIMER_1                                                   0x1484
#define mmBIF_CLK_PDWN_DELAY_TIMER                                              0x1483
#define mmBIF_RESET_EN                                                          0x1482
#define mmBIF_PIF_TXCLK_SWITCH_TIMER                                            0x1481
#define mmBIF_BACO_MSIC                                                         0x1480
#define mmBIF_RESET_CNTL                                                        0x1486
#define mmBIF_RFE_CNTL_MISC                                                     0x148c

#endif /* BIF_4_1_D_H */
