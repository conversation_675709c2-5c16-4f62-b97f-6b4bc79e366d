/* Copyright 2016 Advanced Micro Devices, Inc.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a
 * copy of this software and associated documentation files (the "Software"),
 * to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
 * THE COPYRIGHT HOLDER(S) OR AUTHOR(S) BE LIABLE FOR ANY CLAIM, DAMAGES OR
 * OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 * OTHER DEALINGS IN THE SOFTWARE.
 *
 * Authors: <AUTHORS>
 *
 */

#ifndef __DAL_DPP_DCN10_H__
#define __DAL_DPP_DCN10_H__

#include "dpp.h"

#define TO_DCN10_DPP(dpp)\
	container_of(dpp, struct dcn10_dpp, base)

/* TODO: Use correct number of taps. Using polaris values for now */
#define LB_TOTAL_NUMBER_OF_ENTRIES 5124
#define LB_BITS_PER_ENTRY 144

#define TF_SF(reg_name, field_name, post_fix)\
	.field_name = reg_name ## __ ## field_name ## post_fix

//Used to resolve corner case
#define TF2_SF(reg_name, field_name, post_fix)\
	.field_name = reg_name ## _ ## field_name ## post_fix

#define TF_REG_LIST_DCN(id) \
	SRI(CM_GAMUT_REMAP_CONTROL, CM, id),\
	SRI(CM_GAMUT_REMAP_C11_C12, CM, id),\
	SRI(CM_GAMUT_REMAP_C13_C14, CM, id),\
	SRI(CM_GAMUT_REMAP_C21_C22, CM, id),\
	SRI(CM_GAMUT_REMAP_C23_C24, CM, id),\
	SRI(CM_GAMUT_REMAP_C31_C32, CM, id),\
	SRI(CM_GAMUT_REMAP_C33_C34, CM, id),\
	SRI(DSCL_EXT_OVERSCAN_LEFT_RIGHT, DSCL, id), \
	SRI(DSCL_EXT_OVERSCAN_TOP_BOTTOM, DSCL, id), \
	SRI(OTG_H_BLANK, DSCL, id), \
	SRI(OTG_V_BLANK, DSCL, id), \
	SRI(SCL_MODE, DSCL, id), \
	SRI(LB_DATA_FORMAT, DSCL, id), \
	SRI(LB_MEMORY_CTRL, DSCL, id), \
	SRI(DSCL_AUTOCAL, DSCL, id), \
	SRI(SCL_BLACK_OFFSET, DSCL, id), \
	SRI(SCL_TAP_CONTROL, DSCL, id), \
	SRI(SCL_COEF_RAM_TAP_SELECT, DSCL, id), \
	SRI(SCL_COEF_RAM_TAP_DATA, DSCL, id), \
	SRI(DSCL_2TAP_CONTROL, DSCL, id), \
	SRI(MPC_SIZE, DSCL, id), \
	SRI(SCL_HORZ_FILTER_SCALE_RATIO, DSCL, id), \
	SRI(SCL_VERT_FILTER_SCALE_RATIO, DSCL, id), \
	SRI(SCL_HORZ_FILTER_SCALE_RATIO_C, DSCL, id), \
	SRI(SCL_VERT_FILTER_SCALE_RATIO_C, DSCL, id), \
	SRI(SCL_HORZ_FILTER_INIT, DSCL, id), \
	SRI(SCL_HORZ_FILTER_INIT_C, DSCL, id), \
	SRI(SCL_VERT_FILTER_INIT, DSCL, id), \
	SRI(SCL_VERT_FILTER_INIT_BOT, DSCL, id), \
	SRI(SCL_VERT_FILTER_INIT_C, DSCL, id), \
	SRI(SCL_VERT_FILTER_INIT_BOT_C, DSCL, id), \
	SRI(RECOUT_START, DSCL, id), \
	SRI(RECOUT_SIZE, DSCL, id), \
	SRI(CM_ICSC_CONTROL, CM, id), \
	SRI(CM_ICSC_C11_C12, CM, id), \
	SRI(CM_ICSC_C33_C34, CM, id), \
	SRI(CM_DGAM_RAMB_START_CNTL_B, CM, id), \
	SRI(CM_DGAM_RAMB_START_CNTL_G, CM, id), \
	SRI(CM_DGAM_RAMB_START_CNTL_R, CM, id), \
	SRI(CM_DGAM_RAMB_SLOPE_CNTL_B, CM, id), \
	SRI(CM_DGAM_RAMB_SLOPE_CNTL_G, CM, id), \
	SRI(CM_DGAM_RAMB_SLOPE_CNTL_R, CM, id), \
	SRI(CM_DGAM_RAMB_END_CNTL1_B, CM, id), \
	SRI(CM_DGAM_RAMB_END_CNTL2_B, CM, id), \
	SRI(CM_DGAM_RAMB_END_CNTL1_G, CM, id), \
	SRI(CM_DGAM_RAMB_END_CNTL2_G, CM, id), \
	SRI(CM_DGAM_RAMB_END_CNTL1_R, CM, id), \
	SRI(CM_DGAM_RAMB_END_CNTL2_R, CM, id), \
	SRI(CM_DGAM_RAMB_REGION_0_1, CM, id), \
	SRI(CM_DGAM_RAMB_REGION_14_15, CM, id), \
	SRI(CM_DGAM_RAMA_START_CNTL_B, CM, id), \
	SRI(CM_DGAM_RAMA_START_CNTL_G, CM, id), \
	SRI(CM_DGAM_RAMA_START_CNTL_R, CM, id), \
	SRI(CM_DGAM_RAMA_SLOPE_CNTL_B, CM, id), \
	SRI(CM_DGAM_RAMA_SLOPE_CNTL_G, CM, id), \
	SRI(CM_DGAM_RAMA_SLOPE_CNTL_R, CM, id), \
	SRI(CM_DGAM_RAMA_END_CNTL1_B, CM, id), \
	SRI(CM_DGAM_RAMA_END_CNTL2_B, CM, id), \
	SRI(CM_DGAM_RAMA_END_CNTL1_G, CM, id), \
	SRI(CM_DGAM_RAMA_END_CNTL2_G, CM, id), \
	SRI(CM_DGAM_RAMA_END_CNTL1_R, CM, id), \
	SRI(CM_DGAM_RAMA_END_CNTL2_R, CM, id), \
	SRI(CM_DGAM_RAMA_REGION_0_1, CM, id), \
	SRI(CM_DGAM_RAMA_REGION_14_15, CM, id), \
	SRI(CM_MEM_PWR_CTRL, CM, id), \
	SRI(CM_DGAM_LUT_WRITE_EN_MASK, CM, id), \
	SRI(CM_DGAM_LUT_INDEX, CM, id), \
	SRI(CM_DGAM_LUT_DATA, CM, id), \
	SRI(CM_CONTROL, CM, id), \
	SRI(CM_DGAM_CONTROL, CM, id), \
	SRI(CM_TEST_DEBUG_INDEX, CM, id), \
	SRI(CM_TEST_DEBUG_DATA, CM, id), \
	SRI(FORMAT_CONTROL, CNVC_CFG, id), \
	SRI(CNVC_SURFACE_PIXEL_FORMAT, CNVC_CFG, id), \
	SRI(CURSOR0_CONTROL, CNVC_CUR, id), \
	SRI(CURSOR0_COLOR0, CNVC_CUR, id), \
	SRI(CURSOR0_COLOR1, CNVC_CUR, id), \
	SRI(CURSOR0_FP_SCALE_BIAS, CNVC_CUR, id), \
	SRI(DPP_CONTROL, DPP_TOP, id), \
	SRI(CM_HDR_MULT_COEF, CM, id)



#define TF_REG_LIST_DCN10(id) \
	TF_REG_LIST_DCN(id), \
	SRI(CM_COMA_C11_C12, CM, id),\
	SRI(CM_COMA_C33_C34, CM, id),\
	SRI(CM_COMB_C11_C12, CM, id),\
	SRI(CM_COMB_C33_C34, CM, id),\
	SRI(CM_OCSC_CONTROL, CM, id), \
	SRI(CM_OCSC_C11_C12, CM, id), \
	SRI(CM_OCSC_C33_C34, CM, id), \
	SRI(CM_BNS_VALUES_R, CM, id), \
	SRI(CM_BNS_VALUES_G, CM, id), \
	SRI(CM_BNS_VALUES_B, CM, id), \
	SRI(CM_MEM_PWR_CTRL, CM, id), \
	SRI(CM_RGAM_LUT_DATA, CM, id), \
	SRI(CM_RGAM_LUT_WRITE_EN_MASK, CM, id),\
	SRI(CM_RGAM_LUT_INDEX, CM, id), \
	SRI(CM_RGAM_RAMB_START_CNTL_B, CM, id), \
	SRI(CM_RGAM_RAMB_START_CNTL_G, CM, id), \
	SRI(CM_RGAM_RAMB_START_CNTL_R, CM, id), \
	SRI(CM_RGAM_RAMB_SLOPE_CNTL_B, CM, id), \
	SRI(CM_RGAM_RAMB_SLOPE_CNTL_G, CM, id), \
	SRI(CM_RGAM_RAMB_SLOPE_CNTL_R, CM, id), \
	SRI(CM_RGAM_RAMB_END_CNTL1_B, CM, id), \
	SRI(CM_RGAM_RAMB_END_CNTL2_B, CM, id), \
	SRI(CM_RGAM_RAMB_END_CNTL1_G, CM, id), \
	SRI(CM_RGAM_RAMB_END_CNTL2_G, CM, id), \
	SRI(CM_RGAM_RAMB_END_CNTL1_R, CM, id), \
	SRI(CM_RGAM_RAMB_END_CNTL2_R, CM, id), \
	SRI(CM_RGAM_RAMB_REGION_0_1, CM, id), \
	SRI(CM_RGAM_RAMB_REGION_32_33, CM, id), \
	SRI(CM_RGAM_RAMA_START_CNTL_B, CM, id), \
	SRI(CM_RGAM_RAMA_START_CNTL_G, CM, id), \
	SRI(CM_RGAM_RAMA_START_CNTL_R, CM, id), \
	SRI(CM_RGAM_RAMA_SLOPE_CNTL_B, CM, id), \
	SRI(CM_RGAM_RAMA_SLOPE_CNTL_G, CM, id), \
	SRI(CM_RGAM_RAMA_SLOPE_CNTL_R, CM, id), \
	SRI(CM_RGAM_RAMA_END_CNTL1_B, CM, id), \
	SRI(CM_RGAM_RAMA_END_CNTL2_B, CM, id), \
	SRI(CM_RGAM_RAMA_END_CNTL1_G, CM, id), \
	SRI(CM_RGAM_RAMA_END_CNTL2_G, CM, id), \
	SRI(CM_RGAM_RAMA_END_CNTL1_R, CM, id), \
	SRI(CM_RGAM_RAMA_END_CNTL2_R, CM, id), \
	SRI(CM_RGAM_RAMA_REGION_0_1, CM, id), \
	SRI(CM_RGAM_RAMA_REGION_32_33, CM, id), \
	SRI(CM_RGAM_CONTROL, CM, id), \
	SRI(CM_IGAM_CONTROL, CM, id), \
	SRI(CM_IGAM_LUT_RW_CONTROL, CM, id), \
	SRI(CM_IGAM_LUT_RW_INDEX, CM, id), \
	SRI(CM_IGAM_LUT_SEQ_COLOR, CM, id), \
	SRI(CURSOR_CONTROL, CURSOR, id), \
	SRI(CM_CMOUT_CONTROL, CM, id)


#define TF_REG_LIST_SH_MASK_DCN(mask_sh)\
	TF_SF(CM0_CM_GAMUT_REMAP_CONTROL, CM_GAMUT_REMAP_MODE, mask_sh),\
	TF_SF(CM0_CM_GAMUT_REMAP_C11_C12, CM_GAMUT_REMAP_C11, mask_sh),\
	TF_SF(CM0_CM_GAMUT_REMAP_C11_C12, CM_GAMUT_REMAP_C12, mask_sh),\
	TF_SF(CM0_CM_GAMUT_REMAP_C13_C14, CM_GAMUT_REMAP_C13, mask_sh),\
	TF_SF(CM0_CM_GAMUT_REMAP_C13_C14, CM_GAMUT_REMAP_C14, mask_sh),\
	TF_SF(CM0_CM_GAMUT_REMAP_C21_C22, CM_GAMUT_REMAP_C21, mask_sh),\
	TF_SF(CM0_CM_GAMUT_REMAP_C21_C22, CM_GAMUT_REMAP_C22, mask_sh),\
	TF_SF(CM0_CM_GAMUT_REMAP_C23_C24, CM_GAMUT_REMAP_C23, mask_sh),\
	TF_SF(CM0_CM_GAMUT_REMAP_C23_C24, CM_GAMUT_REMAP_C24, mask_sh),\
	TF_SF(CM0_CM_GAMUT_REMAP_C31_C32, CM_GAMUT_REMAP_C31, mask_sh),\
	TF_SF(CM0_CM_GAMUT_REMAP_C31_C32, CM_GAMUT_REMAP_C32, mask_sh),\
	TF_SF(CM0_CM_GAMUT_REMAP_C33_C34, CM_GAMUT_REMAP_C33, mask_sh),\
	TF_SF(CM0_CM_GAMUT_REMAP_C33_C34, CM_GAMUT_REMAP_C34, mask_sh),\
	TF_SF(DSCL0_DSCL_EXT_OVERSCAN_LEFT_RIGHT, EXT_OVERSCAN_LEFT, mask_sh),\
	TF_SF(DSCL0_DSCL_EXT_OVERSCAN_LEFT_RIGHT, EXT_OVERSCAN_RIGHT, mask_sh),\
	TF_SF(DSCL0_DSCL_EXT_OVERSCAN_TOP_BOTTOM, EXT_OVERSCAN_BOTTOM, mask_sh),\
	TF_SF(DSCL0_DSCL_EXT_OVERSCAN_TOP_BOTTOM, EXT_OVERSCAN_TOP, mask_sh),\
	TF_SF(DSCL0_OTG_H_BLANK, OTG_H_BLANK_START, mask_sh),\
	TF_SF(DSCL0_OTG_H_BLANK, OTG_H_BLANK_END, mask_sh),\
	TF_SF(DSCL0_OTG_V_BLANK, OTG_V_BLANK_START, mask_sh),\
	TF_SF(DSCL0_OTG_V_BLANK, OTG_V_BLANK_END, mask_sh),\
	TF_SF(DSCL0_LB_DATA_FORMAT, INTERLEAVE_EN, mask_sh),\
	TF2_SF(DSCL0, LB_DATA_FORMAT__ALPHA_EN, mask_sh),\
	TF_SF(DSCL0_LB_MEMORY_CTRL, MEMORY_CONFIG, mask_sh),\
	TF_SF(DSCL0_LB_MEMORY_CTRL, LB_MAX_PARTITIONS, mask_sh),\
	TF_SF(DSCL0_DSCL_AUTOCAL, AUTOCAL_MODE, mask_sh),\
	TF_SF(DSCL0_DSCL_AUTOCAL, AUTOCAL_NUM_PIPE, mask_sh),\
	TF_SF(DSCL0_DSCL_AUTOCAL, AUTOCAL_PIPE_ID, mask_sh),\
	TF_SF(DSCL0_SCL_BLACK_OFFSET, SCL_BLACK_OFFSET_RGB_Y, mask_sh),\
	TF_SF(DSCL0_SCL_BLACK_OFFSET, SCL_BLACK_OFFSET_CBCR, mask_sh),\
	TF_SF(DSCL0_SCL_TAP_CONTROL, SCL_V_NUM_TAPS, mask_sh),\
	TF_SF(DSCL0_SCL_TAP_CONTROL, SCL_H_NUM_TAPS, mask_sh),\
	TF_SF(DSCL0_SCL_TAP_CONTROL, SCL_V_NUM_TAPS_C, mask_sh),\
	TF_SF(DSCL0_SCL_TAP_CONTROL, SCL_H_NUM_TAPS_C, mask_sh),\
	TF_SF(DSCL0_SCL_COEF_RAM_TAP_SELECT, SCL_COEF_RAM_TAP_PAIR_IDX, mask_sh),\
	TF_SF(DSCL0_SCL_COEF_RAM_TAP_SELECT, SCL_COEF_RAM_PHASE, mask_sh),\
	TF_SF(DSCL0_SCL_COEF_RAM_TAP_SELECT, SCL_COEF_RAM_FILTER_TYPE, mask_sh),\
	TF_SF(DSCL0_SCL_COEF_RAM_TAP_DATA, SCL_COEF_RAM_EVEN_TAP_COEF, mask_sh),\
	TF_SF(DSCL0_SCL_COEF_RAM_TAP_DATA, SCL_COEF_RAM_EVEN_TAP_COEF_EN, mask_sh),\
	TF_SF(DSCL0_SCL_COEF_RAM_TAP_DATA, SCL_COEF_RAM_ODD_TAP_COEF, mask_sh),\
	TF_SF(DSCL0_SCL_COEF_RAM_TAP_DATA, SCL_COEF_RAM_ODD_TAP_COEF_EN, mask_sh),\
	TF_SF(DSCL0_DSCL_2TAP_CONTROL, SCL_H_2TAP_HARDCODE_COEF_EN, mask_sh),\
	TF_SF(DSCL0_DSCL_2TAP_CONTROL, SCL_H_2TAP_SHARP_EN, mask_sh),\
	TF_SF(DSCL0_DSCL_2TAP_CONTROL, SCL_H_2TAP_SHARP_FACTOR, mask_sh),\
	TF_SF(DSCL0_DSCL_2TAP_CONTROL, SCL_V_2TAP_HARDCODE_COEF_EN, mask_sh),\
	TF_SF(DSCL0_DSCL_2TAP_CONTROL, SCL_V_2TAP_SHARP_EN, mask_sh),\
	TF_SF(DSCL0_DSCL_2TAP_CONTROL, SCL_V_2TAP_SHARP_FACTOR, mask_sh),\
	TF_SF(DSCL0_SCL_MODE, SCL_COEF_RAM_SELECT, mask_sh),\
	TF_SF(DSCL0_SCL_MODE, DSCL_MODE, mask_sh),\
	TF_SF(DSCL0_RECOUT_START, RECOUT_START_X, mask_sh),\
	TF_SF(DSCL0_RECOUT_START, RECOUT_START_Y, mask_sh),\
	TF_SF(DSCL0_RECOUT_SIZE, RECOUT_WIDTH, mask_sh),\
	TF_SF(DSCL0_RECOUT_SIZE, RECOUT_HEIGHT, mask_sh),\
	TF_SF(DSCL0_MPC_SIZE, MPC_WIDTH, mask_sh),\
	TF_SF(DSCL0_MPC_SIZE, MPC_HEIGHT, mask_sh),\
	TF_SF(DSCL0_SCL_HORZ_FILTER_SCALE_RATIO, SCL_H_SCALE_RATIO, mask_sh),\
	TF_SF(DSCL0_SCL_VERT_FILTER_SCALE_RATIO, SCL_V_SCALE_RATIO, mask_sh),\
	TF_SF(DSCL0_SCL_HORZ_FILTER_SCALE_RATIO_C, SCL_H_SCALE_RATIO_C, mask_sh),\
	TF_SF(DSCL0_SCL_VERT_FILTER_SCALE_RATIO_C, SCL_V_SCALE_RATIO_C, mask_sh),\
	TF_SF(DSCL0_SCL_HORZ_FILTER_INIT, SCL_H_INIT_FRAC, mask_sh),\
	TF_SF(DSCL0_SCL_HORZ_FILTER_INIT, SCL_H_INIT_INT, mask_sh),\
	TF_SF(DSCL0_SCL_HORZ_FILTER_INIT_C, SCL_H_INIT_FRAC_C, mask_sh),\
	TF_SF(DSCL0_SCL_HORZ_FILTER_INIT_C, SCL_H_INIT_INT_C, mask_sh),\
	TF_SF(DSCL0_SCL_VERT_FILTER_INIT, SCL_V_INIT_FRAC, mask_sh),\
	TF_SF(DSCL0_SCL_VERT_FILTER_INIT, SCL_V_INIT_INT, mask_sh),\
	TF_SF(DSCL0_SCL_VERT_FILTER_INIT_BOT, SCL_V_INIT_FRAC_BOT, mask_sh),\
	TF_SF(DSCL0_SCL_VERT_FILTER_INIT_BOT, SCL_V_INIT_INT_BOT, mask_sh),\
	TF_SF(DSCL0_SCL_VERT_FILTER_INIT_C, SCL_V_INIT_FRAC_C, mask_sh),\
	TF_SF(DSCL0_SCL_VERT_FILTER_INIT_C, SCL_V_INIT_INT_C, mask_sh),\
	TF_SF(DSCL0_SCL_VERT_FILTER_INIT_BOT_C, SCL_V_INIT_FRAC_BOT_C, mask_sh),\
	TF_SF(DSCL0_SCL_VERT_FILTER_INIT_BOT_C, SCL_V_INIT_INT_BOT_C, mask_sh),\
	TF_SF(DSCL0_SCL_MODE, SCL_CHROMA_COEF_MODE, mask_sh),\
	TF_SF(DSCL0_SCL_MODE, SCL_COEF_RAM_SELECT_CURRENT, mask_sh), \
	TF_SF(CM0_CM_ICSC_CONTROL, CM_ICSC_MODE, mask_sh), \
	TF_SF(CM0_CM_ICSC_C11_C12, CM_ICSC_C11, mask_sh), \
	TF_SF(CM0_CM_ICSC_C11_C12, CM_ICSC_C12, mask_sh), \
	TF_SF(CM0_CM_ICSC_C33_C34, CM_ICSC_C33, mask_sh), \
	TF_SF(CM0_CM_ICSC_C33_C34, CM_ICSC_C34, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMB_START_CNTL_B, CM_DGAM_RAMB_EXP_REGION_START_B, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMB_START_CNTL_B, CM_DGAM_RAMB_EXP_REGION_START_SEGMENT_B, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMB_START_CNTL_G, CM_DGAM_RAMB_EXP_REGION_START_G, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMB_START_CNTL_G, CM_DGAM_RAMB_EXP_REGION_START_SEGMENT_G, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMB_START_CNTL_R, CM_DGAM_RAMB_EXP_REGION_START_R, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMB_START_CNTL_R, CM_DGAM_RAMB_EXP_REGION_START_SEGMENT_R, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMB_SLOPE_CNTL_B, CM_DGAM_RAMB_EXP_REGION_LINEAR_SLOPE_B, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMB_SLOPE_CNTL_G, CM_DGAM_RAMB_EXP_REGION_LINEAR_SLOPE_G, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMB_SLOPE_CNTL_R, CM_DGAM_RAMB_EXP_REGION_LINEAR_SLOPE_R, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMB_END_CNTL1_B, CM_DGAM_RAMB_EXP_REGION_END_B, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMB_END_CNTL2_B, CM_DGAM_RAMB_EXP_REGION_END_SLOPE_B, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMB_END_CNTL2_B, CM_DGAM_RAMB_EXP_REGION_END_BASE_B, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMB_END_CNTL1_G, CM_DGAM_RAMB_EXP_REGION_END_G, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMB_END_CNTL2_G, CM_DGAM_RAMB_EXP_REGION_END_SLOPE_G, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMB_END_CNTL2_G, CM_DGAM_RAMB_EXP_REGION_END_BASE_G, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMB_END_CNTL1_R, CM_DGAM_RAMB_EXP_REGION_END_R, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMB_END_CNTL2_R, CM_DGAM_RAMB_EXP_REGION_END_SLOPE_R, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMB_END_CNTL2_R, CM_DGAM_RAMB_EXP_REGION_END_BASE_R, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMB_REGION_0_1, CM_DGAM_RAMB_EXP_REGION0_LUT_OFFSET, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMB_REGION_0_1, CM_DGAM_RAMB_EXP_REGION0_NUM_SEGMENTS, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMB_REGION_0_1, CM_DGAM_RAMB_EXP_REGION1_LUT_OFFSET, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMB_REGION_0_1, CM_DGAM_RAMB_EXP_REGION1_NUM_SEGMENTS, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMB_REGION_14_15, CM_DGAM_RAMB_EXP_REGION14_LUT_OFFSET, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMB_REGION_14_15, CM_DGAM_RAMB_EXP_REGION14_NUM_SEGMENTS, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMB_REGION_14_15, CM_DGAM_RAMB_EXP_REGION15_LUT_OFFSET, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMB_REGION_14_15, CM_DGAM_RAMB_EXP_REGION15_NUM_SEGMENTS, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMA_START_CNTL_B, CM_DGAM_RAMA_EXP_REGION_START_B, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMA_START_CNTL_B, CM_DGAM_RAMA_EXP_REGION_START_SEGMENT_B, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMA_START_CNTL_G, CM_DGAM_RAMA_EXP_REGION_START_G, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMA_START_CNTL_G, CM_DGAM_RAMA_EXP_REGION_START_SEGMENT_G, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMA_START_CNTL_R, CM_DGAM_RAMA_EXP_REGION_START_R, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMA_START_CNTL_R, CM_DGAM_RAMA_EXP_REGION_START_SEGMENT_R, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMA_SLOPE_CNTL_B, CM_DGAM_RAMA_EXP_REGION_LINEAR_SLOPE_B, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMA_SLOPE_CNTL_G, CM_DGAM_RAMA_EXP_REGION_LINEAR_SLOPE_G, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMA_SLOPE_CNTL_R, CM_DGAM_RAMA_EXP_REGION_LINEAR_SLOPE_R, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMA_END_CNTL1_B, CM_DGAM_RAMA_EXP_REGION_END_B, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMA_END_CNTL2_B, CM_DGAM_RAMA_EXP_REGION_END_SLOPE_B, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMA_END_CNTL2_B, CM_DGAM_RAMA_EXP_REGION_END_BASE_B, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMA_END_CNTL1_G, CM_DGAM_RAMA_EXP_REGION_END_G, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMA_END_CNTL2_G, CM_DGAM_RAMA_EXP_REGION_END_SLOPE_G, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMA_END_CNTL2_G, CM_DGAM_RAMA_EXP_REGION_END_BASE_G, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMA_END_CNTL1_R, CM_DGAM_RAMA_EXP_REGION_END_R, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMA_END_CNTL2_R, CM_DGAM_RAMA_EXP_REGION_END_SLOPE_R, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMA_END_CNTL2_R, CM_DGAM_RAMA_EXP_REGION_END_BASE_R, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMA_REGION_0_1, CM_DGAM_RAMA_EXP_REGION0_LUT_OFFSET, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMA_REGION_0_1, CM_DGAM_RAMA_EXP_REGION0_NUM_SEGMENTS, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMA_REGION_0_1, CM_DGAM_RAMA_EXP_REGION1_LUT_OFFSET, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMA_REGION_0_1, CM_DGAM_RAMA_EXP_REGION1_NUM_SEGMENTS, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMA_REGION_14_15, CM_DGAM_RAMA_EXP_REGION14_LUT_OFFSET, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMA_REGION_14_15, CM_DGAM_RAMA_EXP_REGION14_NUM_SEGMENTS, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMA_REGION_14_15, CM_DGAM_RAMA_EXP_REGION15_LUT_OFFSET, mask_sh), \
	TF_SF(CM0_CM_DGAM_RAMA_REGION_14_15, CM_DGAM_RAMA_EXP_REGION15_NUM_SEGMENTS, mask_sh), \
	TF_SF(CM0_CM_MEM_PWR_CTRL, SHARED_MEM_PWR_DIS, mask_sh), \
	TF_SF(CM0_CM_DGAM_LUT_WRITE_EN_MASK, CM_DGAM_LUT_WRITE_EN_MASK, mask_sh), \
	TF_SF(CM0_CM_DGAM_LUT_WRITE_EN_MASK, CM_DGAM_LUT_WRITE_SEL, mask_sh), \
	TF_SF(CM0_CM_DGAM_LUT_INDEX, CM_DGAM_LUT_INDEX, mask_sh), \
	TF_SF(CM0_CM_DGAM_LUT_DATA, CM_DGAM_LUT_DATA, mask_sh), \
	TF_SF(CM0_CM_DGAM_CONTROL, CM_DGAM_LUT_MODE, mask_sh), \
	TF_SF(CM0_CM_TEST_DEBUG_INDEX, CM_TEST_DEBUG_INDEX, mask_sh), \
	TF_SF(CNVC_CFG0_FORMAT_CONTROL, CNVC_BYPASS, mask_sh), \
	TF2_SF(CNVC_CFG0, FORMAT_CONTROL__ALPHA_EN, mask_sh), \
	TF_SF(CNVC_CFG0_FORMAT_CONTROL, FORMAT_EXPANSION_MODE, mask_sh), \
	TF_SF(CNVC_CFG0_CNVC_SURFACE_PIXEL_FORMAT, CNVC_SURFACE_PIXEL_FORMAT, mask_sh), \
	TF_SF(CNVC_CUR0_CURSOR0_CONTROL, CUR0_MODE, mask_sh), \
	TF_SF(CNVC_CUR0_CURSOR0_CONTROL, CUR0_EXPANSION_MODE, mask_sh), \
	TF_SF(CNVC_CUR0_CURSOR0_CONTROL, CUR0_ENABLE, mask_sh), \
	TF_SF(CNVC_CUR0_CURSOR0_COLOR0, CUR0_COLOR0, mask_sh), \
	TF_SF(CNVC_CUR0_CURSOR0_COLOR1, CUR0_COLOR1, mask_sh), \
	TF_SF(CNVC_CUR0_CURSOR0_FP_SCALE_BIAS, CUR0_FP_BIAS, mask_sh), \
	TF_SF(CNVC_CUR0_CURSOR0_FP_SCALE_BIAS, CUR0_FP_SCALE, mask_sh), \
	TF_SF(DPP_TOP0_DPP_CONTROL, DPP_CLOCK_ENABLE, mask_sh), \
	TF_SF(CM0_CM_HDR_MULT_COEF, CM_HDR_MULT_COEF, mask_sh)

#define TF_REG_LIST_SH_MASK_DCN10(mask_sh)\
	TF_REG_LIST_SH_MASK_DCN(mask_sh),\
	TF_SF(DSCL0_LB_DATA_FORMAT, PIXEL_DEPTH, mask_sh),\
	TF_SF(DSCL0_LB_DATA_FORMAT, PIXEL_EXPAN_MODE, mask_sh),\
	TF_SF(DSCL0_LB_DATA_FORMAT, PIXEL_REDUCE_MODE, mask_sh),\
	TF_SF(DSCL0_LB_DATA_FORMAT, DYNAMIC_PIXEL_DEPTH, mask_sh),\
	TF_SF(DSCL0_LB_DATA_FORMAT, DITHER_EN, mask_sh),\
	TF_SF(CM0_CM_COMA_C11_C12, CM_COMA_C11, mask_sh),\
	TF_SF(CM0_CM_COMA_C11_C12, CM_COMA_C12, mask_sh),\
	TF_SF(CM0_CM_COMA_C33_C34, CM_COMA_C33, mask_sh),\
	TF_SF(CM0_CM_COMA_C33_C34, CM_COMA_C34, mask_sh),\
	TF_SF(CM0_CM_COMB_C11_C12, CM_COMB_C11, mask_sh),\
	TF_SF(CM0_CM_COMB_C11_C12, CM_COMB_C12, mask_sh),\
	TF_SF(CM0_CM_COMB_C33_C34, CM_COMB_C33, mask_sh),\
	TF_SF(CM0_CM_COMB_C33_C34, CM_COMB_C34, mask_sh),\
	TF_SF(CM0_CM_OCSC_CONTROL, CM_OCSC_MODE, mask_sh), \
	TF_SF(CM0_CM_OCSC_C11_C12, CM_OCSC_C11, mask_sh), \
	TF_SF(CM0_CM_OCSC_C11_C12, CM_OCSC_C12, mask_sh), \
	TF_SF(CM0_CM_OCSC_C33_C34, CM_OCSC_C33, mask_sh), \
	TF_SF(CM0_CM_OCSC_C33_C34, CM_OCSC_C34, mask_sh), \
	TF_SF(CM0_CM_BNS_VALUES_R, CM_BNS_BIAS_R, mask_sh), \
	TF_SF(CM0_CM_BNS_VALUES_G, CM_BNS_BIAS_G, mask_sh), \
	TF_SF(CM0_CM_BNS_VALUES_B, CM_BNS_BIAS_B, mask_sh), \
	TF_SF(CM0_CM_BNS_VALUES_R, CM_BNS_SCALE_R, mask_sh), \
	TF_SF(CM0_CM_BNS_VALUES_G, CM_BNS_SCALE_G, mask_sh), \
	TF_SF(CM0_CM_BNS_VALUES_B, CM_BNS_SCALE_B, mask_sh), \
	TF_SF(CM0_CM_MEM_PWR_CTRL, RGAM_MEM_PWR_FORCE, mask_sh), \
	TF_SF(CM0_CM_RGAM_LUT_DATA, CM_RGAM_LUT_DATA, mask_sh), \
	TF_SF(CM0_CM_RGAM_LUT_WRITE_EN_MASK, CM_RGAM_LUT_WRITE_EN_MASK, mask_sh), \
	TF_SF(CM0_CM_RGAM_LUT_WRITE_EN_MASK, CM_RGAM_LUT_WRITE_SEL, mask_sh), \
	TF_SF(CM0_CM_RGAM_LUT_INDEX, CM_RGAM_LUT_INDEX, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMB_START_CNTL_B, CM_RGAM_RAMB_EXP_REGION_START_B, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMB_START_CNTL_B, CM_RGAM_RAMB_EXP_REGION_START_SEGMENT_B, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMB_START_CNTL_G, CM_RGAM_RAMB_EXP_REGION_START_G, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMB_START_CNTL_G, CM_RGAM_RAMB_EXP_REGION_START_SEGMENT_G, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMB_START_CNTL_R, CM_RGAM_RAMB_EXP_REGION_START_R, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMB_START_CNTL_R, CM_RGAM_RAMB_EXP_REGION_START_SEGMENT_R, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMB_SLOPE_CNTL_B, CM_RGAM_RAMB_EXP_REGION_LINEAR_SLOPE_B, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMB_SLOPE_CNTL_G, CM_RGAM_RAMB_EXP_REGION_LINEAR_SLOPE_G, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMB_SLOPE_CNTL_R, CM_RGAM_RAMB_EXP_REGION_LINEAR_SLOPE_R, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMB_END_CNTL1_B, CM_RGAM_RAMB_EXP_REGION_END_B, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMB_END_CNTL2_B, CM_RGAM_RAMB_EXP_REGION_END_SLOPE_B, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMB_END_CNTL2_B, CM_RGAM_RAMB_EXP_REGION_END_BASE_B, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMB_END_CNTL1_G, CM_RGAM_RAMB_EXP_REGION_END_G, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMB_END_CNTL2_G, CM_RGAM_RAMB_EXP_REGION_END_SLOPE_G, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMB_END_CNTL2_G, CM_RGAM_RAMB_EXP_REGION_END_BASE_G, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMB_END_CNTL1_R, CM_RGAM_RAMB_EXP_REGION_END_R, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMB_END_CNTL2_R, CM_RGAM_RAMB_EXP_REGION_END_SLOPE_R, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMB_END_CNTL2_R, CM_RGAM_RAMB_EXP_REGION_END_BASE_R, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMB_REGION_0_1, CM_RGAM_RAMB_EXP_REGION0_LUT_OFFSET, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMB_REGION_0_1, CM_RGAM_RAMB_EXP_REGION0_NUM_SEGMENTS, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMB_REGION_0_1, CM_RGAM_RAMB_EXP_REGION1_LUT_OFFSET, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMB_REGION_0_1, CM_RGAM_RAMB_EXP_REGION1_NUM_SEGMENTS, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMB_REGION_32_33, CM_RGAM_RAMB_EXP_REGION32_LUT_OFFSET, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMB_REGION_32_33, CM_RGAM_RAMB_EXP_REGION32_NUM_SEGMENTS, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMB_REGION_32_33, CM_RGAM_RAMB_EXP_REGION33_LUT_OFFSET, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMB_REGION_32_33, CM_RGAM_RAMB_EXP_REGION33_NUM_SEGMENTS, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMA_START_CNTL_B, CM_RGAM_RAMA_EXP_REGION_START_B, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMA_START_CNTL_B, CM_RGAM_RAMA_EXP_REGION_START_SEGMENT_B, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMA_START_CNTL_G, CM_RGAM_RAMA_EXP_REGION_START_G, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMA_START_CNTL_G, CM_RGAM_RAMA_EXP_REGION_START_SEGMENT_G, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMA_START_CNTL_R, CM_RGAM_RAMA_EXP_REGION_START_R, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMA_START_CNTL_R, CM_RGAM_RAMA_EXP_REGION_START_SEGMENT_R, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMA_SLOPE_CNTL_B, CM_RGAM_RAMA_EXP_REGION_LINEAR_SLOPE_B, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMA_SLOPE_CNTL_G, CM_RGAM_RAMA_EXP_REGION_LINEAR_SLOPE_G, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMA_SLOPE_CNTL_R, CM_RGAM_RAMA_EXP_REGION_LINEAR_SLOPE_R, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMA_END_CNTL1_B, CM_RGAM_RAMA_EXP_REGION_END_B, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMA_END_CNTL2_B, CM_RGAM_RAMA_EXP_REGION_END_SLOPE_B, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMA_END_CNTL2_B, CM_RGAM_RAMA_EXP_REGION_END_BASE_B, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMA_END_CNTL1_G, CM_RGAM_RAMA_EXP_REGION_END_G, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMA_END_CNTL2_G, CM_RGAM_RAMA_EXP_REGION_END_SLOPE_G, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMA_END_CNTL2_G, CM_RGAM_RAMA_EXP_REGION_END_BASE_G, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMA_END_CNTL1_R, CM_RGAM_RAMA_EXP_REGION_END_R, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMA_END_CNTL2_R, CM_RGAM_RAMA_EXP_REGION_END_SLOPE_R, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMA_END_CNTL2_R, CM_RGAM_RAMA_EXP_REGION_END_BASE_R, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMA_REGION_0_1, CM_RGAM_RAMA_EXP_REGION0_LUT_OFFSET, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMA_REGION_0_1, CM_RGAM_RAMA_EXP_REGION0_NUM_SEGMENTS, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMA_REGION_0_1, CM_RGAM_RAMA_EXP_REGION1_LUT_OFFSET, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMA_REGION_0_1, CM_RGAM_RAMA_EXP_REGION1_NUM_SEGMENTS, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMA_REGION_32_33, CM_RGAM_RAMA_EXP_REGION32_LUT_OFFSET, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMA_REGION_32_33, CM_RGAM_RAMA_EXP_REGION32_NUM_SEGMENTS, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMA_REGION_32_33, CM_RGAM_RAMA_EXP_REGION33_LUT_OFFSET, mask_sh), \
	TF_SF(CM0_CM_RGAM_RAMA_REGION_32_33, CM_RGAM_RAMA_EXP_REGION33_NUM_SEGMENTS, mask_sh), \
	TF_SF(CM0_CM_RGAM_CONTROL, CM_RGAM_LUT_MODE, mask_sh), \
	TF_SF(CM0_CM_IGAM_CONTROL, CM_IGAM_LUT_MODE, mask_sh), \
	TF_SF(CM0_CM_IGAM_CONTROL, CM_IGAM_LUT_FORMAT_R, mask_sh), \
	TF_SF(CM0_CM_IGAM_CONTROL, CM_IGAM_LUT_FORMAT_G, mask_sh), \
	TF_SF(CM0_CM_IGAM_CONTROL, CM_IGAM_LUT_FORMAT_B, mask_sh), \
	TF_SF(CM0_CM_IGAM_CONTROL, CM_IGAM_INPUT_FORMAT, mask_sh), \
	TF_SF(CM0_CM_IGAM_LUT_RW_CONTROL, CM_IGAM_DGAM_CONFIG_STATUS, mask_sh), \
	TF_SF(CM0_CM_IGAM_LUT_RW_CONTROL, CM_IGAM_LUT_HOST_EN, mask_sh), \
	TF_SF(CM0_CM_IGAM_LUT_RW_CONTROL, CM_IGAM_LUT_RW_MODE, mask_sh), \
	TF_SF(CM0_CM_IGAM_LUT_RW_CONTROL, CM_IGAM_LUT_SEL, mask_sh), \
	TF_SF(CM0_CM_IGAM_LUT_RW_CONTROL, CM_IGAM_LUT_WRITE_EN_MASK, mask_sh), \
	TF_SF(CM0_CM_IGAM_LUT_RW_INDEX, CM_IGAM_LUT_RW_INDEX, mask_sh), \
	TF_SF(CM0_CM_CONTROL, CM_BYPASS_EN, mask_sh), \
	TF_SF(CM0_CM_IGAM_LUT_SEQ_COLOR, CM_IGAM_LUT_SEQ_COLOR, mask_sh), \
	TF_SF(CNVC_CFG0_FORMAT_CONTROL, OUTPUT_FP, mask_sh), \
	TF_SF(CM0_CM_CMOUT_CONTROL, CM_CMOUT_ROUND_TRUNC_MODE, mask_sh), \
	TF_SF(CURSOR0_CURSOR_CONTROL, CURSOR_MODE, mask_sh), \
	TF_SF(CURSOR0_CURSOR_CONTROL, CURSOR_PITCH, mask_sh), \
	TF_SF(CURSOR0_CURSOR_CONTROL, CURSOR_LINES_PER_CHUNK, mask_sh), \
	TF_SF(CURSOR0_CURSOR_CONTROL, CURSOR_ENABLE, mask_sh), \
	TF_SF(DPP_TOP0_DPP_CONTROL, DPPCLK_RATE_CONTROL, mask_sh)

/*
 *
	DCN1 CM debug status register definition

	register :ID9_CM_STATUS do
	implement_ref :cm
	map to:  :cmdebugind, at: j
	width 32
	disclosure   NEVER

		field :ID9_VUPDATE_CFG, [0], R
		field :ID9_IGAM_LUT_MODE, [2..1], R
		field :ID9_BNS_BYPASS, [3], R
		field :ID9_ICSC_MODE, [5..4], R
		field :ID9_DGAM_LUT_MODE, [8..6], R
		field :ID9_HDR_BYPASS, [9], R
		field :ID9_GAMUT_REMAP_MODE, [11..10], R
		field :ID9_RGAM_LUT_MODE, [14..12], R
		#1 free bit
		field :ID9_OCSC_MODE, [18..16], R
		field :ID9_DENORM_MODE, [21..19], R
		field :ID9_ROUND_TRUNC_MODE, [25..22], R
		field :ID9_DITHER_EN, [26], R
		field :ID9_DITHER_MODE, [28..27], R
	end
*/

#define TF_DEBUG_REG_LIST_SH_DCN10 \
	.CM_TEST_DEBUG_DATA_ID9_ICSC_MODE = 4, \
	.CM_TEST_DEBUG_DATA_ID9_OCSC_MODE = 16

#define TF_DEBUG_REG_LIST_MASK_DCN10 \
	.CM_TEST_DEBUG_DATA_ID9_ICSC_MODE = 0x30, \
	.CM_TEST_DEBUG_DATA_ID9_OCSC_MODE = 0x70000

#define TF_REG_FIELD_LIST(type) \
	type EXT_OVERSCAN_LEFT; \
	type EXT_OVERSCAN_RIGHT; \
	type EXT_OVERSCAN_BOTTOM; \
	type EXT_OVERSCAN_TOP; \
	type OTG_H_BLANK_START; \
	type OTG_H_BLANK_END; \
	type OTG_V_BLANK_START; \
	type OTG_V_BLANK_END; \
	type PIXEL_DEPTH; \
	type PIXEL_EXPAN_MODE; \
	type PIXEL_REDUCE_MODE; \
	type DYNAMIC_PIXEL_DEPTH; \
	type DITHER_EN; \
	type INTERLEAVE_EN; \
	type LB_DATA_FORMAT__ALPHA_EN; \
	type MEMORY_CONFIG; \
	type LB_MAX_PARTITIONS; \
	type AUTOCAL_MODE; \
	type AUTOCAL_NUM_PIPE; \
	type AUTOCAL_PIPE_ID; \
	type SCL_BLACK_OFFSET_RGB_Y; \
	type SCL_BLACK_OFFSET_CBCR; \
	type SCL_V_NUM_TAPS; \
	type SCL_H_NUM_TAPS; \
	type SCL_V_NUM_TAPS_C; \
	type SCL_H_NUM_TAPS_C; \
	type SCL_COEF_RAM_TAP_PAIR_IDX; \
	type SCL_COEF_RAM_PHASE; \
	type SCL_COEF_RAM_FILTER_TYPE; \
	type SCL_COEF_RAM_EVEN_TAP_COEF; \
	type SCL_COEF_RAM_EVEN_TAP_COEF_EN; \
	type SCL_COEF_RAM_ODD_TAP_COEF; \
	type SCL_COEF_RAM_ODD_TAP_COEF_EN; \
	type SCL_H_2TAP_HARDCODE_COEF_EN; \
	type SCL_H_2TAP_SHARP_EN; \
	type SCL_H_2TAP_SHARP_FACTOR; \
	type SCL_V_2TAP_HARDCODE_COEF_EN; \
	type SCL_V_2TAP_SHARP_EN; \
	type SCL_V_2TAP_SHARP_FACTOR; \
	type SCL_COEF_RAM_SELECT; \
	type DSCL_MODE; \
	type RECOUT_START_X; \
	type RECOUT_START_Y; \
	type RECOUT_WIDTH; \
	type RECOUT_HEIGHT; \
	type MPC_WIDTH; \
	type MPC_HEIGHT; \
	type SCL_H_SCALE_RATIO; \
	type SCL_V_SCALE_RATIO; \
	type SCL_H_SCALE_RATIO_C; \
	type SCL_V_SCALE_RATIO_C; \
	type SCL_H_INIT_FRAC; \
	type SCL_H_INIT_INT; \
	type SCL_H_INIT_FRAC_C; \
	type SCL_H_INIT_INT_C; \
	type SCL_V_INIT_FRAC; \
	type SCL_V_INIT_INT; \
	type SCL_V_INIT_FRAC_BOT; \
	type SCL_V_INIT_INT_BOT; \
	type SCL_V_INIT_FRAC_C; \
	type SCL_V_INIT_INT_C; \
	type SCL_V_INIT_FRAC_BOT_C; \
	type SCL_V_INIT_INT_BOT_C; \
	type SCL_CHROMA_COEF_MODE; \
	type SCL_COEF_RAM_SELECT_CURRENT; \
	type CM_GAMUT_REMAP_MODE; \
	type CM_GAMUT_REMAP_C11; \
	type CM_GAMUT_REMAP_C12; \
	type CM_GAMUT_REMAP_C13; \
	type CM_GAMUT_REMAP_C14; \
	type CM_GAMUT_REMAP_C21; \
	type CM_GAMUT_REMAP_C22; \
	type CM_GAMUT_REMAP_C23; \
	type CM_GAMUT_REMAP_C24; \
	type CM_GAMUT_REMAP_C31; \
	type CM_GAMUT_REMAP_C32; \
	type CM_GAMUT_REMAP_C33; \
	type CM_GAMUT_REMAP_C34; \
	type CM_COMA_C11; \
	type CM_COMA_C12; \
	type CM_COMA_C33; \
	type CM_COMA_C34; \
	type CM_COMB_C11; \
	type CM_COMB_C12; \
	type CM_COMB_C33; \
	type CM_COMB_C34; \
	type CM_OCSC_MODE; \
	type CM_OCSC_C11; \
	type CM_OCSC_C12; \
	type CM_OCSC_C33; \
	type CM_OCSC_C34; \
	type RGAM_MEM_PWR_FORCE; \
	type CM_RGAM_LUT_DATA; \
	type CM_RGAM_LUT_WRITE_EN_MASK; \
	type CM_RGAM_LUT_WRITE_SEL; \
	type CM_RGAM_LUT_INDEX; \
	type CM_RGAM_RAMB_EXP_REGION_START_B; \
	type CM_RGAM_RAMB_EXP_REGION_START_SEGMENT_B; \
	type CM_RGAM_RAMB_EXP_REGION_START_G; \
	type CM_RGAM_RAMB_EXP_REGION_START_SEGMENT_G; \
	type CM_RGAM_RAMB_EXP_REGION_START_R; \
	type CM_RGAM_RAMB_EXP_REGION_START_SEGMENT_R; \
	type CM_RGAM_RAMB_EXP_REGION_LINEAR_SLOPE_B; \
	type CM_RGAM_RAMB_EXP_REGION_LINEAR_SLOPE_G; \
	type CM_RGAM_RAMB_EXP_REGION_LINEAR_SLOPE_R; \
	type CM_RGAM_RAMB_EXP_REGION_END_B; \
	type CM_RGAM_RAMB_EXP_REGION_END_SLOPE_B; \
	type CM_RGAM_RAMB_EXP_REGION_END_BASE_B; \
	type CM_RGAM_RAMB_EXP_REGION_END_G; \
	type CM_RGAM_RAMB_EXP_REGION_END_SLOPE_G; \
	type CM_RGAM_RAMB_EXP_REGION_END_BASE_G; \
	type CM_RGAM_RAMB_EXP_REGION_END_R; \
	type CM_RGAM_RAMB_EXP_REGION_END_SLOPE_R; \
	type CM_RGAM_RAMB_EXP_REGION_END_BASE_R; \
	type CM_RGAM_RAMB_EXP_REGION0_LUT_OFFSET; \
	type CM_RGAM_RAMB_EXP_REGION0_NUM_SEGMENTS; \
	type CM_RGAM_RAMB_EXP_REGION1_LUT_OFFSET; \
	type CM_RGAM_RAMB_EXP_REGION1_NUM_SEGMENTS; \
	type CM_RGAM_RAMB_EXP_REGION32_LUT_OFFSET; \
	type CM_RGAM_RAMB_EXP_REGION32_NUM_SEGMENTS; \
	type CM_RGAM_RAMB_EXP_REGION33_LUT_OFFSET; \
	type CM_RGAM_RAMB_EXP_REGION33_NUM_SEGMENTS; \
	type CM_RGAM_RAMA_EXP_REGION_START_B; \
	type CM_RGAM_RAMA_EXP_REGION_START_SEGMENT_B; \
	type CM_RGAM_RAMA_EXP_REGION_START_G; \
	type CM_RGAM_RAMA_EXP_REGION_START_SEGMENT_G; \
	type CM_RGAM_RAMA_EXP_REGION_START_R; \
	type CM_RGAM_RAMA_EXP_REGION_START_SEGMENT_R; \
	type CM_RGAM_RAMA_EXP_REGION_LINEAR_SLOPE_B; \
	type CM_RGAM_RAMA_EXP_REGION_LINEAR_SLOPE_G; \
	type CM_RGAM_RAMA_EXP_REGION_LINEAR_SLOPE_R; \
	type CM_RGAM_RAMA_EXP_REGION_END_B; \
	type CM_RGAM_RAMA_EXP_REGION_END_SLOPE_B; \
	type CM_RGAM_RAMA_EXP_REGION_END_BASE_B; \
	type CM_RGAM_RAMA_EXP_REGION_END_G; \
	type CM_RGAM_RAMA_EXP_REGION_END_SLOPE_G; \
	type CM_RGAM_RAMA_EXP_REGION_END_BASE_G; \
	type CM_RGAM_RAMA_EXP_REGION_END_R; \
	type CM_RGAM_RAMA_EXP_REGION_END_SLOPE_R; \
	type CM_RGAM_RAMA_EXP_REGION_END_BASE_R; \
	type CM_RGAM_RAMA_EXP_REGION0_LUT_OFFSET; \
	type CM_RGAM_RAMA_EXP_REGION0_NUM_SEGMENTS; \
	type CM_RGAM_RAMA_EXP_REGION1_LUT_OFFSET; \
	type CM_RGAM_RAMA_EXP_REGION1_NUM_SEGMENTS; \
	type CM_RGAM_RAMA_EXP_REGION32_LUT_OFFSET; \
	type CM_RGAM_RAMA_EXP_REGION32_NUM_SEGMENTS; \
	type CM_RGAM_RAMA_EXP_REGION33_LUT_OFFSET; \
	type CM_RGAM_RAMA_EXP_REGION33_NUM_SEGMENTS; \
	type CM_RGAM_LUT_MODE; \
	type CM_CMOUT_ROUND_TRUNC_MODE; \
	type CM_BLNDGAM_LUT_MODE; \
	type CM_BLNDGAM_RAMB_EXP_REGION_START_B; \
	type CM_BLNDGAM_RAMB_EXP_REGION_START_SEGMENT_B; \
	type CM_BLNDGAM_RAMB_EXP_REGION_START_G; \
	type CM_BLNDGAM_RAMB_EXP_REGION_START_SEGMENT_G; \
	type CM_BLNDGAM_RAMB_EXP_REGION_START_R; \
	type CM_BLNDGAM_RAMB_EXP_REGION_START_SEGMENT_R; \
	type CM_BLNDGAM_RAMB_EXP_REGION_LINEAR_SLOPE_B; \
	type CM_BLNDGAM_RAMB_EXP_REGION_LINEAR_SLOPE_G; \
	type CM_BLNDGAM_RAMB_EXP_REGION_LINEAR_SLOPE_R; \
	type CM_BLNDGAM_RAMB_EXP_REGION_END_B; \
	type CM_BLNDGAM_RAMB_EXP_REGION_END_SLOPE_B; \
	type CM_BLNDGAM_RAMB_EXP_REGION_END_BASE_B; \
	type CM_BLNDGAM_RAMB_EXP_REGION_END_G; \
	type CM_BLNDGAM_RAMB_EXP_REGION_END_SLOPE_G; \
	type CM_BLNDGAM_RAMB_EXP_REGION_END_BASE_G; \
	type CM_BLNDGAM_RAMB_EXP_REGION_END_R; \
	type CM_BLNDGAM_RAMB_EXP_REGION_END_SLOPE_R; \
	type CM_BLNDGAM_RAMB_EXP_REGION_END_BASE_R; \
	type CM_BLNDGAM_RAMB_EXP_REGION0_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION0_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION1_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION1_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION2_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION2_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION3_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION3_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION4_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION4_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION5_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION5_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION6_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION6_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION7_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION7_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION8_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION8_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION9_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION9_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION10_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION10_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION11_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION11_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION12_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION12_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION13_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION13_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION14_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION14_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION15_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION15_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION16_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION16_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION17_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION17_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION18_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION18_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION19_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION19_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION20_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION20_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION21_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION21_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION22_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION22_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION23_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION23_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION24_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION24_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION25_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION25_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION26_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION26_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION27_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION27_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION28_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION28_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION29_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION29_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION30_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION30_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION31_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION31_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION32_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION32_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMB_EXP_REGION33_LUT_OFFSET; \
	type CM_BLNDGAM_RAMB_EXP_REGION33_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION_START_B; \
	type CM_BLNDGAM_RAMA_EXP_REGION_START_SEGMENT_B; \
	type CM_BLNDGAM_RAMA_EXP_REGION_START_G; \
	type CM_BLNDGAM_RAMA_EXP_REGION_START_SEGMENT_G; \
	type CM_BLNDGAM_RAMA_EXP_REGION_START_R; \
	type CM_BLNDGAM_RAMA_EXP_REGION_START_SEGMENT_R; \
	type CM_BLNDGAM_RAMA_EXP_REGION_LINEAR_SLOPE_B; \
	type CM_BLNDGAM_RAMA_EXP_REGION_LINEAR_SLOPE_G; \
	type CM_BLNDGAM_RAMA_EXP_REGION_LINEAR_SLOPE_R; \
	type CM_BLNDGAM_RAMA_EXP_REGION_END_B; \
	type CM_BLNDGAM_RAMA_EXP_REGION_END_SLOPE_B; \
	type CM_BLNDGAM_RAMA_EXP_REGION_END_BASE_B; \
	type CM_BLNDGAM_RAMA_EXP_REGION_END_G; \
	type CM_BLNDGAM_RAMA_EXP_REGION_END_SLOPE_G; \
	type CM_BLNDGAM_RAMA_EXP_REGION_END_BASE_G; \
	type CM_BLNDGAM_RAMA_EXP_REGION_END_R; \
	type CM_BLNDGAM_RAMA_EXP_REGION_END_SLOPE_R; \
	type CM_BLNDGAM_RAMA_EXP_REGION_END_BASE_R; \
	type CM_BLNDGAM_RAMA_EXP_REGION0_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION0_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION1_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION1_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION2_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION2_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION3_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION3_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION4_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION4_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION5_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION5_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION6_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION6_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION7_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION7_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION8_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION8_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION9_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION9_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION10_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION10_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION11_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION11_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION12_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION12_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION13_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION13_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION14_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION14_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION15_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION15_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION16_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION16_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION17_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION17_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION18_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION18_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION19_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION19_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION20_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION20_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION21_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION21_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION22_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION22_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION23_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION23_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION24_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION24_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION25_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION25_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION26_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION26_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION27_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION27_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION28_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION28_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION29_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION29_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION30_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION30_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION31_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION31_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION32_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION32_NUM_SEGMENTS; \
	type CM_BLNDGAM_RAMA_EXP_REGION33_LUT_OFFSET; \
	type CM_BLNDGAM_RAMA_EXP_REGION33_NUM_SEGMENTS; \
	type CM_BLNDGAM_LUT_WRITE_EN_MASK; \
	type CM_BLNDGAM_LUT_WRITE_SEL; \
	type CM_BLNDGAM_CONFIG_STATUS; \
	type CM_BLNDGAM_LUT_INDEX; \
	type BLNDGAM_MEM_PWR_FORCE; \
	type CM_3DLUT_MODE; \
	type CM_3DLUT_SIZE; \
	type CM_3DLUT_INDEX; \
	type CM_3DLUT_DATA0; \
	type CM_3DLUT_DATA1; \
	type CM_3DLUT_DATA_30BIT; \
	type CM_3DLUT_WRITE_EN_MASK; \
	type CM_3DLUT_RAM_SEL; \
	type CM_3DLUT_30BIT_EN; \
	type CM_3DLUT_CONFIG_STATUS; \
	type CM_3DLUT_READ_SEL; \
	type CM_SHAPER_LUT_MODE; \
	type CM_SHAPER_RAMB_EXP_REGION_START_B; \
	type CM_SHAPER_RAMB_EXP_REGION_START_SEGMENT_B; \
	type CM_SHAPER_RAMB_EXP_REGION_START_G; \
	type CM_SHAPER_RAMB_EXP_REGION_START_SEGMENT_G; \
	type CM_SHAPER_RAMB_EXP_REGION_START_R; \
	type CM_SHAPER_RAMB_EXP_REGION_START_SEGMENT_R; \
	type CM_SHAPER_RAMB_EXP_REGION_END_B; \
	type CM_SHAPER_RAMB_EXP_REGION_END_BASE_B; \
	type CM_SHAPER_RAMB_EXP_REGION_END_G; \
	type CM_SHAPER_RAMB_EXP_REGION_END_BASE_G; \
	type CM_SHAPER_RAMB_EXP_REGION_END_R; \
	type CM_SHAPER_RAMB_EXP_REGION_END_BASE_R; \
	type CM_SHAPER_RAMB_EXP_REGION0_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION0_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION1_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION1_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION2_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION2_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION3_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION3_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION4_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION4_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION5_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION5_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION6_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION6_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION7_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION7_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION8_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION8_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION9_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION9_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION10_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION10_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION11_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION11_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION12_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION12_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION13_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION13_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION14_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION14_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION15_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION15_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION16_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION16_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION17_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION17_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION18_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION18_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION19_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION19_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION20_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION20_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION21_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION21_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION22_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION22_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION23_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION23_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION24_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION24_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION25_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION25_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION26_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION26_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION27_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION27_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION28_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION28_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION29_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION29_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION30_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION30_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION31_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION31_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION32_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION32_NUM_SEGMENTS; \
	type CM_SHAPER_RAMB_EXP_REGION33_LUT_OFFSET; \
	type CM_SHAPER_RAMB_EXP_REGION33_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION_START_B; \
	type CM_SHAPER_RAMA_EXP_REGION_START_SEGMENT_B; \
	type CM_SHAPER_RAMA_EXP_REGION_START_G; \
	type CM_SHAPER_RAMA_EXP_REGION_START_SEGMENT_G; \
	type CM_SHAPER_RAMA_EXP_REGION_START_R; \
	type CM_SHAPER_RAMA_EXP_REGION_START_SEGMENT_R; \
	type CM_SHAPER_RAMA_EXP_REGION_END_B; \
	type CM_SHAPER_RAMA_EXP_REGION_END_BASE_B; \
	type CM_SHAPER_RAMA_EXP_REGION_END_G; \
	type CM_SHAPER_RAMA_EXP_REGION_END_BASE_G; \
	type CM_SHAPER_RAMA_EXP_REGION_END_R; \
	type CM_SHAPER_RAMA_EXP_REGION_END_BASE_R; \
	type CM_SHAPER_RAMA_EXP_REGION0_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION0_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION1_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION1_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION2_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION2_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION3_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION3_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION4_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION4_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION5_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION5_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION6_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION6_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION7_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION7_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION8_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION8_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION9_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION9_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION10_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION10_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION11_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION11_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION12_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION12_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION13_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION13_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION14_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION14_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION15_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION15_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION16_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION16_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION17_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION17_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION18_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION18_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION19_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION19_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION20_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION20_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION21_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION21_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION22_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION22_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION23_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION23_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION24_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION24_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION25_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION25_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION26_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION26_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION27_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION27_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION28_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION28_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION29_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION29_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION30_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION30_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION31_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION31_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION32_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION32_NUM_SEGMENTS; \
	type CM_SHAPER_RAMA_EXP_REGION33_LUT_OFFSET; \
	type CM_SHAPER_RAMA_EXP_REGION33_NUM_SEGMENTS; \
	type CM_SHAPER_LUT_WRITE_EN_MASK; \
	type CM_SHAPER_CONFIG_STATUS; \
	type CM_SHAPER_LUT_WRITE_SEL; \
	type CM_SHAPER_LUT_INDEX; \
	type CM_SHAPER_LUT_DATA; \
	type CM_DGAM_CONFIG_STATUS; \
	type CM_ICSC_MODE; \
	type CM_ICSC_C11; \
	type CM_ICSC_C12; \
	type CM_ICSC_C33; \
	type CM_ICSC_C34; \
	type CM_BNS_BIAS_R; \
	type CM_BNS_BIAS_G; \
	type CM_BNS_BIAS_B; \
	type CM_BNS_SCALE_R; \
	type CM_BNS_SCALE_G; \
	type CM_BNS_SCALE_B; \
	type CM_DGAM_RAMB_EXP_REGION_START_B; \
	type CM_DGAM_RAMB_EXP_REGION_START_SEGMENT_B; \
	type CM_DGAM_RAMB_EXP_REGION_START_G; \
	type CM_DGAM_RAMB_EXP_REGION_START_SEGMENT_G; \
	type CM_DGAM_RAMB_EXP_REGION_START_R; \
	type CM_DGAM_RAMB_EXP_REGION_START_SEGMENT_R; \
	type CM_DGAM_RAMB_EXP_REGION_LINEAR_SLOPE_B; \
	type CM_DGAM_RAMB_EXP_REGION_LINEAR_SLOPE_G; \
	type CM_DGAM_RAMB_EXP_REGION_LINEAR_SLOPE_R; \
	type CM_DGAM_RAMB_EXP_REGION_END_B; \
	type CM_DGAM_RAMB_EXP_REGION_END_SLOPE_B; \
	type CM_DGAM_RAMB_EXP_REGION_END_BASE_B; \
	type CM_DGAM_RAMB_EXP_REGION_END_G; \
	type CM_DGAM_RAMB_EXP_REGION_END_SLOPE_G; \
	type CM_DGAM_RAMB_EXP_REGION_END_BASE_G; \
	type CM_DGAM_RAMB_EXP_REGION_END_R; \
	type CM_DGAM_RAMB_EXP_REGION_END_SLOPE_R; \
	type CM_DGAM_RAMB_EXP_REGION_END_BASE_R; \
	type CM_DGAM_RAMB_EXP_REGION0_LUT_OFFSET; \
	type CM_DGAM_RAMB_EXP_REGION0_NUM_SEGMENTS; \
	type CM_DGAM_RAMB_EXP_REGION1_LUT_OFFSET; \
	type CM_DGAM_RAMB_EXP_REGION1_NUM_SEGMENTS; \
	type CM_DGAM_RAMB_EXP_REGION14_LUT_OFFSET; \
	type CM_DGAM_RAMB_EXP_REGION14_NUM_SEGMENTS; \
	type CM_DGAM_RAMB_EXP_REGION15_LUT_OFFSET; \
	type CM_DGAM_RAMB_EXP_REGION15_NUM_SEGMENTS; \
	type CM_DGAM_RAMA_EXP_REGION_START_B; \
	type CM_DGAM_RAMA_EXP_REGION_START_SEGMENT_B; \
	type CM_DGAM_RAMA_EXP_REGION_START_G; \
	type CM_DGAM_RAMA_EXP_REGION_START_SEGMENT_G; \
	type CM_DGAM_RAMA_EXP_REGION_START_R; \
	type CM_DGAM_RAMA_EXP_REGION_START_SEGMENT_R; \
	type CM_DGAM_RAMA_EXP_REGION_LINEAR_SLOPE_B; \
	type CM_DGAM_RAMA_EXP_REGION_LINEAR_SLOPE_G; \
	type CM_DGAM_RAMA_EXP_REGION_LINEAR_SLOPE_R; \
	type CM_DGAM_RAMA_EXP_REGION_END_B; \
	type CM_DGAM_RAMA_EXP_REGION_END_SLOPE_B; \
	type CM_DGAM_RAMA_EXP_REGION_END_BASE_B; \
	type CM_DGAM_RAMA_EXP_REGION_END_G; \
	type CM_DGAM_RAMA_EXP_REGION_END_SLOPE_G; \
	type CM_DGAM_RAMA_EXP_REGION_END_BASE_G; \
	type CM_DGAM_RAMA_EXP_REGION_END_R; \
	type CM_DGAM_RAMA_EXP_REGION_END_SLOPE_R; \
	type CM_DGAM_RAMA_EXP_REGION_END_BASE_R; \
	type CM_DGAM_RAMA_EXP_REGION0_LUT_OFFSET; \
	type CM_DGAM_RAMA_EXP_REGION0_NUM_SEGMENTS; \
	type CM_DGAM_RAMA_EXP_REGION1_LUT_OFFSET; \
	type CM_DGAM_RAMA_EXP_REGION1_NUM_SEGMENTS; \
	type CM_DGAM_RAMA_EXP_REGION14_LUT_OFFSET; \
	type CM_DGAM_RAMA_EXP_REGION14_NUM_SEGMENTS; \
	type CM_DGAM_RAMA_EXP_REGION15_LUT_OFFSET; \
	type CM_DGAM_RAMA_EXP_REGION15_NUM_SEGMENTS; \
	type SHARED_MEM_PWR_DIS; \
	type CM_IGAM_LUT_FORMAT_R; \
	type CM_IGAM_LUT_FORMAT_G; \
	type CM_IGAM_LUT_FORMAT_B; \
	type CM_IGAM_LUT_HOST_EN; \
	type CM_IGAM_LUT_RW_MODE; \
	type CM_IGAM_LUT_WRITE_EN_MASK; \
	type CM_IGAM_LUT_SEL; \
	type CM_IGAM_LUT_SEQ_COLOR; \
	type CM_IGAM_DGAM_CONFIG_STATUS; \
	type CM_DGAM_LUT_WRITE_EN_MASK; \
	type CM_DGAM_LUT_WRITE_SEL; \
	type CM_DGAM_LUT_INDEX; \
	type CM_DGAM_LUT_DATA; \
	type CM_DGAM_LUT_MODE; \
	type CM_IGAM_LUT_MODE; \
	type CM_IGAM_INPUT_FORMAT; \
	type CM_IGAM_LUT_RW_INDEX; \
	type CM_BYPASS_EN; \
	type FORMAT_EXPANSION_MODE; \
	type CNVC_BYPASS; \
	type OUTPUT_FP; \
	type CNVC_SURFACE_PIXEL_FORMAT; \
	type CURSOR_MODE; \
	type CURSOR_PITCH; \
	type CURSOR_LINES_PER_CHUNK; \
	type CURSOR_ENABLE; \
	type CUR0_MODE; \
	type CUR0_EXPANSION_MODE; \
	type CUR0_ENABLE; \
	type CM_BYPASS; \
	type CM_TEST_DEBUG_INDEX; \
	type CM_TEST_DEBUG_DATA_ID9_ICSC_MODE; \
	type CM_TEST_DEBUG_DATA_ID9_OCSC_MODE;\
	type FORMAT_CONTROL__ALPHA_EN; \
	type CUR0_COLOR0; \
	type CUR0_COLOR1; \
	type DPPCLK_RATE_CONTROL; \
	type DPP_CLOCK_ENABLE; \
	type CM_HDR_MULT_COEF; \
	type CUR0_FP_BIAS; \
	type CUR0_FP_SCALE;

struct dcn_dpp_shift {
	TF_REG_FIELD_LIST(uint8_t)
};

struct dcn_dpp_mask {
	TF_REG_FIELD_LIST(uint32_t)
};

#define DPP_COMMON_REG_VARIABLE_LIST \
	uint32_t DSCL_EXT_OVERSCAN_LEFT_RIGHT; \
	uint32_t DSCL_EXT_OVERSCAN_TOP_BOTTOM; \
	uint32_t OTG_H_BLANK; \
	uint32_t OTG_V_BLANK; \
	uint32_t SCL_MODE; \
	uint32_t LB_DATA_FORMAT; \
	uint32_t LB_MEMORY_CTRL; \
	uint32_t DSCL_AUTOCAL; \
	uint32_t SCL_BLACK_OFFSET; \
	uint32_t SCL_TAP_CONTROL; \
	uint32_t SCL_COEF_RAM_TAP_SELECT; \
	uint32_t SCL_COEF_RAM_TAP_DATA; \
	uint32_t DSCL_2TAP_CONTROL; \
	uint32_t MPC_SIZE; \
	uint32_t SCL_HORZ_FILTER_SCALE_RATIO; \
	uint32_t SCL_VERT_FILTER_SCALE_RATIO; \
	uint32_t SCL_HORZ_FILTER_SCALE_RATIO_C; \
	uint32_t SCL_VERT_FILTER_SCALE_RATIO_C; \
	uint32_t SCL_HORZ_FILTER_INIT; \
	uint32_t SCL_HORZ_FILTER_INIT_C; \
	uint32_t SCL_VERT_FILTER_INIT; \
	uint32_t SCL_VERT_FILTER_INIT_BOT; \
	uint32_t SCL_VERT_FILTER_INIT_C; \
	uint32_t SCL_VERT_FILTER_INIT_BOT_C; \
	uint32_t RECOUT_START; \
	uint32_t RECOUT_SIZE; \
	uint32_t CM_GAMUT_REMAP_CONTROL; \
	uint32_t CM_GAMUT_REMAP_C11_C12; \
	uint32_t CM_GAMUT_REMAP_C13_C14; \
	uint32_t CM_GAMUT_REMAP_C21_C22; \
	uint32_t CM_GAMUT_REMAP_C23_C24; \
	uint32_t CM_GAMUT_REMAP_C31_C32; \
	uint32_t CM_GAMUT_REMAP_C33_C34; \
	uint32_t CM_COMA_C11_C12; \
	uint32_t CM_COMA_C33_C34; \
	uint32_t CM_COMB_C11_C12; \
	uint32_t CM_COMB_C33_C34; \
	uint32_t CM_OCSC_CONTROL; \
	uint32_t CM_OCSC_C11_C12; \
	uint32_t CM_OCSC_C33_C34; \
	uint32_t CM_MEM_PWR_CTRL; \
	uint32_t CM_RGAM_LUT_DATA; \
	uint32_t CM_RGAM_LUT_WRITE_EN_MASK; \
	uint32_t CM_RGAM_LUT_INDEX; \
	uint32_t CM_RGAM_RAMB_START_CNTL_B; \
	uint32_t CM_RGAM_RAMB_START_CNTL_G; \
	uint32_t CM_RGAM_RAMB_START_CNTL_R; \
	uint32_t CM_RGAM_RAMB_SLOPE_CNTL_B; \
	uint32_t CM_RGAM_RAMB_SLOPE_CNTL_G; \
	uint32_t CM_RGAM_RAMB_SLOPE_CNTL_R; \
	uint32_t CM_RGAM_RAMB_END_CNTL1_B; \
	uint32_t CM_RGAM_RAMB_END_CNTL2_B; \
	uint32_t CM_RGAM_RAMB_END_CNTL1_G; \
	uint32_t CM_RGAM_RAMB_END_CNTL2_G; \
	uint32_t CM_RGAM_RAMB_END_CNTL1_R; \
	uint32_t CM_RGAM_RAMB_END_CNTL2_R; \
	uint32_t CM_RGAM_RAMB_REGION_0_1; \
	uint32_t CM_RGAM_RAMB_REGION_32_33; \
	uint32_t CM_RGAM_RAMA_START_CNTL_B; \
	uint32_t CM_RGAM_RAMA_START_CNTL_G; \
	uint32_t CM_RGAM_RAMA_START_CNTL_R; \
	uint32_t CM_RGAM_RAMA_SLOPE_CNTL_B; \
	uint32_t CM_RGAM_RAMA_SLOPE_CNTL_G; \
	uint32_t CM_RGAM_RAMA_SLOPE_CNTL_R; \
	uint32_t CM_RGAM_RAMA_END_CNTL1_B; \
	uint32_t CM_RGAM_RAMA_END_CNTL2_B; \
	uint32_t CM_RGAM_RAMA_END_CNTL1_G; \
	uint32_t CM_RGAM_RAMA_END_CNTL2_G; \
	uint32_t CM_RGAM_RAMA_END_CNTL1_R; \
	uint32_t CM_RGAM_RAMA_END_CNTL2_R; \
	uint32_t CM_RGAM_RAMA_REGION_0_1; \
	uint32_t CM_RGAM_RAMA_REGION_32_33; \
	uint32_t CM_RGAM_CONTROL; \
	uint32_t CM_CMOUT_CONTROL; \
	uint32_t CM_BLNDGAM_LUT_WRITE_EN_MASK; \
	uint32_t CM_BLNDGAM_CONTROL; \
	uint32_t CM_BLNDGAM_RAMB_START_CNTL_B; \
	uint32_t CM_BLNDGAM_RAMB_START_CNTL_G; \
	uint32_t CM_BLNDGAM_RAMB_START_CNTL_R; \
	uint32_t CM_BLNDGAM_RAMB_SLOPE_CNTL_B; \
	uint32_t CM_BLNDGAM_RAMB_SLOPE_CNTL_G; \
	uint32_t CM_BLNDGAM_RAMB_SLOPE_CNTL_R; \
	uint32_t CM_BLNDGAM_RAMB_END_CNTL1_B; \
	uint32_t CM_BLNDGAM_RAMB_END_CNTL2_B; \
	uint32_t CM_BLNDGAM_RAMB_END_CNTL1_G; \
	uint32_t CM_BLNDGAM_RAMB_END_CNTL2_G; \
	uint32_t CM_BLNDGAM_RAMB_END_CNTL1_R; \
	uint32_t CM_BLNDGAM_RAMB_END_CNTL2_R; \
	uint32_t CM_BLNDGAM_RAMB_REGION_0_1; \
	uint32_t CM_BLNDGAM_RAMB_REGION_2_3; \
	uint32_t CM_BLNDGAM_RAMB_REGION_4_5; \
	uint32_t CM_BLNDGAM_RAMB_REGION_6_7; \
	uint32_t CM_BLNDGAM_RAMB_REGION_8_9; \
	uint32_t CM_BLNDGAM_RAMB_REGION_10_11; \
	uint32_t CM_BLNDGAM_RAMB_REGION_12_13; \
	uint32_t CM_BLNDGAM_RAMB_REGION_14_15; \
	uint32_t CM_BLNDGAM_RAMB_REGION_16_17; \
	uint32_t CM_BLNDGAM_RAMB_REGION_18_19; \
	uint32_t CM_BLNDGAM_RAMB_REGION_20_21; \
	uint32_t CM_BLNDGAM_RAMB_REGION_22_23; \
	uint32_t CM_BLNDGAM_RAMB_REGION_24_25; \
	uint32_t CM_BLNDGAM_RAMB_REGION_26_27; \
	uint32_t CM_BLNDGAM_RAMB_REGION_28_29; \
	uint32_t CM_BLNDGAM_RAMB_REGION_30_31; \
	uint32_t CM_BLNDGAM_RAMB_REGION_32_33; \
	uint32_t CM_BLNDGAM_RAMA_START_CNTL_B; \
	uint32_t CM_BLNDGAM_RAMA_START_CNTL_G; \
	uint32_t CM_BLNDGAM_RAMA_START_CNTL_R; \
	uint32_t CM_BLNDGAM_RAMA_SLOPE_CNTL_B; \
	uint32_t CM_BLNDGAM_RAMA_SLOPE_CNTL_G; \
	uint32_t CM_BLNDGAM_RAMA_SLOPE_CNTL_R; \
	uint32_t CM_BLNDGAM_RAMA_END_CNTL1_B; \
	uint32_t CM_BLNDGAM_RAMA_END_CNTL2_B; \
	uint32_t CM_BLNDGAM_RAMA_END_CNTL1_G; \
	uint32_t CM_BLNDGAM_RAMA_END_CNTL2_G; \
	uint32_t CM_BLNDGAM_RAMA_END_CNTL1_R; \
	uint32_t CM_BLNDGAM_RAMA_END_CNTL2_R; \
	uint32_t CM_BLNDGAM_RAMA_REGION_0_1; \
	uint32_t CM_BLNDGAM_RAMA_REGION_2_3; \
	uint32_t CM_BLNDGAM_RAMA_REGION_4_5; \
	uint32_t CM_BLNDGAM_RAMA_REGION_6_7; \
	uint32_t CM_BLNDGAM_RAMA_REGION_8_9; \
	uint32_t CM_BLNDGAM_RAMA_REGION_10_11; \
	uint32_t CM_BLNDGAM_RAMA_REGION_12_13; \
	uint32_t CM_BLNDGAM_RAMA_REGION_14_15; \
	uint32_t CM_BLNDGAM_RAMA_REGION_16_17; \
	uint32_t CM_BLNDGAM_RAMA_REGION_18_19; \
	uint32_t CM_BLNDGAM_RAMA_REGION_20_21; \
	uint32_t CM_BLNDGAM_RAMA_REGION_22_23; \
	uint32_t CM_BLNDGAM_RAMA_REGION_24_25; \
	uint32_t CM_BLNDGAM_RAMA_REGION_26_27; \
	uint32_t CM_BLNDGAM_RAMA_REGION_28_29; \
	uint32_t CM_BLNDGAM_RAMA_REGION_30_31; \
	uint32_t CM_BLNDGAM_RAMA_REGION_32_33; \
	uint32_t CM_BLNDGAM_LUT_INDEX; \
	uint32_t CM_3DLUT_MODE; \
	uint32_t CM_3DLUT_INDEX; \
	uint32_t CM_3DLUT_DATA; \
	uint32_t CM_3DLUT_DATA_30BIT; \
	uint32_t CM_3DLUT_READ_WRITE_CONTROL; \
	uint32_t CM_SHAPER_LUT_WRITE_EN_MASK; \
	uint32_t CM_SHAPER_CONTROL; \
	uint32_t CM_SHAPER_RAMB_START_CNTL_B; \
	uint32_t CM_SHAPER_RAMB_START_CNTL_G; \
	uint32_t CM_SHAPER_RAMB_START_CNTL_R; \
	uint32_t CM_SHAPER_RAMB_END_CNTL_B; \
	uint32_t CM_SHAPER_RAMB_END_CNTL_G; \
	uint32_t CM_SHAPER_RAMB_END_CNTL_R; \
	uint32_t CM_SHAPER_RAMB_REGION_0_1; \
	uint32_t CM_SHAPER_RAMB_REGION_2_3; \
	uint32_t CM_SHAPER_RAMB_REGION_4_5; \
	uint32_t CM_SHAPER_RAMB_REGION_6_7; \
	uint32_t CM_SHAPER_RAMB_REGION_8_9; \
	uint32_t CM_SHAPER_RAMB_REGION_10_11; \
	uint32_t CM_SHAPER_RAMB_REGION_12_13; \
	uint32_t CM_SHAPER_RAMB_REGION_14_15; \
	uint32_t CM_SHAPER_RAMB_REGION_16_17; \
	uint32_t CM_SHAPER_RAMB_REGION_18_19; \
	uint32_t CM_SHAPER_RAMB_REGION_20_21; \
	uint32_t CM_SHAPER_RAMB_REGION_22_23; \
	uint32_t CM_SHAPER_RAMB_REGION_24_25; \
	uint32_t CM_SHAPER_RAMB_REGION_26_27; \
	uint32_t CM_SHAPER_RAMB_REGION_28_29; \
	uint32_t CM_SHAPER_RAMB_REGION_30_31; \
	uint32_t CM_SHAPER_RAMB_REGION_32_33; \
	uint32_t CM_SHAPER_RAMA_START_CNTL_B; \
	uint32_t CM_SHAPER_RAMA_START_CNTL_G; \
	uint32_t CM_SHAPER_RAMA_START_CNTL_R; \
	uint32_t CM_SHAPER_RAMA_END_CNTL_B; \
	uint32_t CM_SHAPER_RAMA_END_CNTL_G; \
	uint32_t CM_SHAPER_RAMA_END_CNTL_R; \
	uint32_t CM_SHAPER_RAMA_REGION_0_1; \
	uint32_t CM_SHAPER_RAMA_REGION_2_3; \
	uint32_t CM_SHAPER_RAMA_REGION_4_5; \
	uint32_t CM_SHAPER_RAMA_REGION_6_7; \
	uint32_t CM_SHAPER_RAMA_REGION_8_9; \
	uint32_t CM_SHAPER_RAMA_REGION_10_11; \
	uint32_t CM_SHAPER_RAMA_REGION_12_13; \
	uint32_t CM_SHAPER_RAMA_REGION_14_15; \
	uint32_t CM_SHAPER_RAMA_REGION_16_17; \
	uint32_t CM_SHAPER_RAMA_REGION_18_19; \
	uint32_t CM_SHAPER_RAMA_REGION_20_21; \
	uint32_t CM_SHAPER_RAMA_REGION_22_23; \
	uint32_t CM_SHAPER_RAMA_REGION_24_25; \
	uint32_t CM_SHAPER_RAMA_REGION_26_27; \
	uint32_t CM_SHAPER_RAMA_REGION_28_29; \
	uint32_t CM_SHAPER_RAMA_REGION_30_31; \
	uint32_t CM_SHAPER_RAMA_REGION_32_33; \
	uint32_t CM_SHAPER_LUT_INDEX; \
	uint32_t CM_SHAPER_LUT_DATA; \
	uint32_t CM_ICSC_CONTROL; \
	uint32_t CM_ICSC_C11_C12; \
	uint32_t CM_ICSC_C33_C34; \
	uint32_t CM_BNS_VALUES_R; \
	uint32_t CM_BNS_VALUES_G; \
	uint32_t CM_BNS_VALUES_B; \
	uint32_t CM_DGAM_RAMB_START_CNTL_B; \
	uint32_t CM_DGAM_RAMB_START_CNTL_G; \
	uint32_t CM_DGAM_RAMB_START_CNTL_R; \
	uint32_t CM_DGAM_RAMB_SLOPE_CNTL_B; \
	uint32_t CM_DGAM_RAMB_SLOPE_CNTL_G; \
	uint32_t CM_DGAM_RAMB_SLOPE_CNTL_R; \
	uint32_t CM_DGAM_RAMB_END_CNTL1_B; \
	uint32_t CM_DGAM_RAMB_END_CNTL2_B; \
	uint32_t CM_DGAM_RAMB_END_CNTL1_G; \
	uint32_t CM_DGAM_RAMB_END_CNTL2_G; \
	uint32_t CM_DGAM_RAMB_END_CNTL1_R; \
	uint32_t CM_DGAM_RAMB_END_CNTL2_R; \
	uint32_t CM_DGAM_RAMB_REGION_0_1; \
	uint32_t CM_DGAM_RAMB_REGION_14_15; \
	uint32_t CM_DGAM_RAMA_START_CNTL_B; \
	uint32_t CM_DGAM_RAMA_START_CNTL_G; \
	uint32_t CM_DGAM_RAMA_START_CNTL_R; \
	uint32_t CM_DGAM_RAMA_SLOPE_CNTL_B; \
	uint32_t CM_DGAM_RAMA_SLOPE_CNTL_G; \
	uint32_t CM_DGAM_RAMA_SLOPE_CNTL_R; \
	uint32_t CM_DGAM_RAMA_END_CNTL1_B; \
	uint32_t CM_DGAM_RAMA_END_CNTL2_B; \
	uint32_t CM_DGAM_RAMA_END_CNTL1_G; \
	uint32_t CM_DGAM_RAMA_END_CNTL2_G; \
	uint32_t CM_DGAM_RAMA_END_CNTL1_R; \
	uint32_t CM_DGAM_RAMA_END_CNTL2_R; \
	uint32_t CM_DGAM_RAMA_REGION_0_1; \
	uint32_t CM_DGAM_RAMA_REGION_14_15; \
	uint32_t CM_DGAM_LUT_WRITE_EN_MASK; \
	uint32_t CM_DGAM_LUT_INDEX; \
	uint32_t CM_DGAM_LUT_DATA; \
	uint32_t CM_CONTROL; \
	uint32_t CM_DGAM_CONTROL; \
	uint32_t CM_IGAM_CONTROL; \
	uint32_t CM_IGAM_LUT_RW_CONTROL; \
	uint32_t CM_IGAM_LUT_RW_INDEX; \
	uint32_t CM_IGAM_LUT_SEQ_COLOR; \
	uint32_t CM_TEST_DEBUG_INDEX; \
	uint32_t CM_TEST_DEBUG_DATA; \
	uint32_t FORMAT_CONTROL; \
	uint32_t CNVC_SURFACE_PIXEL_FORMAT; \
	uint32_t CURSOR_CONTROL; \
	uint32_t CURSOR0_CONTROL; \
	uint32_t CURSOR0_COLOR0; \
	uint32_t CURSOR0_COLOR1; \
	uint32_t DPP_CONTROL; \
	uint32_t CM_HDR_MULT_COEF; \
	uint32_t CURSOR0_FP_SCALE_BIAS;

struct dcn_dpp_registers {
	DPP_COMMON_REG_VARIABLE_LIST
};

struct dcn10_dpp {
	struct dpp base;

	const struct dcn_dpp_registers *tf_regs;
	const struct dcn_dpp_shift *tf_shift;
	const struct dcn_dpp_mask *tf_mask;

	const uint16_t *filter_v;
	const uint16_t *filter_h;
	const uint16_t *filter_v_c;
	const uint16_t *filter_h_c;
	int lb_pixel_depth_supported;
	int lb_memory_size;
	int lb_bits_per_entry;
	bool is_write_to_ram_a_safe;
	struct scaler_data scl_data;
	struct pwl_params pwl_data;
};

enum dcn10_input_csc_select {
	INPUT_CSC_SELECT_BYPASS = 0,
	INPUT_CSC_SELECT_ICSC = 1,
	INPUT_CSC_SELECT_COMA = 2
};

void dpp1_set_cursor_attributes(
		struct dpp *dpp_base,
		struct dc_cursor_attributes *cursor_attributes);

void dpp1_set_cursor_position(
		struct dpp *dpp_base,
		const struct dc_cursor_position *pos,
		const struct dc_cursor_mi_param *param,
		uint32_t width,
		uint32_t height);

void dpp1_cnv_set_optional_cursor_attributes(
			struct dpp *dpp_base,
			struct dpp_cursor_attributes *attr);

bool dpp1_dscl_is_lb_conf_valid(
		int ceil_vratio,
		int num_partitions,
		int vtaps);

void dpp1_dscl_calc_lb_num_partitions(
		const struct scaler_data *scl_data,
		enum lb_memory_config lb_config,
		int *num_part_y,
		int *num_part_c);

void dpp1_degamma_ram_select(
		struct dpp *dpp_base,
							bool use_ram_a);

void dpp1_program_degamma_luta_settings(
		struct dpp *dpp_base,
		const struct pwl_params *params);

void dpp1_program_degamma_lutb_settings(
		struct dpp *dpp_base,
		const struct pwl_params *params);

void dpp1_program_degamma_lut(
		struct dpp *dpp_base,
		const struct pwl_result_data *rgb,
		uint32_t num,
		bool is_ram_a);

void dpp1_power_on_degamma_lut(
		struct dpp *dpp_base,
	bool power_on);

void dpp1_program_input_csc(
		struct dpp *dpp_base,
		enum dc_color_space color_space,
		enum dcn10_input_csc_select select,
		const struct out_csc_color_matrix *tbl_entry);

void dpp1_program_bias_and_scale(
		struct dpp *dpp_base,
		struct dc_bias_and_scale *params);

void dpp1_program_input_lut(
		struct dpp *dpp_base,
		const struct dc_gamma *gamma);

void dpp1_full_bypass(struct dpp *dpp_base);

void dpp1_set_degamma(
		struct dpp *dpp_base,
		enum ipp_degamma_mode mode);

void dpp1_set_degamma_pwl(struct dpp *dpp_base,
		const struct pwl_params *params);


void dpp_read_state(struct dpp *dpp_base,
		struct dcn_dpp_state *s);

void dpp_reset(struct dpp *dpp_base);

void dpp1_cm_program_regamma_lut(
		struct dpp *dpp_base,
		const struct pwl_result_data *rgb,
		uint32_t num);

void dpp1_cm_power_on_regamma_lut(
	struct dpp *dpp_base,
	bool power_on);

void dpp1_cm_configure_regamma_lut(
		struct dpp *dpp_base,
		bool is_ram_a);

/*program re gamma RAM A*/
void dpp1_cm_program_regamma_luta_settings(
		struct dpp *dpp_base,
		const struct pwl_params *params);

/*program re gamma RAM B*/
void dpp1_cm_program_regamma_lutb_settings(
		struct dpp *dpp_base,
		const struct pwl_params *params);
void dpp1_cm_set_output_csc_adjustment(
		struct dpp *dpp_base,
		const uint16_t *regval);

void dpp1_cm_set_output_csc_default(
		struct dpp *dpp_base,
		enum dc_color_space colorspace);

void dpp1_cm_set_gamut_remap(
	struct dpp *dpp,
	const struct dpp_grph_csc_adjustment *adjust);

void dpp1_dscl_set_scaler_manual_scale(
	struct dpp *dpp_base,
	const struct scaler_data *scl_data);

void dpp1_cnv_setup (
		struct dpp *dpp_base,
		enum surface_pixel_format format,
		enum expansion_mode mode,
		struct dc_csc_transform input_csc_color_matrix,
		enum dc_color_space input_color_space,
		struct cnv_alpha_2bit_lut *alpha_2bit_lut);

void dpp1_full_bypass(struct dpp *dpp_base);

void dpp1_dppclk_control(
		struct dpp *dpp_base,
		bool dppclk_div,
		bool enable);

void dpp1_set_hdr_multiplier(
		struct dpp *dpp_base,
		uint32_t multiplier);

bool dpp1_get_optimal_number_of_taps(
		struct dpp *dpp,
		struct scaler_data *scl_data,
		const struct scaling_taps *in_taps);

void dpp1_construct(struct dcn10_dpp *dpp1,
	struct dc_context *ctx,
	uint32_t inst,
	const struct dcn_dpp_registers *tf_regs,
	const struct dcn_dpp_shift *tf_shift,
	const struct dcn_dpp_mask *tf_mask);
#endif
