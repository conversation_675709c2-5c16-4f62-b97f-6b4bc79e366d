#include "head.h"

// 客户端发布主题
#define SUB "QtToUbuntu"
#define PUB "UbuntuToQt"

#define IP "***********"
#define PORT 11221

extern char data[1024];//存储订阅到的数据
extern int demo_data;//存储解析后的数据
int msgid;//消息队列ID
int new;//通信套接字


// /* The cJSON structure: */
// typedef struct cJSON {
// 	struct cJSON *next,*prev;	/* next/prev allow you to walk array/object chains. Alternatively, use GetArraySize/GetArrayItem/GetObjectItem */
// 	struct cJSON *child;		/* An array or object item will have a child pointer pointing to a chain of the items in the array/object. */

// 	int type;					/* The type of the item, as above. */

// 	char *valuestring;			字符串类型的数据
// 	int valueint;				整形的数据
// 	double valuedouble;			浮点型的数据

// 	char *string;				/* The item's name string, if this item is the child of, or is in the list of subitems of an object. */
// } cJSON;


// 订阅线程处理函数
void *sub_handler(void *arg)
{
	// 1、定义信息结构体
	message_data mes;
	printf("1.............\n");

	while (1){
 	// 2、循环订阅上位机发布的信息
	 	mqtt_subscribe(SUB);//订阅主题
 	// 3、解析json格式的字符串数据，转化为cjson的结构体

	 	cJSON *root = cJSON_Parse(data);//将json格式的字符串转化为cjson格式的结构体
	 	//上位机发送来得数据有三种：
		//json["userpass"] = userpass;
		//json["temppass"] = temppass;
		//json["open_door"] = 1；
		//userpass、temppass都是类型
		if (root != NULL) {
 			//如果获取的键值对是userpass
			if (cJSON_GetObjectItem(root, "userpass") != NULL) {
				//获取userpass的值
				mes.type = USER_PASS;
				mes.data = cJSON_GetObjectItem(root, "userpass")->valueint;
				mes.len = sizeof(mes.data);
				//将数据发送到消息队列
				msgsnd(msgid, &mes, sizeof(mes), 0);
				printf("userpass\n");
				//清空root中的数据
				
			}
			//如果获取的键值对是temppass
			if (cJSON_GetObjectItem(root, "temppass") != NULL) {
				//获取temppass的值
				mes.type = TEMP_PASS;
				mes.data = cJSON_GetObjectItem(root, "temppass")->valueint;
				mes.len = sizeof(mes.data);
				//将数据发送到消息队列
				msgsnd(msgid, &mes, sizeof(mes), 0);
				printf("send temppass\n");
			}	
			//如果获取的键值对是open_door
			if (cJSON_GetObjectItem(root, "open_door") != NULL) {
				//获取open_door的值
				mes.type = OPEN_DOOR;
				mes.data = 1;
				mes.len = sizeof(mes.data);
				//将数据发送到消息队列
				msgsnd(msgid, &mes, sizeof(mes), 0);
				printf("send open_door\n");
			}
	 		// 6、释放使用完的空间
			cJSON_Delete(root);
			memset(&mes,0,sizeof(mes));
	
		 sleep(1);
		}

	}
}

// 发布线程处理函数
void *pub_handler(void *arg)
{
	 float tem;
	 while (1) {
	 	// 接收消息队列中的数据
	 	msgrcv(msgid, &tem, sizeof(tem), 1, 0);

	 	// 创建对象
	 	cJSON *root = cJSON_CreateObject();

	 	// 将接收到的信息填充到cjson对象上
	 	cJSON_AddNumberToObject(root, "tem", tem);

	 	// 生成JSON字符串
	 	char *json_str = cJSON_PrintUnformatted(root);

	 	// 发布消息
	 	mqtt_publish(PUB, json_str);
 	// 使用完释放空间，避免内存泄漏
	 	free(json_str);
	 	cJSON_Delete(root);

	 	sleep(1);
	 }
}

	

// 将从消息队列中读取出来的数据进行处理后返回给下位来操作某些功能
void *send_low(void *arg)
{
	 message_data mes;
	 while (1)
	 {
		//清空结构体
		bzero(&mes, sizeof(mes));
		//从消息队列中读取数据
	 	msgrcv(msgid, &mes, sizeof(mes), 100, 0);
		//将mes结构体数据转换成字节数据发送给下位机
		//1、将mes结构体数据转换成字节数据
		uint8_t buf[1024];
		memset(buf, 0, sizeof(buf));
		memcpy(buf, &mes, sizeof(mes));
		//2、发送数据
		if(send(new, &buf, sizeof(buf), 0) == -1){
			perror("send error");
			break;
		}
	 	sleep(1);
	 }
}

// 主函数
int main(int argc, char *argv[])
{
	int ret;//存储函数返回值
	pthread_t tid1, tid2, tid3;//存储线程ID
	// 创建消息队列
	key_t key = ftok("/", 'k');
	if ((msgid = msgget(key, IPC_CREAT | 0664)) == -1)
	{
		perror("msgget error");
		return -1;
	}
	// mqtt初始化
	if (0 != mqtt_init())
	{
		puts("init_mqtt err");
		return -1;
	}
	pid_t pid = fork();
	if (pid > 0) // 父进程，用于与上位机通信
	{
		// 开启消息发布线程
		pthread_create(&tid1, NULL, pub_handler, NULL);

		// 开启消息订阅线程
		pthread_create(&tid2, NULL, sub_handler, NULL);

		while (1)
			;
	}
	else if (pid == 0){//子进程，用于与下位机通信
		//1、创建用于接受连接的套接字
		int sfd = socket(AF_INET, SOCK_STREAM, 0);//AF_INET:ipv4  SOCK_STREAM:tcp
		if (sfd == -1)
	 	{
	 		perror("socket error");
	 		return -1;
	 	}
	 	//设置端口快速重用,重用就是在服务器断开连接后，可以立即重启服务器
	 	int reuse = 1;
	
	 	if (setsockopt(sfd, SOL_SOCKET, SO_REUSEADDR, &reuse, sizeof(reuse)) == -1)
	 	{
	 		perror("setsockopt error");
	 		return -1;
	 	}
		//__LINE__, __FILE__, __func__是预定义的宏，分别表示行号、文件名、函数名
	 	printf("设置端口快速重用成功 _%d_ %s_ %s_\n", __LINE__, __FILE__, __func__);
	 	// 2、绑定IP地址和端口号
	 	// 2.1、填充要绑定的地址信息结构体
	 	struct sockaddr_in sin;
	 	sin.sin_family = AF_INET;							// 表明是ipv4
	 	sin.sin_port = htons(PORT);							// 端口号
	 	sin.sin_addr.s_addr = inet_addr(IP); // IP地址

	 	// 2.2、绑定
	 	if (bind(sfd, (struct sockaddr *)&sin, sizeof(sin)) == -1)
	 	{
	 		perror("bind error");
	 		return -1;
	 	}
	 	// 3、将套接字设置成被动监听状态
	 	if (listen(sfd, 128) == -1)
	 	{
	 		perror("listen error");
	 		return -1;
	 	}
	 	// 4、阻塞等待客户端连接请求，如果有新的客户端连接，则创建一个新的用于通信的套接字
	 	// 4.1、定义客户端地址信息结构体
	 	struct sockaddr_in cin; // 客户端地址信息结构体
	 	cin.sin_family = AF_INET;
	 	socklen_t socklen = sizeof(cin); // 客户端地址信息的大小

	 	// 4.2、阻塞接收客户端的连接请求，并且获取客户端的地址信息
	 	int newfd = accept(sfd, (struct sockaddr *)&cin, &socklen);
	 	if (newfd == -1)
	 	{
	 		perror("accept error");
	 		return -1;
			
	 	}
		printf("success.\n");
	 	new = newfd;
	 	pthread_create(&tid3, NULL, send_low, NULL);
	 	// 5、收发数据使用newfd完成通信
	 	message_data mes = {.type = 1};

	 	while (1)
	 	{
	 		
	 	
	 	
			uint8_t Recvbuf[1024];
			memset(Recvbuf, 0, sizeof(Recvbuf));
	 		// 5.1、接收数据
			 ssize_t bytes_read = read(new, Recvbuf, sizeof(Recvbuf));
			 if (bytes_read <= 0) {
				 if (bytes_read == 0) {
					 //打印具体哪个客户端断开连接
					 printf("Client disconnected\n");	 
				 } else {
					 perror("read error");
				 }
				 break;
			 }
			 //将接收到的数据转换成float打印出来
			 //解析Recvbuf的小端序数据
			 float tem;
			 uint32_t tem_raw = (uint32_t)Recvbuf[0] | 
										(uint32_t)Recvbuf[1] << 8 | 
										(uint32_t)Recvbuf[2] << 16 | 
										(uint32_t)Recvbuf[3] << 24;
			 memcpy(&tem, &tem_raw, sizeof(float));
			 printf("tem: %f\n", tem);

	 		// 将数据存到消息队列中
			msgsnd(msgid, &tem, sizeof(tem), 0);
	 	}
	 	// 6、关闭所有套接字
	 	close(sfd);	  // 关闭监听
	 	close(newfd); // 关闭通信的套接字
	 }else {
	 	printf("与下位机连接失败\n");
	 }

	return 0;
}
